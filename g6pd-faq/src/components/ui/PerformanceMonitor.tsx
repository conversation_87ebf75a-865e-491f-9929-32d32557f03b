'use client'

import { useEffect } from 'react'

export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and on client side
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'production') {
      return
    }

    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const metricName = entry.name
        const value = Math.round(entry.value)
        
        // Log performance metrics (in production, you might send to analytics)
        console.log(`${metricName}: ${value}ms`)
        
        // You can send these metrics to your analytics service
        // analytics.track('web-vital', { metric: metricName, value })
      }
    })

    // Observe Core Web Vitals
    try {
      observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] })
    } catch (e) {
      // Fallback for browsers that don't support all entry types
      console.warn('Performance monitoring not fully supported')
    }

    // Monitor LCP (Largest Contentful Paint)
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      console.log('LCP:', Math.round(lastEntry.startTime))
    })

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      console.warn('LCP monitoring not supported')
    }

    // Monitor CLS (Cumulative Layout Shift)
    let clsValue = 0
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      }
      console.log('CLS:', clsValue)
    })

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (e) {
      console.warn('CLS monitoring not supported')
    }

    return () => {
      observer.disconnect()
      lcpObserver.disconnect()
      clsObserver.disconnect()
    }
  }, [])

  return null
}
