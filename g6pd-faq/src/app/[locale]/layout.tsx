import Navigation from '@/components/layout/Navigation'
import Footer from '@/components/layout/Footer'

const locales = ['zh', 'en']

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const siteTitle = locale === 'zh' ? 'G6PD缺乏症（蚕豆病）FAQ' : 'G6PD Deficiency FAQ'
  const siteDescription = locale === 'zh'
    ? '为G6PD缺乏症患者及家属提供权威、全面的FAQ信息，包括用药指导、饮食建议、症状识别等专业内容。'
    : 'Comprehensive FAQ for G6PD deficiency patients and families'

  return {
    title: {
      template: `%s | ${siteTitle}`,
      default: siteTitle
    },
    description: siteDescription,
    openGraph: {
      title: siteTitle,
      description: siteDescription,
      type: 'website',
      locale: locale,
      alternateLocale: locales.filter(l => l !== locale)
    },
    alternates: {
      languages: {
        'zh': '/zh',
        'en': '/en'
      }
    }
  }
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params

  return (
    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'}>
      <body>
        <div className="min-h-screen flex flex-col">
          <Navigation locale={locale} />
          <main className="flex-1">
            {children}
          </main>
          <Footer locale={locale} />
        </div>
      </body>
    </html>
  )
}
