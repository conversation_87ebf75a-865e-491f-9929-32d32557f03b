import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { Metadata } from 'next'
import {
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  BookOpenIcon,
  BeakerIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale })

  return {
    title: locale === 'zh' ? '用药指导 - G6PD缺乏症FAQ' : 'Medication Guide - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '为G6PD缺乏症患者提供详细的用药指导，包括禁用药物清单、中药注意事项、口服液安全指南等。'
      : 'Comprehensive medication guide for G6PD deficiency patients, including prohibited drugs, Chinese medicine precautions, and oral solution safety guidelines.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,用药指导,禁用药物,中药,口服液,药物安全'
      : 'G6PD deficiency,medication guide,prohibited drugs,Chinese medicine,oral solutions,drug safety'
  }
}

export default async function MedicationsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const t = await getTranslations({ locale })

  const medicationCategories = [
    {
      title: locale === 'zh' ? '中药相关' : 'Chinese Medicine',
      href: `/${locale}/medications/chinese-medicine`,
      icon: BookOpenIcon,
      description: locale === 'zh' ? '中药使用注意事项和禁忌' : 'Chinese medicine precautions and contraindications',
      count: '50+',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: locale === 'zh' ? '口服液相关' : 'Oral Solutions',
      href: `/${locale}/medications/oral-solutions`,
      icon: BeakerIcon,
      description: locale === 'zh' ? '口服液安全指导和建议' : 'Oral solution safety guidelines and recommendations',
      count: '30+',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: locale === 'zh' ? '西药相关' : 'Western Medicine',
      href: `/${locale}/medications/western-medicine`,
      icon: ShieldCheckIcon,
      description: locale === 'zh' ? '西药禁忌和安全用药' : 'Western medicine contraindications and safe usage',
      count: '40+',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    }
  ]

  const commonQuestions = [
    {
      question: locale === 'zh' ? 'G6PD缺乏症患者可以服用阿司匹林吗？' : 'Can G6PD deficient patients take aspirin?',
      href: `/${locale}/faq/aspirin-safety`
    },
    {
      question: locale === 'zh' ? '哪些中药成分需要避免？' : 'Which Chinese medicine ingredients should be avoided?',
      href: `/${locale}/faq/chinese-medicine-ingredients`
    },
    {
      question: locale === 'zh' ? '儿童口服液选择注意事项' : 'Considerations for choosing oral solutions for children',
      href: `/${locale}/faq/children-oral-solutions`
    },
    {
      question: locale === 'zh' ? '如何判断药物是否安全？' : 'How to determine if a medication is safe?',
      href: `/${locale}/faq/medication-safety-check`
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {locale === 'zh' ? '用药指导' : 'Medication Guide'}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
              {locale === 'zh' 
                ? '为G6PD缺乏症患者提供专业、安全的用药指导'
                : 'Professional and safe medication guidance for G6PD deficiency patients'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Emergency Alert */}
      <section className="bg-red-50 border-b border-red-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-center">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-3" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-red-800">
                {locale === 'zh' ? '重要提醒' : 'Important Notice'}
              </h3>
              <p className="text-red-700 mt-1">
                {locale === 'zh' 
                  ? '服用任何新药物前，请务必咨询医生或药师！'
                  : 'Always consult a doctor or pharmacist before taking any new medication!'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Medication Categories */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '药物分类' : 'Medication Categories'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' 
                ? '按类别查看详细的用药指导信息'
                : 'View detailed medication guidance by category'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {medicationCategories.map((category, index) => (
              <Link
                key={index}
                href={category.href}
                className={`group p-6 bg-white rounded-lg border ${category.borderColor} hover:shadow-lg transition-all`}
              >
                <div className="flex items-center justify-between mb-4">
                  <category.icon className={`h-8 w-8 ${category.color}`} />
                  <span className={`text-sm font-semibold ${category.color} ${category.bgColor} px-2 py-1 rounded`}>
                    {category.count}
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-700">
                  {category.title}
                </h3>
                <p className="text-gray-600">
                  {category.description}
                </p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Common Questions */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '常见用药问题' : 'Common Medication Questions'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' 
                ? '患者最关心的用药安全问题'
                : 'Most concerning medication safety questions for patients'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {commonQuestions.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <h3 className="text-lg font-medium text-gray-900 hover:text-blue-700">
                  {item.question}
                </h3>
              </Link>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href={`/${locale}/faq`}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              {locale === 'zh' ? '查看所有问题' : 'View All Questions'}
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
