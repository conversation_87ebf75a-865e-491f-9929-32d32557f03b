import { Metadata } from 'next'
import Link from 'next/link'
import {
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params

  return {
    title: locale === 'zh' ? '中药用药指导 - G6PD缺乏症FAQ' : 'Chinese Medicine Guide - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '详细的G6PD缺乏症患者中药用药指导，包括禁用中药清单、安全中药推荐和用药注意事项。'
      : 'Comprehensive Chinese medicine guide for G6PD deficiency patients, including prohibited herbs, safe medications, and usage precautions.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,中药,禁用中药,安全用药,中药清单'
      : 'G6PD deficiency,Chinese medicine,prohibited herbs,safe medication,TCM list'
  }
}

export default async function ChineseMedicinePage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params

  const prohibitedHerbs = [
    {
      category: locale === 'zh' ? '矿物类中药' : 'Mineral-based TCM',
      herbs: [
        { name: locale === 'zh' ? '牛黄' : 'Calculus Bovis', risk: 'high', latin: 'Calculus Bovis' },
        { name: locale === 'zh' ? '雄黄' : 'Realgar', risk: 'high', latin: 'Realgar' },
        { name: locale === 'zh' ? '朱砂' : 'Cinnabar', risk: 'high', latin: 'Cinnabaris' },
        { name: locale === 'zh' ? '珍珠' : 'Pearl', risk: 'medium', latin: 'Margarita' }
      ]
    },
    {
      category: locale === 'zh' ? '芳香类中药' : 'Aromatic TCM',
      herbs: [
        { name: locale === 'zh' ? '薄荷' : 'Mint', risk: 'medium', latin: 'Menthae Herba' },
        { name: locale === 'zh' ? '樟脑' : 'Camphor', risk: 'high', latin: 'Camphora' },
        { name: locale === 'zh' ? '冰片' : 'Borneol', risk: 'high', latin: 'Borneolum' },
        { name: locale === 'zh' ? '薄荷脑' : 'Menthol', risk: 'high', latin: 'Mentholum' }
      ]
    }
  ]

  const cautiousHerbs = [
    {
      category: locale === 'zh' ? '需要特别谨慎的中药' : 'TCM Requiring Special Caution',
      herbs: [
        { name: locale === 'zh' ? '金银花' : 'Honeysuckle', latin: 'Lonicerae Flos', risk: 'medium', reason: locale === 'zh' ? '含氧化性成分' : 'Contains oxidative components' },
        { name: locale === 'zh' ? '连翘' : 'Forsythia', latin: 'Forsythiae Fructus', risk: 'medium', reason: locale === 'zh' ? '含氧化性成分' : 'Contains oxidative components' },
        { name: locale === 'zh' ? '板蓝根' : 'Isatis Root', latin: 'Isatidis Radix', risk: 'medium', reason: locale === 'zh' ? '可能含氧化性成分' : 'May contain oxidative components' },
        { name: locale === 'zh' ? '蒲公英' : 'Dandelion', latin: 'Taraxaci Herba', risk: 'medium', reason: locale === 'zh' ? '需要谨慎使用' : 'Requires cautious use' }
      ]
    }
  ]

  const relativelyMilderHerbs = [
    {
      category: locale === 'zh' ? '相对温和的中药（仍需医师指导）' : 'Relatively Milder TCM (Still Requires Medical Guidance)',
      herbs: [
        { name: locale === 'zh' ? '甘草' : 'Licorice', latin: 'Glycyrrhizae Radix', note: locale === 'zh' ? '调和诸药，相对安全' : 'Harmonizes formulas, relatively safe' },
        { name: locale === 'zh' ? '茯苓' : 'Poria', latin: 'Poria', note: locale === 'zh' ? '健脾利湿，性质平和' : 'Spleen-strengthening, mild nature' },
        { name: locale === 'zh' ? '山药' : 'Chinese Yam', latin: 'Dioscoreae Rhizoma', note: locale === 'zh' ? '药食同源，相对安全' : 'Food-medicine dual use, relatively safe' },
        { name: locale === 'zh' ? '大枣' : 'Jujube', latin: 'Jujubae Fructus', note: locale === 'zh' ? '补中益气，性质温和' : 'Qi-tonifying, mild nature' }
      ]
    },
    {
      category: locale === 'zh' ? '止咳化痰类（需谨慎）' : 'Cough Relief (Use with Caution)',
      herbs: [
        { name: locale === 'zh' ? '川贝母' : 'Fritillaria', latin: 'Fritillariae Bulbus', note: locale === 'zh' ? '润肺止咳，需医师指导' : 'Lung-moistening, requires medical guidance' },
        { name: locale === 'zh' ? '枇杷叶' : 'Loquat Leaf', latin: 'Eriobotryae Folium', note: locale === 'zh' ? '清肺止咳，需去毛处理' : 'Lung-clearing, requires proper processing' },
        { name: locale === 'zh' ? '桔梗' : 'Platycodon', latin: 'Platycodonis Radix', note: locale === 'zh' ? '宣肺化痰，需适量使用' : 'Lung-opening, use in moderation' }
      ]
    }
  ]

  const cautiousPatentMedicines = [
    {
      name: locale === 'zh' ? '板蓝根颗粒' : 'Banlangen Granules',
      safety: 'caution',
      indication: locale === 'zh' ? '清热解毒，用于感冒发热' : 'Heat-clearing and detoxifying, for cold and fever',
      precaution: locale === 'zh' ? '可能含氧化性成分，G6PD患者应避免' : 'May contain oxidative components, G6PD patients should avoid'
    },
    {
      name: locale === 'zh' ? '金银花露' : 'Honeysuckle Dew',
      safety: 'avoid',
      indication: locale === 'zh' ? '清热解毒，用于小儿热毒' : 'Heat-clearing and detoxifying, for pediatric heat toxin',
      precaution: locale === 'zh' ? '含金银花，有氧化性成分，应避免使用' : 'Contains honeysuckle with oxidative components, should be avoided'
    }
  ]

  const milderPatentMedicines = [
    {
      name: locale === 'zh' ? '川贝枇杷膏' : 'Fritillaria and Loquat Syrup',
      safety: 'mild',
      indication: locale === 'zh' ? '润肺止咳，用于干咳少痰' : 'Lung-moistening and cough-relieving, for dry cough',
      precaution: locale === 'zh' ? '相对温和，但仍需医师指导，糖尿病患者慎用' : 'Relatively mild, but still requires medical guidance, use with caution in diabetic patients'
    },
    {
      name: locale === 'zh' ? '甘草片' : 'Licorice Tablets',
      safety: 'mild',
      indication: locale === 'zh' ? '止咳化痰，调和诸药' : 'Cough relief and expectorant, harmonizes formulas',
      precaution: locale === 'zh' ? '相对安全，但长期使用需注意' : 'Relatively safe, but caution with long-term use'
    },
    {
      name: locale === 'zh' ? '山药片' : 'Chinese Yam Tablets',
      safety: 'mild',
      indication: locale === 'zh' ? '健脾益气，药食同源' : 'Spleen-strengthening and qi-boosting, food-medicine dual use',
      precaution: locale === 'zh' ? '药食同源，相对安全' : 'Food-medicine dual use, relatively safe'
    }
  ]

  const guidelines = [
    {
      title: locale === 'zh' ? '选择正规中医师' : 'Choose Qualified TCM Practitioners',
      description: locale === 'zh' ? '就诊时主动告知医生您患有G6PD缺乏症，让医生选择安全的中药处方' : 'Actively inform TCM doctors about your G6PD deficiency so they can prescribe safe herbal formulas',
      icon: ShieldCheckIcon
    },
    {
      title: locale === 'zh' ? '仔细阅读成分' : 'Carefully Read Ingredients',
      description: locale === 'zh' ? '购买中成药前务必查看完整成分表，避免含有禁用成分的产品' : 'Always check complete ingredient lists before purchasing patent medicines to avoid prohibited components',
      icon: InformationCircleIcon
    },
    {
      title: locale === 'zh' ? '避免自行配药' : 'Avoid Self-medication',
      description: locale === 'zh' ? '不要自行购买和配制中药，特别是含有复杂成分的复方制剂' : 'Do not self-purchase and prepare Chinese medicines, especially complex compound preparations',
      icon: ExclamationTriangleIcon
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-green-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {locale === 'zh' ? '中药用药指导' : 'Chinese Medicine Guide'}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
              {locale === 'zh' 
                ? '为G6PD缺乏症患者提供详细的中药安全用药指导'
                : 'Detailed Chinese medicine safety guide for G6PD deficiency patients'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Important Warning */}
      <section className="bg-red-50 border-b border-red-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600 mr-4" />
            <div className="text-center">
              <h3 className="text-xl font-semibold text-red-800">
                {locale === 'zh' ? '重要提醒' : 'Important Warning'}
              </h3>
              <p className="text-red-700 mt-2">
                {locale === 'zh' 
                  ? 'G6PD缺乏症患者应避免含有牛黄、雄黄、朱砂、薄荷脑等成分的中药！'
                  : 'G6PD deficient patients should avoid Chinese medicines containing Calculus Bovis, Realgar, Cinnabar, Menthol, etc.!'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Prohibited Herbs */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '禁用中药清单' : 'Prohibited Chinese Medicines'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' 
                ? 'G6PD缺乏症患者应避免使用的中药'
                : 'Chinese medicines that G6PD deficient patients should avoid'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {prohibitedHerbs.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-red-500">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {category.category}
                </h3>
                <div className="space-y-3">
                  {category.herbs.map((herb, herbIndex) => (
                    <div key={herbIndex} className="border-b border-gray-200 pb-3 last:border-b-0">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-gray-900">{herb.name}</span>
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          herb.risk === 'high' 
                            ? 'bg-red-100 text-red-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {herb.risk === 'high' 
                            ? (locale === 'zh' ? '高风险' : 'High Risk')
                            : (locale === 'zh' ? '中风险' : 'Medium Risk')
                          }
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm italic">{herb.latin}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Cautious Herbs */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '需要特别谨慎的中药' : 'Chinese Medicines Requiring Special Caution'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh'
                ? '这些中药含有氧化性成分，G6PD缺乏症患者应避免使用'
                : 'These Chinese medicines contain oxidative components and should be avoided by G6PD deficient patients'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {cautiousHerbs.map((category, index) => (
              <div key={index} className="bg-yellow-50 rounded-lg p-6 border-l-4 border-yellow-500">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mr-2" />
                  {category.category}
                </h3>
                <div className="space-y-3">
                  {category.herbs.map((herb, herbIndex) => (
                    <div key={herbIndex} className="border-b border-yellow-200 pb-3 last:border-b-0">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-gray-900">{herb.name}</span>
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-semibold">
                          {herb.risk === 'high'
                            ? (locale === 'zh' ? '高风险' : 'High Risk')
                            : (locale === 'zh' ? '中风险' : 'Medium Risk')
                          }
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm italic mb-1">{herb.latin}</p>
                      <p className="text-yellow-700 text-sm">
                        <strong>{locale === 'zh' ? '原因：' : 'Reason: '}</strong>{herb.reason}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Relatively Milder Herbs */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '相对温和的中药' : 'Relatively Milder Chinese Medicines'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh'
                ? '这些中药相对温和，但仍需在中医师严格指导下使用'
                : 'These Chinese medicines are relatively milder, but still require strict TCM practitioner guidance'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {relativelyMilderHerbs.map((category, index) => (
              <div key={index} className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <InformationCircleIcon className="h-6 w-6 text-blue-600 mr-2" />
                  {category.category}
                </h3>
                <div className="space-y-3">
                  {category.herbs.map((herb, herbIndex) => (
                    <div key={herbIndex} className="border-b border-blue-200 pb-3 last:border-b-0">
                      <p className="font-medium text-gray-900">{herb.name}</p>
                      <p className="text-gray-600 text-sm italic mb-1">{herb.latin}</p>
                      <p className="text-blue-700 text-sm">{herb.note}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Cautious Patent Medicines */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '需要避免的中成药' : 'Patent Medicines to Avoid'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh'
                ? '这些中成药含有对G6PD缺乏症患者有风险的成分'
                : 'These patent medicines contain components that are risky for G6PD deficient patients'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {cautiousPatentMedicines.map((medicine, index) => (
              <div key={index} className="bg-red-50 rounded-lg p-6 shadow-lg border-l-4 border-red-500">
                <div className="flex items-center mb-3">
                  <XCircleIcon className="h-6 w-6 text-red-600 mr-2" />
                  <h3 className="font-semibold text-gray-900">{medicine.name}</h3>
                  <span className={`ml-auto px-2 py-1 rounded text-xs font-semibold ${
                    medicine.safety === 'avoid'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {medicine.safety === 'avoid'
                      ? (locale === 'zh' ? '避免使用' : 'Avoid')
                      : (locale === 'zh' ? '谨慎使用' : 'Caution')
                    }
                  </span>
                </div>
                <p className="text-gray-700 mb-3 text-sm">{medicine.indication}</p>
                <p className="text-red-700 text-sm">
                  <strong>{locale === 'zh' ? '注意：' : 'Warning: '}</strong>
                  {medicine.precaution}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Milder Patent Medicines */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '相对温和的中成药' : 'Relatively Milder Patent Medicines'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh'
                ? '这些中成药相对温和，但仍需在医师指导下使用'
                : 'These patent medicines are relatively milder, but still require medical guidance'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {milderPatentMedicines.map((medicine, index) => (
              <div key={index} className="bg-blue-50 rounded-lg p-6 shadow-lg border-l-4 border-blue-500">
                <div className="flex items-center mb-3">
                  <InformationCircleIcon className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="font-semibold text-gray-900">{medicine.name}</h3>
                  <span className="ml-auto px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-semibold">
                    {locale === 'zh' ? '需指导' : 'Guidance Needed'}
                  </span>
                </div>
                <p className="text-gray-700 mb-3 text-sm">{medicine.indication}</p>
                <p className="text-blue-700 text-sm">
                  <strong>{locale === 'zh' ? '注意：' : 'Note: '}</strong>
                  {medicine.precaution}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Guidelines */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '用药指导原则' : 'Medication Guidelines'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {guidelines.map((item, index) => (
              <div key={index} className="bg-blue-50 rounded-lg p-6 text-center">
                <item.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {item.title}
                </h3>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '相关资源' : 'Related Resources'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link
              href={`/${locale}/medications/western-medicine`}
              className="p-6 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '西药指导' : 'Western Medicine'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '西药用药注意事项' : 'Western medicine precautions'}
              </p>
            </Link>

            <Link
              href={`/${locale}/medications/oral-solutions`}
              className="p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '口服液指导' : 'Oral Solutions'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '口服液安全指导' : 'Oral solution safety guide'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/chinese-medicine-ingredients`}
              className="p-6 bg-green-50 rounded-lg hover:bg-green-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '中药成分详解' : 'TCM Ingredients'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '详细的中药成分分析' : 'Detailed TCM ingredient analysis'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/symptoms`}
              className="p-6 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '症状识别' : 'Symptom Recognition'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '了解溶血症状' : 'Learn about hemolysis symptoms'}
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Medical Disclaimer */}
      <section className="py-8 bg-yellow-50 border-t border-yellow-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-yellow-800">
              <strong>{locale === 'zh' ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
              {locale === 'zh' 
                ? '本页面信息仅供参考，不能替代专业医疗建议。任何中药使用都应咨询合格的中医师。'
                : 'The information on this page is for reference only and cannot replace professional medical advice. Any Chinese medicine use should consult qualified TCM practitioners.'
              }
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
