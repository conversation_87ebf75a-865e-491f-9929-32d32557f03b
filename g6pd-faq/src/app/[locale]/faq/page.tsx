import { Metadata } from 'next'
import Link from 'next/link'
import { faqCategories, allFaqData } from '@/lib/faq-data'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const isZh = locale === 'zh'

  const title = isZh ? '常见问题解答 - G6PD缺乏症（蚕豆病）' : 'FAQ - G6PD Deficiency (Favism)'
  const description = isZh
    ? '关于G6PD缺乏症（蚕豆病）的专业问题解答，包括用药安全、症状识别、饮食指导等'
    : 'Professional answers about G6PD deficiency (favism), including medication safety, symptom recognition, dietary guidance and more'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function FAQPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const isZh = locale === 'zh'

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {isZh ? '常见问题解答' : 'Frequently Asked Questions'}
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              {isZh
                ? '关于G6PD缺乏症（蚕豆病）的专业问题解答，帮助您更好地了解和管理这种疾病'
                : 'Professional answers about G6PD deficiency (favism) to help you better understand and manage this condition'
              }
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Categories */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {faqCategories[locale as 'zh' | 'en'].map((category) => (
            <Link
              key={category.id}
              href={`/${locale}/faq/${category.id}`}
              className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow group"
            >
              <div className="text-4xl mb-4">{category.icon}</div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 group-hover:text-blue-600">
                {category.name}
              </h3>
              <p className="text-gray-600 text-sm">
                {category.description}
              </p>
            </Link>
          ))}
        </div>

        {/* Popular Questions */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {isZh ? '热门问题' : 'Popular Questions'}
          </h2>
          <div className="space-y-6">
            {allFaqData
              .filter(faq => faq.locale === locale)
              .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))
              .slice(0, 6)
              .map((faq) => (
                <div key={faq.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                  <h3 className="text-lg font-semibold mb-3 text-gray-900">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                  <div className="mt-3 flex flex-wrap gap-2">
                    {faq.keywords.map((keyword) => (
                      <span
                        key={keyword}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Search Section */}
        <div className="mt-12 bg-blue-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {isZh ? '找不到您要的答案？' : "Can't find what you're looking for?"}
          </h2>
          <p className="text-gray-600 mb-6">
            {isZh
              ? '使用我们的搜索功能查找更多相关信息'
              : 'Use our search function to find more relevant information'
            }
          </p>
          <Link
            href={`/${locale}/search`}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
          >
            {isZh ? '搜索FAQ' : 'Search FAQ'}
          </Link>
        </div>
      </div>
    </div>
  )
}
