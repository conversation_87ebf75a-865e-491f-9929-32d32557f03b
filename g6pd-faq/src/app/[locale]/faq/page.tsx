import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { faqManager } from '@/lib/data'
import { generateSEOMetadata, StructuredDataScript } from '@/components/seo'
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  BookOpenIcon,
  ClockIcon,
  TagIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params: { locale }
}: {
  params: { locale: string }
}): Promise<Metadata> {
  const seoData = {
    title: locale === 'zh' ? '常见问题 - G6PD缺乏症FAQ' : 'FAQ - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '浏览G6PD缺乏症（蚕豆病）的常见问题，包括用药指导、饮食建议、症状识别等专业内容。'
      : '<PERSON><PERSON><PERSON> frequently asked questions about G6PD deficiency, including medication guidance, dietary advice, and symptom recognition.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,蚕豆病,常见问题,用药指导,饮食建议,症状识别'
      : 'G6PD deficiency,FAQ,medication guidance,dietary advice,symptom recognition',
    canonical: `/${locale}/faq`
  }

  return generateSEOMetadata({ data: seoData, locale })
}

export default async function FAQPage({ 
  params: { locale },
  searchParams
}: { 
  params: { locale: string }
  searchParams: { category?: string; search?: string; page?: string }
}) {
  const t = await getTranslations()
  const faqs = await faqManager.loadFAQs(locale)
  
  // Filter FAQs based on search params
  let filteredFAQs = faqs
  
  if (searchParams.category) {
    filteredFAQs = filteredFAQs.filter(faq => faq.category === searchParams.category)
  }
  
  if (searchParams.search) {
    const searchTerm = searchParams.search.toLowerCase()
    filteredFAQs = filteredFAQs.filter(faq => 
      faq.title.toLowerCase().includes(searchTerm) ||
      faq.question.toLowerCase().includes(searchTerm) ||
      faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  // Pagination
  const page = parseInt(searchParams.page || '1')
  const itemsPerPage = 12
  const totalPages = Math.ceil(filteredFAQs.length / itemsPerPage)
  const startIndex = (page - 1) * itemsPerPage
  const paginatedFAQs = filteredFAQs.slice(startIndex, startIndex + itemsPerPage)

  const categories = [
    { id: 'all', name: t('faq.categories.all'), count: faqs.length },
    { id: 'medications', name: t('faq.categories.medications'), count: faqs.filter(f => f.category === 'medications').length },
    { id: 'diet', name: t('faq.categories.diet'), count: faqs.filter(f => f.category === 'diet').length },
    { id: 'symptoms', name: t('faq.categories.symptoms'), count: faqs.filter(f => f.category === 'symptoms').length },
    { id: 'treatment', name: t('faq.categories.treatment'), count: faqs.filter(f => f.category === 'treatment').length },
  ]

  return (
    <>
      <StructuredDataScript 
        data={{ faqs: paginatedFAQs }} 
        type="FAQPage" 
        locale={locale} 
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {t('faq.title')}
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                {t('faq.subtitle')}
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar */}
            <div className="lg:w-1/4">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
                {/* Search */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('common.search')}
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder={t('common.searchPlaceholder')}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      defaultValue={searchParams.search || ''}
                    />
                    <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                  </div>
                </div>

                {/* Categories */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {t('faq.filters.category')}
                  </h3>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <Link
                        key={category.id}
                        href={`/${locale}/faq${category.id !== 'all' ? `?category=${category.id}` : ''}`}
                        className={`flex items-center justify-between p-2 rounded-md text-sm transition-colors ${
                          (searchParams.category === category.id) || (category.id === 'all' && !searchParams.category)
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        <span>{category.name}</span>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          {category.count}
                        </span>
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Difficulty Filter */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {t('faq.filters.difficulty')}
                  </h3>
                  <div className="space-y-2">
                    {['basic', 'intermediate', 'advanced'].map((level) => (
                      <label key={level} className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          {t(`faq.difficulty.${level}`)}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:w-3/4">
              {/* Results Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    {locale === 'zh' 
                      ? `显示 ${startIndex + 1}-${Math.min(startIndex + itemsPerPage, filteredFAQs.length)} 条，共 ${filteredFAQs.length} 条结果`
                      : `Showing ${startIndex + 1}-${Math.min(startIndex + itemsPerPage, filteredFAQs.length)} of ${filteredFAQs.length} results`
                    }
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <FunnelIcon className="h-5 w-5 text-gray-400" />
                  <select className="text-sm border border-gray-300 rounded-md px-3 py-1">
                    <option>{locale === 'zh' ? '最新更新' : 'Latest Updated'}</option>
                    <option>{locale === 'zh' ? '最受欢迎' : 'Most Popular'}</option>
                    <option>{locale === 'zh' ? '按难度' : 'By Difficulty'}</option>
                  </select>
                </div>
              </div>

              {/* FAQ Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {paginatedFAQs.map((faq) => (
                  <div key={faq.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs font-medium rounded ${
                          faq.difficulty === 'basic' ? 'bg-green-100 text-green-800' :
                          faq.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {t(`faq.difficulty.${faq.difficulty}`)}
                        </span>
                        {faq.medicalReview && (
                          <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                            {locale === 'zh' ? '医学审核' : 'Medical Review'}
                          </span>
                        )}
                      </div>
                      <ClockIcon className="h-4 w-4 text-gray-400" />
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {faq.title}
                    </h3>

                    <p className="text-gray-600 text-sm mb-4">
                      {faq.shortAnswer || faq.answer.substring(0, 150) + '...'}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {faq.tags.slice(0, 2).map((tag) => (
                          <span key={tag} className="inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                            <TagIcon className="h-3 w-3 mr-1" />
                            {tag}
                          </span>
                        ))}
                      </div>
                      <Link
                        href={`/${locale}/faq/${faq.slug}`}
                        className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        {t('common.readMore')}
                        <BookOpenIcon className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center space-x-2">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                    <Link
                      key={pageNum}
                      href={`/${locale}/faq?page=${pageNum}${searchParams.category ? `&category=${searchParams.category}` : ''}${searchParams.search ? `&search=${searchParams.search}` : ''}`}
                      className={`px-3 py-2 text-sm rounded-md ${
                        pageNum === page
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {pageNum}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
