import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale })

  return {
    title: t('faq.title'),
    description: t('faq.subtitle'),
    openGraph: {
      title: t('faq.title'),
      description: t('faq.subtitle'),
      type: 'website',
    },
  }
}

export default async function FAQPage({
  params,
  searchParams
}: {
  params: Promise<{ locale: string }>
  searchParams: Promise<{ category?: string; search?: string; page?: string }>
}) {
  const { locale } = await params
  const searchParamsData = await searchParams
  const t = await getTranslations({ locale })

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {t('faq.title')}
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('faq.subtitle')}
            </p>
          </div>
        </div>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            FAQ页面开发中
          </h2>
          <p className="text-gray-600">
            FAQ功能正在开发中，敬请期待...
          </p>
        </div>
      </div>
    </div>
  )
}
