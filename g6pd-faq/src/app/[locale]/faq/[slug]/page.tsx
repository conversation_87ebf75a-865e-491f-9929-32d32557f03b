import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { faqManager } from '@/lib/data'
import { generateSEOMetadata, StructuredDataScript } from '@/components/seo'
import { generateBreadcrumbs, formatDate } from '@/lib/utils'
import {
  ClockIcon,
  TagIcon,
  ShareIcon,
  PrinterIcon,
  ChevronLeftIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

export async function generateStaticParams() {
  const zhFaqs = await faqManager.loadFAQs('zh')
  const enFaqs = await faqManager.loadFAQs('en')
  
  const params = []
  
  for (const faq of zhFaqs) {
    params.push({ locale: 'zh', slug: faq.slug })
  }
  
  for (const faq of enFaqs) {
    params.push({ locale: 'en', slug: faq.slug })
  }
  
  return params
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string; slug: string }>
}): Promise<Metadata> {
  const { locale, slug } = await params
  const faq = await faqManager.getFAQ(slug, locale)
  
  if (!faq) {
    return {
      title: 'FAQ Not Found',
      description: 'The requested FAQ could not be found.'
    }
  }

  const seoData = {
    title: faq.title,
    description: faq.shortAnswer || faq.answer.substring(0, 160),
    keywords: faq.tags.join(', '),
    canonical: `/${locale}/faq/${slug}`
  }

  return generateSEOMetadata({ data: seoData, locale })
}

export default async function FAQDetailPage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}) {
  const { locale, slug } = await params;
  const faq = await faqManager.getFAQ(slug, locale);
  
  if (!faq) {
    notFound();
  }

  const relatedFaqs = await faqManager.getRelatedFAQs(faq.id, locale, 4)
  const breadcrumbs = generateBreadcrumbs(`/${locale}/faq/${slug}`, locale)

  return (
    <>
      <StructuredDataScript 
        data={{ 
          title: faq.title,
          description: faq.shortAnswer || faq.answer.substring(0, 200),
          lastUpdated: faq.lastUpdated,
          breadcrumbs
        }} 
        type="Article" 
        locale={locale} 
      />
      
      <StructuredDataScript 
        data={{ breadcrumbs }} 
        type="BreadcrumbList" 
        locale={locale} 
      />

      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumbs */}
        <div className="bg-white border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2">
                {breadcrumbs.map((item, index) => (
                  <li key={item.href} className="flex items-center">
                    {index > 0 && (
                      <svg className="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                    {index === breadcrumbs.length - 1 ? (
                      <span className="text-sm font-medium text-gray-500 truncate max-w-xs">
                        {item.title}
                      </span>
                    ) : (
                      <Link href={item.href} className="text-sm font-medium text-blue-600 hover:text-blue-700">
                        {item.title}
                      </Link>
                    )}
                  </li>
                ))}
              </ol>
            </nav>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Back button */}
          <div className="mb-6">
            <Link
              href={`/${locale}/faq`}
              className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              <ChevronLeftIcon className="h-4 w-4 mr-1" />
              {locale === 'zh' ? '返回FAQ列表' : 'Back to FAQ'}
            </Link>
          </div>

          {/* Main content */}
          <article className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* Header */}
            <header className="px-6 py-8 border-b border-gray-200">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                    faq.difficulty === 'basic' ? 'bg-green-100 text-green-800' :
                    faq.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {locale === 'zh' ? 
                      (faq.difficulty === 'basic' ? '基础' : faq.difficulty === 'intermediate' ? '中级' : '高级') :
                      (faq.difficulty === 'basic' ? 'Basic' : faq.difficulty === 'intermediate' ? 'Intermediate' : 'Advanced')
                    }
                  </span>
                  {faq.medicalReview && (
                    <span className="inline-flex items-center px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full">
                      <CheckCircleIcon className="h-4 w-4 mr-1" />
                      {locale === 'zh' ? '医学审核' : 'Medical Review'}
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-md">
                    <ShareIcon className="h-5 w-5" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-md">
                    <PrinterIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {faq.title}
              </h1>
              
              <div className="flex items-center text-sm text-gray-500 space-x-4">
                <div className="flex items-center">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {locale === 'zh' ? '最后更新：' : 'Last updated: '}
                  {formatDate(faq.lastUpdated, locale)}
                </div>
                {faq.author && (
                  <div>
                    {locale === 'zh' ? '作者：' : 'Author: '}{faq.author}
                  </div>
                )}
              </div>
            </header>

            {/* Content */}
            <div className="px-6 py-8">
              {/* Emergency warning for certain topics */}
              {(faq.category === 'symptoms' || faq.tags.includes('急性溶血') || faq.tags.includes('emergency')) && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5" />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        {locale === 'zh' ? '紧急提醒' : 'Emergency Notice'}
                      </h3>
                      <p className="mt-1 text-sm text-red-700">
                        {locale === 'zh' 
                          ? '如出现急性溶血症状，请立即就医。本内容仅供参考，不能替代专业医疗建议。'
                          : 'If you experience acute hemolysis symptoms, seek immediate medical attention. This content is for reference only and cannot replace professional medical advice.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* FAQ Content */}
              <div className="prose prose-lg max-w-none">
                <div dangerouslySetInnerHTML={{ __html: faq.answer }} />
              </div>

              {/* Tags */}
              {faq.tags.length > 0 && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {locale === 'zh' ? '相关标签' : 'Related Tags'}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {faq.tags.map((tag) => (
                      <span key={tag} className="inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full">
                        <TagIcon className="h-3 w-3 mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Sources */}
              {faq.sources && faq.sources.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {locale === 'zh' ? '参考来源' : 'Sources'}
                  </h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {faq.sources.map((source, index) => (
                      <li key={index}>• {source}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </article>

          {/* Related FAQs */}
          {relatedFaqs.length > 0 && (
            <div className="mt-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {locale === 'zh' ? '相关问题' : 'Related Questions'}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {relatedFaqs.map((relatedFaq) => (
                  <Link
                    key={relatedFaq.id}
                    href={`/${locale}/faq/${relatedFaq.slug}`}
                    className="block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                  >
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {relatedFaq.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {relatedFaq.shortAnswer || relatedFaq.answer.substring(0, 100) + '...'}
                    </p>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  )
}
