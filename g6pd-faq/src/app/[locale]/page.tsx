export default async function HomePage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const isZh = locale === 'zh'

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              {isZh ? 'G6PD缺乏症（蚕豆病）' : 'G6PD Deficiency (Favism)'}
              <span className="block text-blue-600">
                {isZh ? '专业指导中心' : 'Professional Guide Center'}
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-gray-600">
              {isZh
                ? '为患者及家属提供权威、全面的医疗信息和生活指导，包括用药安全、饮食建议、症状识别等专业内容'
                : 'Providing authoritative and comprehensive medical information and lifestyle guidance for patients and families, including medication safety, dietary advice, symptom recognition and more'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href={`/${locale}/faq`}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                {isZh ? '浏览常见问题' : 'Browse FAQ'}
              </a>
              <a
                href={`/${locale}/medications`}
                className="bg-white text-blue-600 border-2 border-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                {isZh ? '用药指导' : 'Medication Guide'}
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Access Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">
              {isZh ? '中药禁忌' : 'Chinese Medicine Contraindications'}
            </h3>
            <p className="text-gray-600 mb-4">
              {isZh
                ? '了解G6PD缺乏症患者不能使用的中药成分和药物，避免溶血性贫血发作'
                : 'Learn about Chinese medicine ingredients and drugs that G6PD deficient patients cannot use to avoid hemolytic anemia attacks'
              }
            </p>
            <a
              href={`/${locale}/faq/medications/chinese-medicine`}
              className="text-blue-600 font-medium hover:text-blue-800"
            >
              {isZh ? '查看详情 →' : 'View Details →'}
            </a>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="text-orange-600 mb-4">
              <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">
              {isZh ? '口服液安全' : 'Oral Solution Safety'}
            </h3>
            <p className="text-gray-600 mb-4">
              {isZh
                ? '查询各种口服液药物的安全性，包括儿童常用的感冒药、退烧药等'
                : 'Check the safety of various oral solution medications, including common cold and fever medicines for children'
              }
            </p>
            <a
              href={`/${locale}/faq/medications/oral-solutions`}
              className="text-blue-600 font-medium hover:text-blue-800"
            >
              {isZh ? '查看详情 →' : 'View Details →'}
            </a>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="text-green-600 mb-4">
              <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">
              {isZh ? '症状识别' : 'Symptom Recognition'}
            </h3>
            <p className="text-gray-600 mb-4">
              {isZh
                ? '学习识别G6PD缺乏症的症状表现，了解何时需要紧急就医'
                : 'Learn to recognize symptoms of G6PD deficiency and understand when emergency medical care is needed'
              }
            </p>
            <a
              href={`/${locale}/faq/symptoms`}
              className="text-blue-600 font-medium hover:text-blue-800"
            >
              {isZh ? '查看详情 →' : 'View Details →'}
            </a>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              {isZh ? '关于G6PD缺乏症' : 'About G6PD Deficiency'}
            </h2>
            <p className="text-xl text-blue-100">
              {isZh
                ? '了解这种常见的遗传性疾病的基本信息'
                : 'Learn basic information about this common genetic disorder'
              }
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">4-5%</div>
              <div className="text-blue-100">
                {isZh ? '全球患病率' : 'Global Prevalence'}
              </div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">400M+</div>
              <div className="text-blue-100">
                {isZh ? '全球患者数量' : 'Global Patient Count'}
              </div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">X-linked</div>
              <div className="text-blue-100">
                {isZh ? '遗传方式' : 'Inheritance Pattern'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
