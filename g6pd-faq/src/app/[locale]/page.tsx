import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import {
  HeartIcon,
  ShieldCheckIcon,
  BookOpenIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

export default async function HomePage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const t = await getTranslations({ locale })

  const quickAccessItems = [
    {
      title: t('home.quickAccess.medications'),
      href: `/${locale}/medications`,
      icon: ShieldCheckIcon,
      description: locale === 'zh' ? '查询药物安全性' : 'Check medication safety'
    },
    {
      title: t('home.quickAccess.diet'),
      href: `/${locale}/faq/diet`,
      icon: HeartIcon,
      description: locale === 'zh' ? '了解饮食禁忌' : 'Learn about dietary restrictions'
    },
    {
      title: t('home.quickAccess.symptoms'),
      href: `/${locale}/faq/symptoms`,
      icon: ExclamationTriangleIcon,
      description: locale === 'zh' ? '识别症状表现' : 'Identify symptoms'
    }
  ]

  const featuredCategories = [
    {
      title: locale === 'zh' ? '中药相关' : 'Chinese Medicine',
      href: `/${locale}/medications/chinese-medicine`,
      icon: BookOpenIcon,
      count: '50+',
      description: locale === 'zh' ? '中药使用注意事项' : 'Chinese medicine precautions'
    },
    {
      title: locale === 'zh' ? '口服液相关' : 'Oral Solutions',
      href: `/${locale}/medications/oral-solutions`,
      icon: ShieldCheckIcon,
      count: '30+',
      description: locale === 'zh' ? '口服液安全指导' : 'Oral solution safety guide'
    },
    {
      title: locale === 'zh' ? '症状识别' : 'Symptom Recognition',
      href: `/${locale}/faq/symptoms`,
      icon: ExclamationTriangleIcon,
      count: '20+',
      description: locale === 'zh' ? '急性溶血症状' : 'Acute hemolysis symptoms'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('home.hero.title')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
              {t('home.hero.subtitle')}
            </p>
            <Link
              href={`/${locale}/faq`}
              className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
            >
              {t('home.hero.cta')}
              <BookOpenIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Emergency Alert */}
      <section className="bg-red-50 border-b border-red-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-center">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-3" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-red-800">
                {t('home.emergencyAlert.title')}
              </h3>
              <p className="text-red-700 mt-1">
                {t('home.emergencyAlert.content')}
              </p>
              <Link
                href={`/${locale}/emergency`}
                className="inline-flex items-center mt-2 text-sm font-medium text-red-800 hover:text-red-900"
              >
                {t('home.emergencyAlert.button')} →
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Access */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t('home.quickAccess.title')}
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {quickAccessItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="group p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all"
              >
                <div className="flex items-center mb-4">
                  <item.icon className="h-8 w-8 text-blue-600 group-hover:text-blue-700" />
                  <h3 className="ml-3 text-lg font-semibold text-gray-900 group-hover:text-blue-700">
                    {item.title}
                  </h3>
                </div>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '热门分类' : 'Popular Categories'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' ? '最常查询的问题分类' : 'Most frequently searched question categories'}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredCategories.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <category.icon className="h-8 w-8 text-blue-600" />
                  <span className="text-sm font-semibold text-blue-600 bg-blue-100 px-2 py-1 rounded">
                    {category.count}
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {category.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {category.description}
                </p>
                <Link
                  href={category.href}
                  className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                >
                  {locale === 'zh' ? '查看更多' : 'Learn more'} →
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {locale === 'zh' ? '找不到答案？' : 'Can\'t find an answer?'}
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            {locale === 'zh' ? '使用搜索功能快速找到您需要的信息' : 'Use our search function to quickly find the information you need'}
          </p>
          <Link
            href={`/${locale}/search`}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <MagnifyingGlassIcon className="mr-2 h-5 w-5" />
            {locale === 'zh' ? '开始搜索' : 'Start searching'}
          </Link>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">200+</div>
              <div className="text-blue-100">
                {locale === 'zh' ? '常见问题' : 'FAQ Articles'}
              </div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">50+</div>
              <div className="text-blue-100">
                {locale === 'zh' ? '药物信息' : 'Medications'}
              </div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">10+</div>
              <div className="text-blue-100">
                {locale === 'zh' ? '专业分类' : 'Categories'}
              </div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">24/7</div>
              <div className="text-blue-100">
                {locale === 'zh' ? '在线访问' : 'Online Access'}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
