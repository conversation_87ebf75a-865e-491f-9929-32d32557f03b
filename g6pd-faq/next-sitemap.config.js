/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd.site',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: ['/server-sitemap.xml'],
  alternateRefs: [
    {
      href: 'https://g6pd.site/zh',
      hreflang: 'zh',
    },
    {
      href: 'https://g6pd.site/en',
      hreflang: 'en',
    },
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/_next/', '/admin/'],
      },
    ],
    additionalSitemaps: [
      'https://g6pd.site/server-sitemap.xml',
    ],
  },
  transform: async (config, path) => {
    // Custom transformation for multilingual paths
    const defaultTransform = {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
      alternateRefs: config.alternateRefs ?? [],
    }

    // Set different priorities based on page type
    if (path === '/zh' || path === '/en') {
      return {
        ...defaultTransform,
        priority: 1.0,
        changefreq: 'daily',
      }
    }

    if (path.includes('/faq/') || path.includes('/medications/')) {
      return {
        ...defaultTransform,
        priority: 0.8,
        changefreq: 'weekly',
      }
    }

    if (path.includes('/search') || path.includes('/about')) {
      return {
        ...defaultTransform,
        priority: 0.6,
        changefreq: 'monthly',
      }
    }

    return defaultTransform
  },
}
