[{"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx": "1", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx": "2", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx": "3", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx": "4", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx": "5", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx": "6", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx": "7", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx": "8", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx": "9", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx": "10", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx": "11", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts": "12", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts": "13", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts": "14", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts": "15"}, {"size": 10567, "mtime": 1751010018962, "results": "16", "hashOfConfig": "17"}, {"size": 11650, "mtime": 1751007295062, "results": "18", "hashOfConfig": "17"}, {"size": 2161, "mtime": 1751007348957, "results": "19", "hashOfConfig": "17"}, {"size": 8910, "mtime": 1751007332047, "results": "20", "hashOfConfig": "17"}, {"size": 570, "mtime": 1750978944822, "results": "21", "hashOfConfig": "17"}, {"size": 4086, "mtime": 1750953852807, "results": "22", "hashOfConfig": "17"}, {"size": 6545, "mtime": 1750978985652, "results": "23", "hashOfConfig": "17"}, {"size": 3202, "mtime": 1750978902223, "results": "24", "hashOfConfig": "17"}, {"size": 6017, "mtime": 1750978925491, "results": "25", "hashOfConfig": "17"}, {"size": 8266, "mtime": 1751007182950, "results": "26", "hashOfConfig": "17"}, {"size": 7087, "mtime": 1751007142189, "results": "27", "hashOfConfig": "17"}, {"size": 489, "mtime": 1751006949186, "results": "28", "hashOfConfig": "17"}, {"size": 9785, "mtime": 1750954592567, "results": "29", "hashOfConfig": "17"}, {"size": 2345, "mtime": 1751006964480, "results": "30", "hashOfConfig": "17"}, {"size": 6233, "mtime": 1751006992256, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "smafxb", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx", ["77"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx", ["78", "79"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts", [], [], {"ruleId": "80", "severity": 2, "message": "81", "line": 60, "column": 4, "nodeType": "82", "messageId": "83", "endLine": 60, "endColumn": 7, "suggestions": "84"}, {"ruleId": "85", "severity": 1, "message": "86", "line": 192, "column": 12, "nodeType": "87", "endLine": 192, "endColumn": 72}, {"ruleId": "85", "severity": 1, "message": "86", "line": 196, "column": 5, "nodeType": "87", "endLine": 202, "endColumn": 7}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["88", "89"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"messageId": "90", "fix": "91", "desc": "92"}, {"messageId": "93", "fix": "94", "desc": "95"}, "suggestUnknown", {"range": "96", "text": "97"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "98", "text": "99"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", [1437, 1440], "unknown", [1437, 1440], "never"]