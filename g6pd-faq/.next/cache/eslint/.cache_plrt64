[{"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx": "1", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx": "2", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx": "3", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx": "4", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx": "5", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx": "6", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx": "7", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx": "8", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx": "9", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx": "10", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx": "11", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts": "12", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts": "13", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts": "14", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts": "15"}, {"size": 10562, "mtime": 1750979237215, "results": "16", "hashOfConfig": "17"}, {"size": 11535, "mtime": 1750979192152, "results": "18", "hashOfConfig": "17"}, {"size": 1978, "mtime": 1750978961103, "results": "19", "hashOfConfig": "17"}, {"size": 8880, "mtime": 1750979021549, "results": "20", "hashOfConfig": "17"}, {"size": 570, "mtime": 1750978944822, "results": "21", "hashOfConfig": "17"}, {"size": 4086, "mtime": 1750953852807, "results": "22", "hashOfConfig": "17"}, {"size": 6545, "mtime": 1750978985652, "results": "23", "hashOfConfig": "17"}, {"size": 3202, "mtime": 1750978902223, "results": "24", "hashOfConfig": "17"}, {"size": 6017, "mtime": 1750978925491, "results": "25", "hashOfConfig": "17"}, {"size": 7747, "mtime": 1750979614829, "results": "26", "hashOfConfig": "17"}, {"size": 6961, "mtime": 1750979082431, "results": "27", "hashOfConfig": "17"}, {"size": 486, "mtime": 1750954446939, "results": "28", "hashOfConfig": "17"}, {"size": 9785, "mtime": 1750954592567, "results": "29", "hashOfConfig": "17"}, {"size": 2325, "mtime": 1750954520937, "results": "30", "hashOfConfig": "17"}, {"size": 6217, "mtime": 1750954548965, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "smafxb", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx", ["77"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx", ["78", "79", "80", "81"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx", ["82"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx", ["83", "84", "85", "86", "87", "88", "89", "90", "91"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx", ["92", "93", "94", "95", "96"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts", ["97"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts", ["98"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts", ["99", "100", "101", "102"], [], {"ruleId": "103", "severity": 2, "message": "104", "line": 40, "column": 13, "nodeType": "105", "endLine": 40, "endColumn": 28}, {"ruleId": "106", "severity": 2, "message": "107", "line": 19, "column": 34, "nodeType": "108", "messageId": "109", "endLine": 19, "endColumn": 37, "suggestions": "110"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 20, "column": 40, "nodeType": "108", "messageId": "109", "endLine": 20, "endColumn": 43, "suggestions": "111"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 28, "column": 28, "nodeType": "108", "messageId": "109", "endLine": 28, "endColumn": 31, "suggestions": "112"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 53, "column": 35, "nodeType": "108", "messageId": "109", "endLine": 53, "endColumn": 38, "suggestions": "113"}, {"ruleId": "114", "severity": 2, "message": "115", "line": 9, "column": 3, "nodeType": null, "messageId": "116", "endLine": 9, "endColumn": 16}, {"ruleId": "106", "severity": 2, "message": "107", "line": 37, "column": 28, "nodeType": "108", "messageId": "109", "endLine": 37, "endColumn": 31, "suggestions": "117"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 38, "column": 60, "nodeType": "108", "messageId": "109", "endLine": 38, "endColumn": 63, "suggestions": "118"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 133, "column": 34, "nodeType": "108", "messageId": "109", "endLine": 133, "endColumn": 37, "suggestions": "119"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 159, "column": 18, "nodeType": "108", "messageId": "109", "endLine": 159, "endColumn": 21, "suggestions": "120"}, {"ruleId": "121", "severity": 1, "message": "122", "line": 183, "column": 12, "nodeType": "123", "endLine": 183, "endColumn": 72}, {"ruleId": "121", "severity": 1, "message": "122", "line": 187, "column": 5, "nodeType": "123", "endLine": 193, "endColumn": 7}, {"ruleId": "106", "severity": 2, "message": "107", "line": 198, "column": 44, "nodeType": "108", "messageId": "109", "endLine": 198, "endColumn": 47, "suggestions": "124"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 216, "column": 43, "nodeType": "108", "messageId": "109", "endLine": 216, "endColumn": 46, "suggestions": "125"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 225, "column": 26, "nodeType": "108", "messageId": "109", "endLine": 225, "endColumn": 29, "suggestions": "126"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 64, "column": 46, "nodeType": "108", "messageId": "109", "endLine": 64, "endColumn": 49, "suggestions": "127"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 102, "column": 42, "nodeType": "108", "messageId": "109", "endLine": 102, "endColumn": 45, "suggestions": "128"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 165, "column": 55, "nodeType": "108", "messageId": "109", "endLine": 165, "endColumn": 58, "suggestions": "129"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 178, "column": 70, "nodeType": "108", "messageId": "109", "endLine": 178, "endColumn": 73, "suggestions": "130"}, {"ruleId": "114", "severity": 2, "message": "131", "line": 192, "column": 56, "nodeType": null, "messageId": "116", "endLine": 192, "endColumn": 62}, {"ruleId": "106", "severity": 2, "message": "107", "line": 10, "column": 35, "nodeType": "108", "messageId": "109", "endLine": 10, "endColumn": 38, "suggestions": "132"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 89, "column": 20, "nodeType": "108", "messageId": "109", "endLine": 89, "endColumn": 23, "suggestions": "133"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 148, "column": 46, "nodeType": "108", "messageId": "109", "endLine": 148, "endColumn": 49, "suggestions": "134"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 148, "column": 56, "nodeType": "108", "messageId": "109", "endLine": 148, "endColumn": 59, "suggestions": "135"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 160, "column": 46, "nodeType": "108", "messageId": "109", "endLine": 160, "endColumn": 49, "suggestions": "136"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 160, "column": 56, "nodeType": "108", "messageId": "109", "endLine": 160, "endColumn": 59, "suggestions": "137"}, "react-hooks/rules-of-hooks", "React Hook \"useTranslations\" cannot be called in an async function.", "Identifier", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["138", "139"], ["140", "141"], ["142", "143"], ["144", "145"], "@typescript-eslint/no-unused-vars", "'UserGroupIcon' is defined but never used.", "unusedVar", ["146", "147"], ["148", "149"], ["150", "151"], ["152", "153"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["154", "155"], ["156", "157"], ["158", "159"], ["160", "161"], ["162", "163"], ["164", "165"], ["166", "167"], "'locale' is defined but never used.", ["168", "169"], ["170", "171"], ["172", "173"], ["174", "175"], ["176", "177"], ["178", "179"], {"messageId": "180", "fix": "181", "desc": "182"}, {"messageId": "183", "fix": "184", "desc": "185"}, {"messageId": "180", "fix": "186", "desc": "182"}, {"messageId": "183", "fix": "187", "desc": "185"}, {"messageId": "180", "fix": "188", "desc": "182"}, {"messageId": "183", "fix": "189", "desc": "185"}, {"messageId": "180", "fix": "190", "desc": "182"}, {"messageId": "183", "fix": "191", "desc": "185"}, {"messageId": "180", "fix": "192", "desc": "182"}, {"messageId": "183", "fix": "193", "desc": "185"}, {"messageId": "180", "fix": "194", "desc": "182"}, {"messageId": "183", "fix": "195", "desc": "185"}, {"messageId": "180", "fix": "196", "desc": "182"}, {"messageId": "183", "fix": "197", "desc": "185"}, {"messageId": "180", "fix": "198", "desc": "182"}, {"messageId": "183", "fix": "199", "desc": "185"}, {"messageId": "180", "fix": "200", "desc": "182"}, {"messageId": "183", "fix": "201", "desc": "185"}, {"messageId": "180", "fix": "202", "desc": "182"}, {"messageId": "183", "fix": "203", "desc": "185"}, {"messageId": "180", "fix": "204", "desc": "182"}, {"messageId": "183", "fix": "205", "desc": "185"}, {"messageId": "180", "fix": "206", "desc": "182"}, {"messageId": "183", "fix": "207", "desc": "185"}, {"messageId": "180", "fix": "208", "desc": "182"}, {"messageId": "183", "fix": "209", "desc": "185"}, {"messageId": "180", "fix": "210", "desc": "182"}, {"messageId": "183", "fix": "211", "desc": "185"}, {"messageId": "180", "fix": "212", "desc": "182"}, {"messageId": "183", "fix": "213", "desc": "185"}, {"messageId": "180", "fix": "214", "desc": "182"}, {"messageId": "183", "fix": "215", "desc": "185"}, {"messageId": "180", "fix": "216", "desc": "182"}, {"messageId": "183", "fix": "217", "desc": "185"}, {"messageId": "180", "fix": "218", "desc": "182"}, {"messageId": "183", "fix": "219", "desc": "185"}, {"messageId": "180", "fix": "220", "desc": "182"}, {"messageId": "183", "fix": "221", "desc": "185"}, {"messageId": "180", "fix": "222", "desc": "182"}, {"messageId": "183", "fix": "223", "desc": "185"}, {"messageId": "180", "fix": "224", "desc": "182"}, {"messageId": "183", "fix": "225", "desc": "185"}, "suggestUnknown", {"range": "226", "text": "227"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "228", "text": "229"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "230", "text": "227"}, {"range": "231", "text": "229"}, {"range": "232", "text": "227"}, {"range": "233", "text": "229"}, {"range": "234", "text": "227"}, {"range": "235", "text": "229"}, {"range": "236", "text": "227"}, {"range": "237", "text": "229"}, {"range": "238", "text": "227"}, {"range": "239", "text": "229"}, {"range": "240", "text": "227"}, {"range": "241", "text": "229"}, {"range": "242", "text": "227"}, {"range": "243", "text": "229"}, {"range": "244", "text": "227"}, {"range": "245", "text": "229"}, {"range": "246", "text": "227"}, {"range": "247", "text": "229"}, {"range": "248", "text": "227"}, {"range": "249", "text": "229"}, {"range": "250", "text": "227"}, {"range": "251", "text": "229"}, {"range": "252", "text": "227"}, {"range": "253", "text": "229"}, {"range": "254", "text": "227"}, {"range": "255", "text": "229"}, {"range": "256", "text": "227"}, {"range": "257", "text": "229"}, {"range": "258", "text": "227"}, {"range": "259", "text": "229"}, {"range": "260", "text": "227"}, {"range": "261", "text": "229"}, {"range": "262", "text": "227"}, {"range": "263", "text": "229"}, {"range": "264", "text": "227"}, {"range": "265", "text": "229"}, {"range": "266", "text": "227"}, {"range": "267", "text": "229"}, {"range": "268", "text": "227"}, {"range": "269", "text": "229"}, [536, 539], "unknown", [536, 539], "never", [618, 621], [618, 621], [862, 865], [862, 865], [1408, 1411], [1408, 1411], [1114, 1117], [1114, 1117], [1196, 1199], [1196, 1199], [3938, 3941], [3938, 3941], [4392, 4395], [4392, 4395], [5324, 5327], [5324, 5327], [5780, 5783], [5780, 5783], [5999, 6002], [5999, 6002], [1636, 1639], [1636, 1639], [2863, 2866], [2863, 2866], [4992, 4995], [4992, 4995], [5299, 5302], [5299, 5302], [380, 383], [380, 383], [1730, 1733], [1730, 1733], [4325, 4328], [4325, 4328], [4335, 4338], [4335, 4338], [4634, 4637], [4634, 4637], [4644, 4647], [4644, 4647]]