[{"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx": "1", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx": "2", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx": "3", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx": "4", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx": "5", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx": "6", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx": "7", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx": "8", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx": "9", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx": "10", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx": "11", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts": "12", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts": "13", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts": "14", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts": "15", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/medications/page.tsx": "16"}, {"size": 10720, "mtime": 1751013099946, "results": "17", "hashOfConfig": "18"}, {"size": 1684, "mtime": 1751013069084, "results": "19", "hashOfConfig": "18"}, {"size": 1385, "mtime": 1751013956446, "results": "20", "hashOfConfig": "18"}, {"size": 978, "mtime": 1751013852945, "results": "21", "hashOfConfig": "18"}, {"size": 197, "mtime": 1751013339775, "results": "22", "hashOfConfig": "18"}, {"size": 92, "mtime": 1751013663097, "results": "23", "hashOfConfig": "18"}, {"size": 6545, "mtime": 1750978985652, "results": "24", "hashOfConfig": "18"}, {"size": 3202, "mtime": 1750978902223, "results": "25", "hashOfConfig": "18"}, {"size": 6017, "mtime": 1750978925491, "results": "26", "hashOfConfig": "18"}, {"size": 8266, "mtime": 1751007182950, "results": "27", "hashOfConfig": "18"}, {"size": 7087, "mtime": 1751007142189, "results": "28", "hashOfConfig": "18"}, {"size": 519, "mtime": 1751011060671, "results": "29", "hashOfConfig": "18"}, {"size": 9785, "mtime": 1750954592567, "results": "30", "hashOfConfig": "18"}, {"size": 2345, "mtime": 1751006964480, "results": "31", "hashOfConfig": "18"}, {"size": 6233, "mtime": 1751006992256, "results": "32", "hashOfConfig": "18"}, {"size": 8260, "mtime": 1751011860096, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "smafxb", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx", ["82"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx", ["83", "84"], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/medications/page.tsx", ["85", "86"], [], {"ruleId": "87", "severity": 2, "message": "88", "line": 31, "column": 9, "nodeType": null, "messageId": "89", "endLine": 31, "endColumn": 25}, {"ruleId": "90", "severity": 1, "message": "91", "line": 192, "column": 12, "nodeType": "92", "endLine": 192, "endColumn": 72}, {"ruleId": "90", "severity": 1, "message": "91", "line": 196, "column": 5, "nodeType": "92", "endLine": 202, "endColumn": 7}, {"ruleId": "87", "severity": 2, "message": "93", "line": 17, "column": 9, "nodeType": null, "messageId": "89", "endLine": 17, "endColumn": 10}, {"ruleId": "87", "severity": 2, "message": "93", "line": 32, "column": 9, "nodeType": null, "messageId": "89", "endLine": 32, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'searchParamsData' is assigned a value but never used.", "unusedVar", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'t' is assigned a value but never used."]