[{"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx": "1", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/diet/page.tsx": "2", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/medications/chinese-medicine/page.tsx": "3", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/medications/oral-solutions/page.tsx": "4", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/medications/page.tsx": "5", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx": "6", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/symptoms/page.tsx": "7", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/treatment/page.tsx": "8", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx": "9", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/medications/page.tsx": "10", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx": "11", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/search/page.tsx": "12", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx": "13", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx": "14", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/robots.ts": "15", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/sitemap.ts": "16", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx": "17", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx": "18", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/layout/Footer.tsx": "19", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/layout/Navigation.tsx": "20", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx": "21", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx": "22", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx": "23", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts": "24", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts": "25", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/faq-data.ts": "26", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts": "27", "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts": "28"}, {"size": 10720, "mtime": 1751013099946, "results": "29", "hashOfConfig": "30"}, {"size": 14562, "mtime": 1751031573214, "results": "31", "hashOfConfig": "30"}, {"size": 11545, "mtime": 1751027813745, "results": "32", "hashOfConfig": "30"}, {"size": 13121, "mtime": 1751027865776, "results": "33", "hashOfConfig": "30"}, {"size": 6922, "mtime": 1751027764307, "results": "34", "hashOfConfig": "30"}, {"size": 4804, "mtime": 1751027709509, "results": "35", "hashOfConfig": "30"}, {"size": 11065, "mtime": 1751031513935, "results": "36", "hashOfConfig": "30"}, {"size": 17567, "mtime": 1751031638735, "results": "37", "hashOfConfig": "30"}, {"size": 1638, "mtime": 1751027530905, "results": "38", "hashOfConfig": "30"}, {"size": 8117, "mtime": 1751025961712, "results": "39", "hashOfConfig": "30"}, {"size": 9245, "mtime": 1751032080914, "results": "40", "hashOfConfig": "30"}, {"size": 10409, "mtime": 1751032400817, "results": "41", "hashOfConfig": "30"}, {"size": 197, "mtime": 1751013339775, "results": "42", "hashOfConfig": "30"}, {"size": 145, "mtime": 1751027730562, "results": "43", "hashOfConfig": "30"}, {"size": 266, "mtime": 1751031849965, "results": "44", "hashOfConfig": "30"}, {"size": 1308, "mtime": 1751032130577, "results": "45", "hashOfConfig": "30"}, {"size": 6545, "mtime": 1750978985652, "results": "46", "hashOfConfig": "30"}, {"size": 3202, "mtime": 1750978902223, "results": "47", "hashOfConfig": "30"}, {"size": 4840, "mtime": 1751027477315, "results": "48", "hashOfConfig": "30"}, {"size": 6089, "mtime": 1751031698217, "results": "49", "hashOfConfig": "30"}, {"size": 6017, "mtime": 1750978925491, "results": "50", "hashOfConfig": "30"}, {"size": 8355, "mtime": 1751026142159, "results": "51", "hashOfConfig": "30"}, {"size": 7087, "mtime": 1751007142189, "results": "52", "hashOfConfig": "30"}, {"size": 519, "mtime": 1751011060671, "results": "53", "hashOfConfig": "30"}, {"size": 9785, "mtime": 1750954592567, "results": "54", "hashOfConfig": "30"}, {"size": 22032, "mtime": 1751032540569, "results": "55", "hashOfConfig": "30"}, {"size": 2345, "mtime": 1751006964480, "results": "56", "hashOfConfig": "30"}, {"size": 6233, "mtime": 1751006992256, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "smafxb", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/diet/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/medications/chinese-medicine/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/medications/oral-solutions/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/medications/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/symptoms/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/treatment/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/medications/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/search/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/robots.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/sitemap.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/layout/Navigation.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/performance-monitor.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/faq-data.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/types.ts", [], [], "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts", [], []]