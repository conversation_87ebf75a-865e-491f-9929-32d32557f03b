[{"name": "generate-buildid", "duration": 188, "timestamp": 129767093813, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751025498551, "traceId": "da2b8c02f279ed5a"}, {"name": "load-custom-routes", "duration": 1395, "timestamp": 129767094099, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751025498551, "traceId": "da2b8c02f279ed5a"}, {"name": "create-dist-dir", "duration": 404, "timestamp": 129767154604, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751025498612, "traceId": "da2b8c02f279ed5a"}, {"name": "create-pages-mapping", "duration": 228, "timestamp": 129767278685, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751025498736, "traceId": "da2b8c02f279ed5a"}, {"name": "collect-app-paths", "duration": 2353, "timestamp": 129767278954, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751025498736, "traceId": "da2b8c02f279ed5a"}, {"name": "create-app-mapping", "duration": 3232, "timestamp": 129767281337, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751025498739, "traceId": "da2b8c02f279ed5a"}, {"name": "public-dir-conflict-check", "duration": 733, "timestamp": 129767285077, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751025498742, "traceId": "da2b8c02f279ed5a"}, {"name": "generate-routes-manifest", "duration": 3054, "timestamp": 129767286060, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751025498743, "traceId": "da2b8c02f279ed5a"}, {"name": "create-entrypoints", "duration": 49800, "timestamp": 129767298773, "id": 14, "parentId": 1, "tags": {}, "startTime": 1751025498756, "traceId": "da2b8c02f279ed5a"}, {"name": "generate-webpack-config", "duration": 448758, "timestamp": 129767348805, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751025498806, "traceId": "da2b8c02f279ed5a"}, {"name": "next-trace-entrypoint-plugin", "duration": 2206, "timestamp": 129767911452, "id": 17, "parentId": 16, "tags": {}, "startTime": 1751025499369, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 343860, "timestamp": 129767920013, "id": 22, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 358060, "timestamp": 129767920041, "id": 24, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 411079, "timestamp": 129767919994, "id": 21, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module", "duration": 236441, "timestamp": 129768128906, "id": 29, "parentId": 25, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?page=%2F%5Blocale%5D%2Fmedications%2Fpage&name=app%2F%5Blocale%5D%2Fmedications%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fmedications%2Fpage.tsx&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=%2F%5Blocale%5D%2Fmedications%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!", "layer": "rsc"}, "startTime": 1751025499586, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-tsx", "duration": 16433, "timestamp": 129768366914, "id": 30, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx", "layer": "rsc"}, "startTime": 1751025499824, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-tsx", "duration": 7069, "timestamp": 129768378473, "id": 31, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx", "layer": "rsc"}, "startTime": 1751025499836, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-tsx", "duration": 5077, "timestamp": 129768384401, "id": 32, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx", "layer": "rsc"}, "startTime": 1751025499842, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-tsx", "duration": 10532, "timestamp": 129768386125, "id": 33, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx", "layer": "rsc"}, "startTime": 1751025499843, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 477715, "timestamp": 129767919434, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 478252, "timestamp": 129767919967, "id": 20, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 478208, "timestamp": 129767920026, "id": 23, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 478173, "timestamp": 129767920068, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F%5Blocale%5D%2Fpage&name=app%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=%2F%5Blocale%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-tsx", "duration": 4686, "timestamp": 129768400077, "id": 34, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx", "layer": "rsc"}, "startTime": 1751025499857, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-tsx", "duration": 15876, "timestamp": 129768400503, "id": 35, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/[slug]/page.tsx", "layer": "rsc"}, "startTime": 1751025499858, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-tsx", "duration": 16380, "timestamp": 129768404911, "id": 36, "parentId": 29, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/medications/page.tsx", "layer": "rsc"}, "startTime": 1751025499862, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-js", "duration": 13694, "timestamp": 129768485207, "id": 37, "parentId": 36, "tags": {"name": "__barrel_optimize__?names=BeakerIcon,BookOpenIcon,ExclamationTriangleIcon,ShieldCheckIcon!=!/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/@heroicons/react/24/outline/esm/index.js", "layer": "rsc"}, "startTime": 1751025499942, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module", "duration": 12394, "timestamp": 129768487466, "id": 38, "parentId": 16, "tags": {"layer": "rsc"}, "startTime": 1751025499945, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-js", "duration": 6969, "timestamp": 129768506582, "id": 39, "parentId": 37, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js", "layer": "rsc"}, "startTime": 1751025499964, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 594093, "timestamp": 129767920051, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F%5Blocale%5D%2Fmedications%2Fpage&name=app%2F%5Blocale%5D%2Fmedications%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fmedications%2Fpage.tsx&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=%2F%5Blocale%5D%2Fmedications%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module-json", "duration": 1139, "timestamp": 129768533110, "id": 40, "parentId": 38, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/messages/zh.json", "layer": "rsc"}, "startTime": 1751025499990, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 620303, "timestamp": 129767920196, "id": 28, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F%5Blocale%5D%2Ffaq%2F%5Bslug%5D%2Fpage&name=app%2F%5Blocale%5D%2Ffaq%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ffaq%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=%2F%5Blocale%5D%2Ffaq%2F%5Bslug%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "add-entry", "duration": 624744, "timestamp": 129767920083, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F%5Blocale%5D%2Ffaq%2Fpage&name=app%2F%5Blocale%5D%2Ffaq%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ffaq%2Fpage.tsx&appDir=%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp&appPaths=%2F%5Blocale%5D%2Ffaq%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751025499377, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module", "duration": 2072, "timestamp": 129768611223, "id": 83, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!", "layer": "ssr"}, "startTime": 1751025500068, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module", "duration": 242, "timestamp": 129768613371, "id": 84, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmxm%2Fworkspace%2Fcode%2Fsoho%2F2024%2Fg6pd-faq%2Fg6pd-faq%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!", "layer": "rsc"}, "startTime": 1751025500071, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module", "duration": 169, "timestamp": 129768613635, "id": 85, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!", "layer": "ssr"}, "startTime": 1751025500071, "traceId": "da2b8c02f279ed5a"}, {"name": "build-module", "duration": 173, "timestamp": 129768613874, "id": 86, "parentId": 16, "tags": {"name": "/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!", "layer": "rsc"}, "startTime": 1751025500071, "traceId": "da2b8c02f279ed5a"}, {"name": "make", "duration": 760120, "timestamp": 129767919082, "id": 18, "parentId": 16, "tags": {}, "startTime": 1751025499376, "traceId": "da2b8c02f279ed5a"}, {"name": "get-entries", "duration": 780, "timestamp": 129768680629, "id": 88, "parentId": 87, "tags": {}, "startTime": 1751025500138, "traceId": "da2b8c02f279ed5a"}, {"name": "node-file-trace-plugin", "duration": 108594, "timestamp": 129768684907, "id": 89, "parentId": 87, "tags": {"traceEntryCount": "16"}, "startTime": 1751025500142, "traceId": "da2b8c02f279ed5a"}, {"name": "collect-traced-files", "duration": 677, "timestamp": 129768793514, "id": 90, "parentId": 87, "tags": {}, "startTime": 1751025500251, "traceId": "da2b8c02f279ed5a"}, {"name": "finish-modules", "duration": 113806, "timestamp": 129768680392, "id": 87, "parentId": 17, "tags": {}, "startTime": 1751025500138, "traceId": "da2b8c02f279ed5a"}, {"name": "chunk-graph", "duration": 13321, "timestamp": 129768822374, "id": 92, "parentId": 91, "tags": {}, "startTime": 1751025500280, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-modules", "duration": 43, "timestamp": 129768835849, "id": 94, "parentId": 91, "tags": {}, "startTime": 1751025500293, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-chunks", "duration": 13643, "timestamp": 129768835979, "id": 95, "parentId": 91, "tags": {}, "startTime": 1751025500293, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-tree", "duration": 133, "timestamp": 129768849742, "id": 96, "parentId": 91, "tags": {}, "startTime": 1751025500307, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-chunk-modules", "duration": 8262, "timestamp": 129768849967, "id": 97, "parentId": 91, "tags": {}, "startTime": 1751025500307, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize", "duration": 22556, "timestamp": 129768835776, "id": 93, "parentId": 91, "tags": {}, "startTime": 1751025500293, "traceId": "da2b8c02f279ed5a"}, {"name": "module-hash", "duration": 15139, "timestamp": 129768872295, "id": 98, "parentId": 91, "tags": {}, "startTime": 1751025500330, "traceId": "da2b8c02f279ed5a"}, {"name": "code-generation", "duration": 110463, "timestamp": 129768887494, "id": 99, "parentId": 91, "tags": {}, "startTime": 1751025500345, "traceId": "da2b8c02f279ed5a"}, {"name": "hash", "duration": 9721, "timestamp": 129769004267, "id": 100, "parentId": 91, "tags": {}, "startTime": 1751025500462, "traceId": "da2b8c02f279ed5a"}, {"name": "code-generation-jobs", "duration": 318, "timestamp": 129769013986, "id": 101, "parentId": 91, "tags": {}, "startTime": 1751025500471, "traceId": "da2b8c02f279ed5a"}, {"name": "module-assets", "duration": 360, "timestamp": 129769014239, "id": 102, "parentId": 91, "tags": {}, "startTime": 1751025500471, "traceId": "da2b8c02f279ed5a"}, {"name": "create-chunk-assets", "duration": 7549, "timestamp": 129769014618, "id": 103, "parentId": 91, "tags": {}, "startTime": 1751025500472, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 17884, "timestamp": 129769040801, "id": 107, "parentId": 104, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751025500498, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 17800, "timestamp": 129769040901, "id": 108, "parentId": 104, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751025500498, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 17364, "timestamp": 129769041339, "id": 110, "parentId": 104, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751025500499, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 12222, "timestamp": 129769046484, "id": 115, "parentId": 104, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751025500504, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 12192, "timestamp": 129769046516, "id": 116, "parentId": 104, "tags": {"name": "368.js", "cache": "HIT"}, "startTime": 1751025500504, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 12129, "timestamp": 129769046581, "id": 118, "parentId": 104, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1751025500504, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 58, "timestamp": 129769058654, "id": 123, "parentId": 104, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1751025500516, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 39456, "timestamp": 129769040062, "id": 106, "parentId": 104, "tags": {"name": "../app/_not-found/page.js", "cache": "MISS"}, "startTime": 1751025500497, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 38654, "timestamp": 129769040916, "id": 109, "parentId": 104, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1751025500498, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 36697, "timestamp": 129769042911, "id": 112, "parentId": 104, "tags": {"name": "../app/[locale]/page.js", "cache": "MISS"}, "startTime": 1751025500500, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 38290, "timestamp": 129769041366, "id": 111, "parentId": 104, "tags": {"name": "../app/[locale]/medications/page.js", "cache": "MISS"}, "startTime": 1751025500499, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 33211, "timestamp": 129769046526, "id": 117, "parentId": 104, "tags": {"name": "961.js", "cache": "MISS"}, "startTime": 1751025500504, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 36494, "timestamp": 129769043299, "id": 113, "parentId": 104, "tags": {"name": "../app/[locale]/faq/page.js", "cache": "MISS"}, "startTime": 1751025500501, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 48236, "timestamp": 129769049854, "id": 120, "parentId": 104, "tags": {"name": "658.js", "cache": "MISS"}, "startTime": 1751025500507, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 70623, "timestamp": 129769036245, "id": 105, "parentId": 104, "tags": {"name": "../app/favicon.ico/route.js", "cache": "MISS"}, "startTime": 1751025500493, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 111909, "timestamp": 129769050172, "id": 121, "parentId": 104, "tags": {"name": "751.js", "cache": "MISS"}, "startTime": 1751025500507, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 123852, "timestamp": 129769056189, "id": 122, "parentId": 104, "tags": {"name": "272.js", "cache": "MISS"}, "startTime": 1751025500513, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 147883, "timestamp": 129769043644, "id": 114, "parentId": 104, "tags": {"name": "../app/[locale]/faq/[slug]/page.js", "cache": "MISS"}, "startTime": 1751025500501, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 147570, "timestamp": 129769046594, "id": 119, "parentId": 104, "tags": {"name": "825.js", "cache": "MISS"}, "startTime": 1751025500504, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-webpack-plugin-optimize", "duration": 169246, "timestamp": 129769024934, "id": 104, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751025500482, "traceId": "da2b8c02f279ed5a"}, {"name": "css-minimizer-plugin", "duration": 184, "timestamp": 129769194372, "id": 124, "parentId": 16, "tags": {}, "startTime": 1751025500652, "traceId": "da2b8c02f279ed5a"}, {"name": "create-trace-assets", "duration": 2298, "timestamp": 129769194804, "id": 125, "parentId": 17, "tags": {}, "startTime": 1751025500652, "traceId": "da2b8c02f279ed5a"}, {"name": "seal", "duration": 394689, "timestamp": 129768810823, "id": 91, "parentId": 16, "tags": {}, "startTime": 1751025500268, "traceId": "da2b8c02f279ed5a"}, {"name": "webpack-compilation", "duration": 1304065, "timestamp": 129767909630, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751025499367, "traceId": "da2b8c02f279ed5a"}, {"name": "emit", "duration": 12461, "timestamp": 129769214175, "id": 126, "parentId": 13, "tags": {}, "startTime": 1751025500671, "traceId": "da2b8c02f279ed5a"}, {"name": "webpack-close", "duration": 311536, "timestamp": 129769227952, "id": 127, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751025500685, "traceId": "da2b8c02f279ed5a"}, {"name": "webpack-generate-error-stats", "duration": 4593, "timestamp": 129769539588, "id": 128, "parentId": 127, "tags": {}, "startTime": 1751025500997, "traceId": "da2b8c02f279ed5a"}, {"name": "make", "duration": 411, "timestamp": 129769561205, "id": 130, "parentId": 129, "tags": {}, "startTime": 1751025501018, "traceId": "da2b8c02f279ed5a"}, {"name": "chunk-graph", "duration": 61, "timestamp": 129769562640, "id": 132, "parentId": 131, "tags": {}, "startTime": 1751025501020, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-modules", "duration": 15, "timestamp": 129769562800, "id": 134, "parentId": 131, "tags": {}, "startTime": 1751025501020, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-chunks", "duration": 106, "timestamp": 129769562909, "id": 135, "parentId": 131, "tags": {}, "startTime": 1751025501020, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-tree", "duration": 14, "timestamp": 129769563094, "id": 136, "parentId": 131, "tags": {}, "startTime": 1751025501020, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize-chunk-modules", "duration": 82, "timestamp": 129769563221, "id": 137, "parentId": 131, "tags": {}, "startTime": 1751025501020, "traceId": "da2b8c02f279ed5a"}, {"name": "optimize", "duration": 627, "timestamp": 129769562738, "id": 133, "parentId": 131, "tags": {}, "startTime": 1751025501020, "traceId": "da2b8c02f279ed5a"}, {"name": "module-hash", "duration": 30, "timestamp": 129769563643, "id": 138, "parentId": 131, "tags": {}, "startTime": 1751025501021, "traceId": "da2b8c02f279ed5a"}, {"name": "code-generation", "duration": 21, "timestamp": 129769563695, "id": 139, "parentId": 131, "tags": {}, "startTime": 1751025501021, "traceId": "da2b8c02f279ed5a"}, {"name": "hash", "duration": 97, "timestamp": 129769563795, "id": 140, "parentId": 131, "tags": {}, "startTime": 1751025501021, "traceId": "da2b8c02f279ed5a"}, {"name": "code-generation-jobs", "duration": 78, "timestamp": 129769563892, "id": 141, "parentId": 131, "tags": {}, "startTime": 1751025501021, "traceId": "da2b8c02f279ed5a"}, {"name": "module-assets", "duration": 35, "timestamp": 129769563951, "id": 142, "parentId": 131, "tags": {}, "startTime": 1751025501021, "traceId": "da2b8c02f279ed5a"}, {"name": "create-chunk-assets", "duration": 21, "timestamp": 129769563998, "id": 143, "parentId": 131, "tags": {}, "startTime": 1751025501021, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-js", "duration": 67, "timestamp": 129769569914, "id": 145, "parentId": 144, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751025501027, "traceId": "da2b8c02f279ed5a"}, {"name": "minify-webpack-plugin-optimize", "duration": 1277, "timestamp": 129769568720, "id": 144, "parentId": 129, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751025501026, "traceId": "da2b8c02f279ed5a"}, {"name": "css-minimizer-plugin", "duration": 10, "timestamp": 129769570091, "id": 146, "parentId": 129, "tags": {}, "startTime": 1751025501027, "traceId": "da2b8c02f279ed5a"}, {"name": "seal", "duration": 10248, "timestamp": 129769562405, "id": 131, "parentId": 129, "tags": {}, "startTime": 1751025501020, "traceId": "da2b8c02f279ed5a"}, {"name": "webpack-compilation", "duration": 14702, "timestamp": 129769558113, "id": 129, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751025501015, "traceId": "da2b8c02f279ed5a"}, {"name": "emit", "duration": 1131, "timestamp": 129769572913, "id": 147, "parentId": 13, "tags": {}, "startTime": 1751025501030, "traceId": "da2b8c02f279ed5a"}, {"name": "webpack-close", "duration": 327, "timestamp": 129769574398, "id": 148, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751025501032, "traceId": "da2b8c02f279ed5a"}]