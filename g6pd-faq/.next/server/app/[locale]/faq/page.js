(()=>{var e={};e.id=818,e.ids=[818],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1646:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eY,generateMetadata:()=>e$});var n,i,o,s,a,h,u,c=r(7413),l=r(1120),f=function(e,t){return(f=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function p(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}f(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var d=function(){return(d=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function m(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}function g(e,t){var r=t&&t.cache?t.cache:_,n=t&&t.serializer?t.serializer:y;return(t&&t.strategy?t.strategy:function(e,t){var r,n,i=1===e.length?E:b;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function E(e,t,r,n){var i=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),o=t.get(i);return void 0===o&&(o=e.call(this,n),t.set(i,o)),o}function b(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),o=t.get(i);return void 0===o&&(o=e.apply(this,n),t.set(i,o)),o}Object.create,"function"==typeof SuppressedError&&SuppressedError;var y=function(){return JSON.stringify(arguments)},v=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),_={create:function(){return new v}},T={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,b.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,E.bind(this,e,r,n)}};function A(e){return e.type===i.literal}function H(e){return e.type===i.number}function S(e){return e.type===i.date}function R(e){return e.type===i.time}function B(e){return e.type===i.select}function P(e){return e.type===i.plural}function N(e){return e.type===i.tag}function O(e){return!!(e&&"object"==typeof e&&e.type===o.number)}function I(e){return!!(e&&"object"==typeof e&&e.type===o.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(o||(o={}));var L=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,w=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,C=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,M=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,D=/^(@+)?(\+|#+)?[rs]?$/g,G=/(\*)(0+)|(#+)(0+)|(0+)/g,U=/^(0+)$/;function k(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(D,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function x(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function F(e){var t=x(e);return t||{}}var j={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},q=new RegExp("^".concat(L.source,"*")),V=new RegExp("".concat(L.source,"*$"));function X(e,t){return{start:e,end:t}}var K=!!String.prototype.startsWith&&"_a".startsWith("a",1),$=!!String.fromCodePoint,Y=!!Object.fromEntries,Z=!!String.prototype.codePointAt,W=!!String.prototype.trimStart,z=!!String.prototype.trimEnd,Q=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},J=!0;try{J=(null==(s=es("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:s[0])==="a"}catch(e){J=!1}var ee=K?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},et=$?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,o=0;i>o;){if((e=t[o++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},er=Y?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],i=n[0],o=n[1];t[i]=o}return t},en=Z?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:(i-55296<<10)+(r-56320)+65536}},ei=W?function(e){return e.trimStart()}:function(e){return e.replace(q,"")},eo=z?function(e){return e.trimEnd()}:function(e){return e.replace(V,"")};function es(e,t){return new RegExp(e,t)}if(J){var ea=es("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");a=function(e,t){var r;return ea.lastIndex=t,null!=(r=ea.exec(e)[1])?r:""}}else a=function(e,t){for(var r=[];;){var n,i=en(e,t);if(void 0===i||ec(i)||(n=i)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(i),t+=i>=65536?2:1}return et.apply(void 0,r)};var eh=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var o=[];!this.isEOF();){var s=this.char();if(123===s){var a=this.parseArgument(e,r);if(a.err)return a;o.push(a.val)}else if(125===s&&e>0)break;else if(35===s&&("plural"===t||"selectordinal"===t)){var h=this.clonePosition();this.bump(),o.push({type:i.pound,location:X(h,this.clonePosition())})}else if(60!==s||this.ignoreTag||47!==this.peek())if(60===s&&!this.ignoreTag&&eu(this.peek()||0)){var a=this.parseTag(e,t);if(a.err)return a;o.push(a.val)}else{var a=this.parseLiteral(e,t);if(a.err)return a;o.push(a.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,X(this.clonePosition(),this.clonePosition()));else break}return{val:o,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(o,"/>"),location:X(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,X(r,this.clonePosition()));var s=this.parseMessage(e+1,t,!0);if(s.err)return s;var a=s.val,h=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,X(r,this.clonePosition()));if(this.isEOF()||!eu(this.char()))return this.error(n.INVALID_TAG,X(h,this.clonePosition()));var u=this.clonePosition();return o!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,X(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:i.tag,value:o,children:a,location:X(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,X(h,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var o=this.tryParseQuote(t);if(o){n+=o;continue}var s=this.tryParseUnquoted(e,t);if(s){n+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){n+=a;continue}break}var h=X(r,this.clonePosition());return{val:{type:i.literal,value:n,location:h},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(eu(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return et.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),et(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,X(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(n.MALFORMED_ARGUMENT,X(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:o,location:X(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(r,this.clonePosition()));return this.parseArgumentOptions(e,t,o,r);default:return this.error(n.MALFORMED_ARGUMENT,X(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=a(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:X(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,s){var a,h=this.clonePosition(),u=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,X(h,c));case"number":case"date":case"time":this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var f=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=eo(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,X(this.clonePosition(),this.clonePosition()));l={style:m,styleLocation:X(f,this.clonePosition())}}var g=this.tryParseArgumentClose(s);if(g.err)return g;var E=X(s,this.clonePosition());if(l&&ee(null==l?void 0:l.style,"::",0)){var b=ei(l.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(b,l.styleLocation);if(p.err)return p;return{val:{type:i.number,value:r,location:E,style:p.val},err:null}}if(0===b.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,E);var y,v=b;this.locale&&(v=function(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if("j"===i){for(var o=0;n+1<e.length&&e.charAt(n+1)===i;)o++,n++;var s=1+(1&o),a=o<2?1:3+(o>>1),h=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(j[t||""]||j[n||""]||j["".concat(n,"-001")]||j["001"])[0]}(t);for(("H"==h||"k"==h)&&(a=0);a-- >0;)r+="a";for(;s-- >0;)r=h+r}else"J"===i?r+="H":r+=i}return r}(b,this.locale));var m={type:o.dateTime,pattern:v,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?(y={},v.replace(w,function(e){var t=e.length;switch(e[0]){case"G":y.era=4===t?"long":5===t?"narrow":"short";break;case"y":y.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":y.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":y.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":y.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"a":y.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":y.hourCycle="h12",y.hour=["numeric","2-digit"][t-1];break;case"H":y.hourCycle="h23",y.hour=["numeric","2-digit"][t-1];break;case"K":y.hourCycle="h11",y.hour=["numeric","2-digit"][t-1];break;case"k":y.hourCycle="h24",y.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":y.minute=["numeric","2-digit"][t-1];break;case"s":y.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":y.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),y):{}};return{val:{type:"date"===u?i.date:i.time,value:r,location:E,style:m},err:null}}return{val:{type:"number"===u?i.number:"date"===u?i.date:i.time,value:r,location:E,style:null!=(a=null==l?void 0:l.style)?a:null},err:null};case"plural":case"selectordinal":case"select":var _=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,X(_,d({},_)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),A=0;if("select"!==u&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,X(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),T=this.parseIdentifierIfPossible(),A=p.val}var H=this.tryParsePluralOrSelectOptions(e,u,t,T);if(H.err)return H;var g=this.tryParseArgumentClose(s);if(g.err)return g;var S=X(s,this.clonePosition());if("select"===u)return{val:{type:i.select,value:r,options:er(H.val),location:S},err:null};return{val:{type:i.plural,value:r,options:er(H.val),offset:A,pluralType:"plural"===u?"cardinal":"ordinal",location:S},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,X(h,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,X(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(C).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var i=t[n].split("/");if(0===i.length)throw Error("Invalid number skeleton");for(var o=i[0],s=i.slice(1),a=0;a<s.length;a++)if(0===s[a].length)throw Error("Invalid number skeleton");r.push({stem:o,options:s})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:o.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=d(d(d({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return d(d({},e),F(t))},{}));continue;case"engineering":t=d(d(d({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return d(d({},e),F(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(G,function(e,r,n,i,o,s){if(r)t.minimumIntegerDigits=n.length;else if(i&&o)throw Error("We currently do not support maximum integer digits");else if(s)throw Error("We currently do not support exact integer digits");return""});continue}if(U.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(M.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(M,function(e,r,n,i,o,s){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&s?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+s.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var i=n.options[0];"w"===i?t=d(d({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=d(d({},t),k(i)));continue}if(D.test(n.stem)){t=d(d({},t),k(n.stem));continue}var o=x(n.stem);o&&(t=d(d({},t),o));var s=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!U.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);s&&(t=d(d({},t),s))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,i){for(var o,s=!1,a=[],h=new Set,u=i.value,c=i.location;;){if(0===u.length){var l=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=X(l,this.clonePosition()),u=this.message.slice(l.offset,this.offset())}else break}if(h.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===u&&(s=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,X(this.clonePosition(),this.clonePosition()));var d=this.parseMessage(e+1,t,r);if(d.err)return d;var m=this.tryParseArgumentClose(p);if(m.err)return m;a.push([u,{value:d.val,location:X(p,this.clonePosition())}]),h.add(u),this.bumpSpace(),u=(o=this.parseIdentifierIfPossible()).value,c=o.location}return 0===a.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,X(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(n.MISSING_OTHER_CLAUSE,X(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,o=0;!this.isEOF();){var s=this.char();if(s>=48&&s<=57)i=!0,o=10*o+(s-48),this.bump();else break}var a=X(n,this.clonePosition());return i?Q(o*=r)?{val:o,err:null}:this.error(t,a):this.error(e,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=en(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(ee(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&ec(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function eu(e){return e>=97&&e<=122||e>=65&&e<=90}function ec(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function el(e,t){void 0===t&&(t={});var r=new eh(e,t=d({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var i=SyntaxError(n[r.err.kind]);throw i.location=r.err.location,i.originalMessage=r.err.message,i}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,B(t)||P(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else H(t)&&O(t.style)||(S(t)||R(t))&&I(t.style)?delete t.style.location:N(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(h||(h={}));var ef=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return p(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ep=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),h.INVALID_VALUE,i)||this}return p(t,e),t}(ef),ed=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),h.INVALID_VALUE,n)||this}return p(t,e),t}(ef),em=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),h.MISSING_VALUE,r)||this}return p(t,e),t}(ef);function eg(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var eE=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var s,a,c=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=c.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return function e(t,r,n,o,s,a,c){if(1===t.length&&A(t[0]))return[{type:u.literal,value:t[0].value}];for(var l=[],f=0;f<t.length;f++){var p=t[f];if(A(p)){l.push({type:u.literal,value:p.value});continue}if(p.type===i.pound){"number"==typeof a&&l.push({type:u.literal,value:n.getNumberFormat(r).format(a)});continue}var d=p.value;if(!(s&&d in s))throw new em(d,c);var m=s[d];if(p.type===i.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),l.push({type:"string"==typeof m?u.literal:u.object,value:m});continue}if(S(p)){var g="string"==typeof p.style?o.date[p.style]:I(p.style)?p.style.parsedOptions:void 0;l.push({type:u.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(R(p)){var g="string"==typeof p.style?o.time[p.style]:I(p.style)?p.style.parsedOptions:o.time.medium;l.push({type:u.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(H(p)){var g="string"==typeof p.style?o.number[p.style]:O(p.style)?p.style.parsedOptions:void 0;g&&g.scale&&(m*=g.scale||1),l.push({type:u.literal,value:n.getNumberFormat(r,g).format(m)});continue}if(N(p)){var E=p.children,b=p.value,y=s[b];if("function"!=typeof y)throw new ed(b,"function",c);var v=y(e(E,r,n,o,s,a).map(function(e){return e.value}));Array.isArray(v)||(v=[v]),l.push.apply(l,v.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(B(p)){var _=p.options[m]||p.options.other;if(!_)throw new ep(p.value,m,Object.keys(p.options),c);l.push.apply(l,e(_.value,r,n,o,s));continue}if(P(p)){var _=p.options["=".concat(m)];if(!_){if(!Intl.PluralRules)throw new ef('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',h.MISSING_INTL_API,c);var T=n.getPluralRules(r,{type:p.pluralType}).select(m-(p.offset||0));_=p.options[T]||p.options.other}if(!_)throw new ep(p.value,m,Object.keys(p.options),c);l.push.apply(l,e(_.value,r,n,o,s,m-(p.offset||0)));continue}}return l.length<2?l:l.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}(c.ast,c.locales,c.formatters,c.formats,e,void 0,c.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=c.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(c.locales)[0]}},this.getAst=function(){return c.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var l=o||{},f=(l.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}(l,["formatters"]));this.ast=e.__parse(t,d(d({},f),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(s=e.formats,n?Object.keys(s).reduce(function(e,t){var r,i;return e[t]=(r=s[t],(i=n[t])?d(d(d({},r||{}),i||{}),Object.keys(r).reduce(function(e,t){return e[t]=d(d({},r[t]),i[t]||{}),e},{})):r),e},d({},s)):s),this.formatters=o&&o.formatters||(void 0===(a=this.formatterCache)&&(a={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:g(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,m([void 0],t,!1)))},{cache:eg(a.number),strategy:T.variadic}),getDateTimeFormat:g(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,m([void 0],t,!1)))},{cache:eg(a.dateTime),strategy:T.variadic}),getPluralRules:g(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,m([void 0],t,!1)))},{cache:eg(a.pluralRules),strategy:T.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=el,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();class eb extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),this.code=e,t&&(this.originalMessage=t)}}var ey=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(ey||{});function ev(...e){return e.filter(Boolean).join(".")}function e_(e){return ev(e.namespace,e.key)}function eT(e){console.error(e)}function eA(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function eH(e,t){return g(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:T.variadic})}function eS(e,t){return eH((...t)=>new e(...t),t)}function eR(e){return{getDateTimeFormat:eS(Intl.DateTimeFormat,e.dateTime),getNumberFormat:eS(Intl.NumberFormat,e.number),getPluralRules:eS(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:eS(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:eS(Intl.ListFormat,e.list),getDisplayNames:eS(Intl.DisplayNames,e.displayNames)}}function eB(e,t,r,n){let i=ev(n,r);if(!t)throw Error(i);let o=t;return r.split(".").forEach(t=>{let r=o[t];if(null==t||null==r)throw Error(i+` (${e})`);o=r}),o}function eP(e){return e.includes("[[...")}function eN(e){return e.includes("[...")}function eO(e){return e.includes("[")}function eI(e){return"function"==typeof e.then}r(9933);var eL=r(6280);r(6294);let ew=(0,l.cache)(function(){return{locale:void 0}}),eC=(0,l.cache)(async function(){let e=(0,eL.b)();return eI(e)?await e:e}),eM=(0,l.cache)(async function(){let e;try{e=(await eC()).get("X-NEXT-INTL-LOCALE")||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function eD(){return ew().locale||await eM()}var eG=r(9916);let eU=["zh","en"],ek=async({locale:e})=>(eU.includes(e)||(0,eG.notFound)(),{locale:e,messages:(await r(6565)(`./${e}.json`)).default}),ex=(0,l.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),eF=(0,l.cache)(async function(e,t){let r=e({locale:t,get requestLocale(){return t?Promise.resolve(t):eD()}});if(eI(r)&&(r=await r),!r.locale)throw Error("No locale was returned from `getRequestConfig`.\n\nSee https://next-intl.dev/docs/usage/configuration#i18n-request");return r}),ej=(0,l.cache)(eR),eq=(0,l.cache)(eA),eV=(0,l.cache)(async function(e){let t=await eF(ek,e);return{...function({formats:e,getMessageFallback:t,messages:r,onError:n,...i}){return{...i,formats:e||void 0,messages:r||void 0,onError:n||eT,getMessageFallback:t||e_}}(t),_formatters:ej(eq()),timeZone:t.timeZone||ex()}});var eX=(0,l.cache)(function(e,t){return function({_cache:e=eA(),_formatters:t=eR(e),getMessageFallback:r=e_,messages:n,namespace:i,onError:o=eT,...s}){return function({messages:e,namespace:t,...r},n){var i;return e=e["!"],t="!"===(i=t)?void 0:i.slice((n+".").length),function(e){let t=function(e,t,r,n=eT){try{if(!t)throw Error(void 0);let n=r?eB(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new eb(ey.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function({cache:e,formats:t,formatters:r,getMessageFallback:n=e_,locale:i,messagesOrError:o,namespace:s,onError:a,timeZone:h}){let u=o instanceof eb;function c(e,t,r){let i=new eb(t,r);return a(i),n({error:i,key:e,namespace:s})}function f(a,f,p){var d;let m,g;if(u)return n({error:o,key:a,namespace:s});try{m=eB(i,o,a,s)}catch(e){return c(a,ey.MISSING_MESSAGE,e.message)}if("object"==typeof m){let e;return c(a,Array.isArray(m)?ey.INVALID_MESSAGE:ey.INSUFFICIENT_PATH,e)}let E=(d=m,f?void 0:d);if(E)return E;r.getMessageFormat||(r.getMessageFormat=eH((...e)=>new eE(e[0],e[1],e[2],{formatters:r,...e[3]}),e.message));try{g=r.getMessageFormat(m,i,function(e,t,r){let n=eE.formats.date,i=eE.formats.time,o={...e?.dateTime,...t?.dateTime},s={date:{...n,...o},time:{...i,...o},number:{...e?.number,...t?.number}};return r&&["date","time"].forEach(e=>{let t=s[e];for(let[e,n]of Object.entries(t))t[e]={timeZone:r,...n}}),s}(t,p,h),{formatters:{...r,getDateTimeFormat:(e,t)=>r.getDateTimeFormat(e,{timeZone:h,...t})}})}catch(e){return c(a,ey.INVALID_MESSAGE,e.message)}try{let e=g.format(f?function(e){let t={};return Object.keys(e).forEach(r=>{let n,i=0,o=e[r];n="function"==typeof o?e=>{let t=o(e);return(0,l.isValidElement)(t)?(0,l.cloneElement)(t,{key:r+i++}):t}:o,t[r]=n}),t}(f):f);if(null==e)throw Error(void 0);return(0,l.isValidElement)(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return c(a,ey.FORMATTING_ERROR,e.message)}}function p(e,t,r){let n=f(e,t,r);return"string"!=typeof n?c(e,ey.INVALID_MESSAGE,void 0):n}return p.rich=f,p.markup=(e,t,r)=>f(e,t,r),p.raw=e=>{if(u)return n({error:o,key:e,namespace:s});try{return eB(i,o,e,s)}catch(t){return c(e,ey.MISSING_MESSAGE,t.message)}},p.has=e=>{if(u)return!1;try{return eB(i,o,e,s),!0}catch{return!1}},p}({...e,messagesOrError:t})}({...r,messages:e,namespace:t})}({...s,onError:o,cache:e,formatters:t,getMessageFallback:r,messages:{"!":n},namespace:i?`!.${i}`:"!"},"!")}({...e,namespace:t})}),eK=(0,l.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),eX(await eV(r),t)});async function e$({params:e}){let{locale:t}=await e,r=await eK({locale:t});return{title:r("faq.title"),description:r("faq.subtitle"),openGraph:{title:r("faq.title"),description:r("faq.subtitle"),type:"website"}}}async function eY({params:e}){let{locale:t}=await e,r=await eK({locale:t});return(0,c.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,c.jsx)("div",{className:"bg-white shadow-sm",children:(0,c.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:r("faq.title")}),(0,c.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:r("faq.subtitle")})]})})}),(0,c.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8",children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"FAQ页面开发中"}),(0,c.jsx)("p",{className:"text-gray-600",children:"FAQ功能正在开发中，敬请期待..."})]})})]})}},2434:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>f,tree:()=>u});var n=r(5239),i=r(8088),o=r(8170),s=r.n(o),a=r(893),h={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(h[e]=()=>a[e]);r.d(t,h);let u={children:["",{children:["[locale]",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1646)),"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1434)),"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/faq/page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/faq/page",pathname:"/[locale]/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},2584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return o},ReadonlyHeadersError:function(){return i}});let n=r(3763);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,i);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==s)return n.ReflectAdapter.get(t,s,i)},set(t,r,i,o){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,i,o);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);return n.ReflectAdapter.set(t,a??r,i,o)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==o&&n.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===o||n.ReflectAdapter.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return h},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return d},getModifiedCookieValues:function(){return c},responseCookiesToRequestCookies:function(){return g},wrapWithMutableAccessCheck:function(){return p}});let n=r(3158),i=r(3763),o=r(9294),s=r(3033);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new a}}class h{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function c(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function l(e,t){let r=c(t);if(0===r.length)return!1;let i=new n.ResponseCookies(e),o=i.getAll();for(let e of r)i.set(e);for(let e of o)i.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let s=[],a=new Set,h=()=>{let e=o.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),s=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of s){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},c=new Proxy(r,{get(e,t,r){switch(t){case u:return s;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{h()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{h()}};default:return i.ReflectAdapter.get(e,t,r)}}});return c}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return i.ReflectAdapter.get(e,r,n)}}});return t}function d(e){return"action"===e.phase}function m(e){if(!d((0,s.getExpectedRequestStore)(e)))throw new a}function g(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},6280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return l}});let n=r(2584),i=r(9294),o=r(3033),s=r(4971),a=r(23),h=r(8388),u=r(6926),c=(r(4523),r(8719));function l(){let e=i.workAsyncStorage.getStore(),t=o.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,u=t;let n=f.get(u);if(n)return n;let i=(0,h.makeHangingPromise)(u.renderSignal,"`headers()`");return f.set(u,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${d(arguments[0])}, ...)\``,t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},delete:{value:function(){let e=`\`headers().delete(${d(arguments[0])})\``,t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},get:{value:function(){let e=`\`headers().get(${d(arguments[0])})\``,t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},has:{value:function(){let e=`\`headers().has(${d(arguments[0])})\``,t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},set:{value:function(){let e=`\`headers().set(${d(arguments[0])}, ...)\``,t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},keys:{value:function(){let e="`headers().keys()`",t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},values:{value:function(){let e="`headers().values()`",t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},entries:{value:function(){let e="`headers().entries()`",t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=g(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}}}),i}else"prerender-ppr"===t.type?(0,s.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,s.throwToInterruptStaticGeneration)("headers",e,t);(0,s.trackDynamicDataInDynamicRender)(e,t)}return p((0,o.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function d(e){return"string"==typeof e?`'${e}'`:"..."}let m=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},6294:(e,t,r)=>{"use strict";let n=r(3033),i=r(9294),o=r(4971),s=r(6926),a=r(23),h=r(8479);function u(){let e=i.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return c(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return c(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function c(e,t){let r,n=l.get(u);return n||(r=f(e),l.set(e,r),r)}let l=new WeakMap;function f(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let d=(0,s.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=i.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,o.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,o.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new h.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},6565:(e,t,r)=>{var n={"./en.json":[7368,368],"./zh.json":[2961,961]};function i(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],i=t[0];return r.e(t[1]).then(()=>r.t(i,19))}i.keys=()=>Object.keys(n),i.id=6565,e.exports=i},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9933:(e,t,r)=>{"use strict";let n=r(4069),i=r(3158),o=r(9294),s=r(3033),a=r(4971),h=r(23),u=r(8388),c=r(6926),l=(r(4523),r(8719)),f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):E.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):b.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function E(){return this.getAll().map(e=>[e.name,e]).values()}function b(e){for(let e of this.getAll())this.delete(e.name);return e}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,825,658,112],()=>r(2434));module.exports=n})();