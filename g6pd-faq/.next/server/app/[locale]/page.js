(()=>{var e={};e.id=465,e.ids=[465],e.modules={376:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},486:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1434:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,generateMetadata:()=>i,generateStaticParams:()=>a});var s=r(7413);let n=["zh","en"];function a(){return n.map(e=>({locale:e}))}async function i({params:e}){let{locale:t}=await e,r="zh"===t?"G6PD缺乏症（蚕豆病）FAQ":"G6PD Deficiency FAQ",s="zh"===t?"为G6PD缺乏症患者及家属提供权威、全面的FAQ信息，包括用药指导、饮食建议、症状识别等专业内容。":"Comprehensive FAQ for G6PD deficiency patients and families";return{title:{template:`%s | ${r}`,default:r},description:s,openGraph:{title:r,description:s,type:"website",locale:t,alternateLocale:n.filter(e=>e!==t)},alternates:{languages:{zh:"/zh",en:"/en"}}}}async function o({children:e,params:t}){let{locale:r}=await t;return(0,s.jsx)("html",{lang:r,dir:"ar"===r?"rtl":"ltr",children:(0,s.jsx)("body",{children:(0,s.jsx)("div",{className:"min-h-screen",children:e})})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(7413);function n({children:e}){return(0,s.jsx)("html",{children:(0,s.jsx)("body",{children:e})})}r(1135)},4746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),n=r(8088),a=r(8170),i=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9697)),"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,1434)),"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/[locale]/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6487:()=>{},6824:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8335:()=>{},8638:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9697:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(7413);async function n({params:e}){let{locale:t}=await e;return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6 text-gray-900",children:"G6PD缺乏症（蚕豆病）专业指导"}),(0,s.jsx)("p",{className:"text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-gray-600",children:"为患者及家属提供权威、实用的医疗信息和生活指导"}),(0,s.jsxs)("p",{className:"text-lg text-gray-500",children:["当前语言: ",t]}),(0,s.jsx)("div",{className:"mt-8 p-4 bg-green-100 rounded-lg",children:(0,s.jsx)("p",{className:"text-green-800 font-semibold",children:"✅ 页面成功渲染 - 路由系统正常工作"})})]})})})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,825,658],()=>r(4746));module.exports=s})();