{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a1cefb38._.js", "server/edge/chunks/[root-of-the-server]__a22575ff._.js", "server/edge/chunks/edge-wrapper_e39a16aa.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(zh|en)/:path*{(\\\\.json)}?", "originalSource": "/(zh|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LIXoHCsdOMe9aFsvsMkvZ+AYBGyeWeYcHcdaq7dka8Q=", "__NEXT_PREVIEW_MODE_ID": "a087278588070e8572b9f04faf705de5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a2cec68a3eb49fdeaa6a0a37ac1902121c25f9900161722979200aa972121b2f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2fd75b85f93e824b1f5fe8219ab5b6837246735430771c7fcf6b5dea18bbd80e"}}}, "instrumentation": null, "functions": {}}