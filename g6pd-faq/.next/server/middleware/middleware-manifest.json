{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a1cefb38._.js", "server/edge/chunks/[root-of-the-server]__a22575ff._.js", "server/edge/chunks/edge-wrapper_e39a16aa.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HFu4iGw/muDmA0av5NaGC+G92NwsADNCmL6VKGGgWEo=", "__NEXT_PREVIEW_MODE_ID": "93afe5946d607efe85edf828c4bbd544", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fa36bcbb1b670ceea2ef698f8c11c32afa70fc1a6e3d5a04c2544577d30c85e4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4c773bf9f049dee60a3b8f909b945d5e4bc9e9d4e0e9835f2820a6658a98c334"}}}, "instrumentation": null, "functions": {}}