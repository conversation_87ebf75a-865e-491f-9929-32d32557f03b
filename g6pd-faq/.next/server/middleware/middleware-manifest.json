{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a1cefb38._.js", "server/edge/chunks/[root-of-the-server]__a22575ff._.js", "server/edge/chunks/edge-wrapper_e39a16aa.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HFu4iGw/muDmA0av5NaGC+G92NwsADNCmL6VKGGgWEo=", "__NEXT_PREVIEW_MODE_ID": "3d8032cb38115868f76db5b82bdc356c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b7b9d2649228c5bb1a02bf23d1d487ea2394d9c3408094c388ecfbd90bb5e815", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a1099a00b4b5a327f997c78e789ed7df798fb6034e668c95b64b385d6c17576c"}}}, "instrumentation": null, "functions": {}}