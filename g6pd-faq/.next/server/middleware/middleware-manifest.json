{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a1cefb38._.js", "server/edge/chunks/[root-of-the-server]__a22575ff._.js", "server/edge/chunks/edge-wrapper_e39a16aa.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HFu4iGw/muDmA0av5NaGC+G92NwsADNCmL6VKGGgWEo=", "__NEXT_PREVIEW_MODE_ID": "5ddd7add1f1bb769e7d001b4933e372a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6ca3f07feb9a303919a8cb10f75f8f7dd13866a99be0b05ed021805e05206422", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f8d8842ae0d30165ff4d34bc11b6fbb169ca941ec42b63319be30223e0fc2116"}}}, "instrumentation": null, "functions": {}}