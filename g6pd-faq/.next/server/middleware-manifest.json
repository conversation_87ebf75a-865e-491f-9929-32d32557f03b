{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1cefb38._.js", "server/edge/chunks/[root-of-the-server]__a22575ff._.js", "server/edge/chunks/edge-wrapper_e39a16aa.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LIXoHCsdOMe9aFsvsMkvZ+AYBGyeWeYcHcdaq7dka8Q=", "__NEXT_PREVIEW_MODE_ID": "8c6c8fb89369d8569be0ee9323f7951d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1a33806635c5859ca2664a71a2f72531d5720cc404f87b06f5ed8e23d1738418", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8625a719da9ccf4fd499891edc069596378bd0802cb774060390af6dccda3d01"}}}, "sortedMiddleware": ["/"], "functions": {}}