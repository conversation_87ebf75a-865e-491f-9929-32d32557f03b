{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1cefb38._.js", "server/edge/chunks/[root-of-the-server]__a22575ff._.js", "server/edge/chunks/edge-wrapper_e39a16aa.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(zh|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(zh|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LIXoHCsdOMe9aFsvsMkvZ+AYBGyeWeYcHcdaq7dka8Q=", "__NEXT_PREVIEW_MODE_ID": "3949235bea5eba65f73fa4d001486a84", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1d43e127b5df36290f44e1fb2452cca5a1b8f04006e3ae237a8bd59d5fedb6d7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1b66cd790ea3ec96bce136d324370d24ef8075bdf5c515fcfc3ed1a62e8fa34b"}}}, "sortedMiddleware": ["/"], "functions": {}}