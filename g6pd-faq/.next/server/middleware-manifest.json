{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1cefb38._.js", "server/edge/chunks/[root-of-the-server]__a22575ff._.js", "server/edge/chunks/edge-wrapper_e39a16aa.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HFu4iGw/muDmA0av5NaGC+G92NwsADNCmL6VKGGgWEo=", "__NEXT_PREVIEW_MODE_ID": "825d07a992c92222f8b05395f5944abf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9fce762801fee69f761edb49925053d3bf8016ed70cbd0bf1c260568aecea76c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4b993d55f329ecfeeea2a67ec59bbfc3a35c0281b4079fa8e774bbe76193b76f"}}}, "sortedMiddleware": ["/"], "functions": {}}