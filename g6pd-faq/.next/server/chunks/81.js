exports.id=81,exports.ids=[81],exports.modules={376:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},3096:(e,s,t)=>{Promise.resolve().then(t.bind(t,5196)),Promise.resolve().then(t.t.bind(t,5814,23)),Promise.resolve().then(t.bind(t,4078))},3774:()=>{},4038:()=>{},4078:(e,s,t)=>{"use strict";t.d(s,{default:()=>g});var r=t(687),a=t(3210),i=t(5814),l=t.n(i),n=t(8521),c=t(7618),d=t(6189),o=t(1836),m=t(6510),h=t(143),x=t(7010);let f=[{code:"zh",name:"中文",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"}],p=function(){let e=(0,n.Ym)(),s=(0,d.useRouter)(),t=(0,d.usePathname)(),[i,l]=(0,a.useState)(!1),c=f.find(s=>s.code===e),o=r=>{let a=t.replace(`/${e}`,"");s.push(`/${r}${a}`),l(!1)};return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>l(!i),className:"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md","aria-expanded":i,"aria-haspopup":"true",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"hidden sm:inline",children:[c?.flag," ",c?.name]}),(0,r.jsx)("span",{className:"sm:hidden",children:c?.flag}),(0,r.jsx)(x.A,{className:`h-4 w-4 transition-transform ${i?"rotate-180":""}`})]}),i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>l(!1)}),(0,r.jsx)("div",{className:"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5",children:(0,r.jsx)("div",{className:"py-1",role:"menu",children:f.map(s=>(0,r.jsxs)("button",{onClick:()=>o(s.code),className:`flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100 ${e===s.code?"bg-blue-50 text-blue-700":"text-gray-700"}`,role:"menuitem",children:[(0,r.jsx)("span",{className:"mr-3 text-lg",children:s.flag}),(0,r.jsx)("span",{children:s.name}),e===s.code&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},s.code))})})]})]})},g=function(){let e=(0,n.Ym)(),s=(0,c.c3)("navigation"),t=(0,d.usePathname)(),[i,h]=(0,a.useState)(!1),x=[{href:`/${e}`,label:s("home")},{href:`/${e}/faq`,label:s("faq"),children:[{href:`/${e}/faq/medications`,label:s("medications")},{href:`/${e}/faq/diet`,label:s("diet")},{href:`/${e}/faq/symptoms`,label:s("symptoms")},{href:`/${e}/faq/treatment`,label:s("treatment")}]},{href:`/${e}/medications`,label:s("medications"),children:[{href:`/${e}/medications/chinese-medicine`,label:s("chineseMedicine")},{href:`/${e}/medications/oral-solutions`,label:s("oralSolutions")},{href:`/${e}/medications/western-medicine`,label:s("westernMedicine")}]},{href:`/${e}/search`,label:s("search")},{href:`/${e}/about`,label:s("about")}],f=s=>s===`/${e}`?t===s:t.startsWith(s);return(0,r.jsxs)("nav",{className:"bg-white shadow-sm border-b",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsx)(l(),{href:`/${e}`,className:"text-xl font-bold text-blue-600",children:"G6PD FAQ"})}),(0,r.jsx)("div",{className:"hidden md:ml-6 md:flex md:space-x-8",children:x.map(e=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(l(),{href:e.href,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${f(e.href)?"border-blue-500 text-gray-900":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}`,children:e.label}),e.children&&(0,r.jsx)("div",{className:"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50",children:(0,r.jsx)("div",{className:"py-1",children:e.children.map(e=>(0,r.jsx)(l(),{href:e.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:e.label},e.href))})})]},e.href))})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(p,{}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("button",{onClick:()=>h(!i),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",children:i?(0,r.jsx)(o.A,{className:"h-6 w-6"}):(0,r.jsx)(m.A,{className:"h-6 w-6"})})})]})]})}),i&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("div",{className:"pt-2 pb-3 space-y-1",children:x.map(e=>(0,r.jsxs)("div",{children:[(0,r.jsx)(l(),{href:e.href,className:`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${f(e.href)?"bg-blue-50 border-blue-500 text-blue-700":"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700"}`,onClick:()=>h(!1),children:e.label}),e.children&&(0,r.jsx)("div",{className:"pl-6",children:e.children.map(e=>(0,r.jsx)(l(),{href:e.href,className:"block pl-3 pr-4 py-2 text-sm text-gray-600 hover:text-gray-900",onClick:()=>h(!1),children:e.label},e.href))})]},e.href))})})]})}},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>l});var r=t(7413),a=t(1001),i=t.n(a);t(1135);let l={title:"G6PD Deficiency FAQ",description:"Comprehensive FAQ for G6PD deficiency patients and families"};function n({children:e}){return(0,r.jsx)("html",{children:(0,r.jsx)("body",{className:`${i().variable} font-sans antialiased`,children:e})})}},4544:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});var r=t(2907);(0,r.registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx","Navigation");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx","default")},6248:(e,s,t)=>{Promise.resolve().then(t.bind(t,994)),Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,4544))},6470:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g,generateMetadata:()=>p,generateStaticParams:()=>f});var r=t(7413),a=t(8946),i=t(3930),l=t(9916),n=t(4544),c=t(563),d=t(4536),o=t.n(d),m=t(3685);let h=function(){let e=(0,c.A)("footer"),s=(0,m.A)(),t=new Date().getFullYear();return(0,r.jsx)("footer",{className:"bg-gray-50 border-t",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"G6PD FAQ"})}),(0,r.jsx)("p",{className:"mt-4 text-sm text-gray-600 max-w-md",children:"zh"===s?"为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导。":"Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase",children:"zh"===s?"快速链接":"Quick Links"}),(0,r.jsxs)("ul",{className:"mt-4 space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/faq`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"常见问题":"FAQ"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/medications`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"用药指导":"Medications"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/search`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"搜索":"Search"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/about`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"关于我们":"About"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase",children:"zh"===s?"分类":"Categories"}),(0,r.jsxs)("ul",{className:"mt-4 space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/faq/medications`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"用药相关":"Medications"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/faq/diet`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"饮食相关":"Diet"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/faq/symptoms`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"症状相关":"Symptoms"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:`/${s}/faq/treatment`,className:"text-sm text-gray-600 hover:text-gray-900",children:"zh"===s?"治疗相关":"Treatment"})})]})]})]}),(0,r.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-800",children:"zh"===s?"紧急情况：如出现急性溶血症状（黄疸、深色尿液、疲劳等），请立即就医！":"Emergency: If you experience acute hemolysis symptoms (jaundice, dark urine, fatigue), seek immediate medical attention!"})})]})})}),(0,r.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex space-x-6",children:[(0,r.jsx)(o(),{href:`/${s}/privacy`,className:"text-sm text-gray-600 hover:text-gray-900",children:e("links.privacy")}),(0,r.jsx)(o(),{href:`/${s}/terms`,className:"text-sm text-gray-600 hover:text-gray-900",children:e("links.terms")}),(0,r.jsx)(o(),{href:`/${s}/contact`,className:"text-sm text-gray-600 hover:text-gray-900",children:e("links.contact")})]}),(0,r.jsx)("div",{className:"mt-4 md:mt-0",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e("copyright")," \xa9 ",t]})})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("p",{className:"text-xs text-gray-500 text-center",children:e("disclaimer")})})]})]})})},x=["zh","en"];function f(){return x.map(e=>({locale:e}))}async function p({params:{locale:e}}){let s=await (0,i.A)({locale:e}),t=s?.site?.title||"G6PD Deficiency FAQ",r=s?.site?.description||"Comprehensive FAQ for G6PD deficiency patients and families";return{title:{template:`%s | ${t}`,default:t},description:r,keywords:s?.site?.keywords,openGraph:{title:t,description:r,type:"website",locale:e,alternateLocale:x.filter(s=>s!==e)},alternates:{languages:{zh:"/zh",en:"/en"}}}}async function g({children:e,params:{locale:s}}){x.includes(s)||(0,l.notFound)();let t=await (0,i.A)({locale:s});return(0,r.jsx)("html",{lang:s,dir:"ar"===s?"rtl":"ltr",children:(0,r.jsx)("body",{children:(0,r.jsx)(a.A,{messages:t,children:(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)(h,{})]})})})})}},6824:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))}};