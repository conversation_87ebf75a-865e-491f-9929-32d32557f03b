{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/contact/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\ninterface ContactPageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: ContactPageProps): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  return {\n    title: isZh ? '联系我们 - G6PD缺乏症FAQ' : 'Contact Us - G6PD Deficiency FAQ',\n    description: isZh \n      ? '联系G6PD Guide团队。我们欢迎您的问题、建议和反馈，帮助我们改进网站服务。'\n      : 'Contact the G6PD Guide team. We welcome your questions, suggestions, and feedback to help us improve our website services.',\n  }\n}\n\nexport default async function ContactPage({ params }: ContactPageProps) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">\n            {isZh ? '联系我们' : 'Contact Us'}\n          </h1>\n          \n          <div className=\"prose prose-lg max-w-none\">\n            {isZh ? (\n              <>\n                <p className=\"text-gray-700 mb-8\">\n                  我们非常重视您的意见和建议。如果您有任何问题、反馈或需要帮助，请通过以下方式联系我们。\n                </p>\n\n                <div className=\"grid md:grid-cols-2 gap-8 mb-8\">\n                  <div className=\"bg-blue-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-blue-900 mb-4\">一般咨询</h3>\n                    <p className=\"text-blue-800 mb-2\">\n                      <strong>邮箱：</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-blue-700 text-sm\">\n                      网站使用问题、内容咨询、一般性问题\n                    </p>\n                  </div>\n\n                  <div className=\"bg-green-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-green-900 mb-4\">意见反馈</h3>\n                    <p className=\"text-green-800 mb-2\">\n                      <strong>邮箱：</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-green-700 text-sm\">\n                      网站改进建议、功能需求、用户体验反馈\n                    </p>\n                  </div>\n\n                  <div className=\"bg-purple-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-purple-900 mb-4\">内容纠错</h3>\n                    <p className=\"text-purple-800 mb-2\">\n                      <strong>邮箱：</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-purple-700 text-sm\">\n                      医学信息纠错、内容更新建议、专业意见\n                    </p>\n                  </div>\n\n                  <div className=\"bg-orange-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-orange-900 mb-4\">合作洽谈</h3>\n                    <p className=\"text-orange-800 mb-2\">\n                      <strong>邮箱：</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-orange-700 text-sm\">\n                      医疗机构合作、学术交流、内容合作\n                    </p>\n                  </div>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">常见问题</h2>\n                <div className=\"space-y-4 mb-8\">\n                  <div className=\"border-l-4 border-blue-400 pl-4\">\n                    <h4 className=\"font-semibold text-gray-900\">Q: 网站信息是否可以替代医生建议？</h4>\n                    <p className=\"text-gray-700\">A: 不可以。本网站信息仅供参考，任何医疗决定都应咨询专业医生。</p>\n                  </div>\n                  <div className=\"border-l-4 border-green-400 pl-4\">\n                    <h4 className=\"font-semibold text-gray-900\">Q: 如何确保信息的准确性？</h4>\n                    <p className=\"text-gray-700\">A: 我们基于权威医学文献和临床指南编写内容，并定期更新。</p>\n                  </div>\n                  <div className=\"border-l-4 border-purple-400 pl-4\">\n                    <h4 className=\"font-semibold text-gray-900\">Q: 可以转载网站内容吗？</h4>\n                    <p className=\"text-gray-700\">A: 个人非商业用途可以引用，但需注明来源。商业用途请联系我们。</p>\n                  </div>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">响应时间</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  我们承诺在收到您的邮件后：\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>一般咨询：1-2个工作日内回复</li>\n                  <li>紧急内容纠错：24小时内处理</li>\n                  <li>功能反馈：3-5个工作日内回复</li>\n                  <li>合作洽谈：5-7个工作日内回复</li>\n                </ul>\n\n                <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4\">\n                  <p className=\"text-yellow-800\">\n                    <strong>紧急医疗情况：</strong>如果您遇到紧急医疗情况，请立即就医或拨打急救电话，不要依赖邮件联系。\n                  </p>\n                </div>\n              </>\n            ) : (\n              <>\n                <p className=\"text-gray-700 mb-8\">\n                  We value your opinions and suggestions. If you have any questions, feedback, or need assistance, please contact us through the following methods.\n                </p>\n\n                <div className=\"grid md:grid-cols-2 gap-8 mb-8\">\n                  <div className=\"bg-blue-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-blue-900 mb-4\">General Inquiries</h3>\n                    <p className=\"text-blue-800 mb-2\">\n                      <strong>Email:</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-blue-700 text-sm\">\n                      Website usage issues, content inquiries, general questions\n                    </p>\n                  </div>\n\n                  <div className=\"bg-green-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-green-900 mb-4\">Feedback</h3>\n                    <p className=\"text-green-800 mb-2\">\n                      <strong>Email:</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-green-700 text-sm\">\n                      Website improvement suggestions, feature requests, user experience feedback\n                    </p>\n                  </div>\n\n                  <div className=\"bg-purple-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-purple-900 mb-4\">Content Corrections</h3>\n                    <p className=\"text-purple-800 mb-2\">\n                      <strong>Email:</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-purple-700 text-sm\">\n                      Medical information corrections, content update suggestions, professional opinions\n                    </p>\n                  </div>\n\n                  <div className=\"bg-orange-50 p-6 rounded-lg\">\n                    <h3 className=\"text-xl font-semibold text-orange-900 mb-4\">Partnerships</h3>\n                    <p className=\"text-orange-800 mb-2\">\n                      <strong>Email:</strong> <EMAIL>\n                    </p>\n                    <p className=\"text-orange-700 text-sm\">\n                      Medical institution partnerships, academic exchanges, content collaboration\n                    </p>\n                  </div>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">Frequently Asked Questions</h2>\n                <div className=\"space-y-4 mb-8\">\n                  <div className=\"border-l-4 border-blue-400 pl-4\">\n                    <h4 className=\"font-semibold text-gray-900\">Q: Can website information replace doctor&apos;s advice?</h4>\n                    <p className=\"text-gray-700\">A: No. Website information is for reference only. Any medical decisions should consult professional doctors.</p>\n                  </div>\n                  <div className=\"border-l-4 border-green-400 pl-4\">\n                    <h4 className=\"font-semibold text-gray-900\">Q: How do you ensure information accuracy?</h4>\n                    <p className=\"text-gray-700\">A: We base content on authoritative medical literature and clinical guidelines, with regular updates.</p>\n                  </div>\n                  <div className=\"border-l-4 border-purple-400 pl-4\">\n                    <h4 className=\"font-semibold text-gray-900\">Q: Can I reprint website content?</h4>\n                    <p className=\"text-gray-700\">A: Personal non-commercial use with attribution is allowed. For commercial use, please contact us.</p>\n                  </div>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">Response Time</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  We commit to responding to your emails within:\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>General inquiries: 1-2 business days</li>\n                  <li>Urgent content corrections: 24 hours</li>\n                  <li>Feature feedback: 3-5 business days</li>\n                  <li>Partnership discussions: 5-7 business days</li>\n                </ul>\n\n                <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4\">\n                  <p className=\"text-yellow-800\">\n                    <strong>Medical Emergencies:</strong> If you encounter a medical emergency, please seek immediate medical attention or call emergency services. Do not rely on email contact.\n                  </p>\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAoB;IACjE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,OAAO;QACL,OAAO,OAAO,sBAAsB;QACpC,aAAa,OACT,8CACA;IACN;AACF;AAEe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,OAAO,SAAS;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;kCACZ,qBACC;;8CACE,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAY;;;;;;;8DAEtB,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4C;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAY;;;;;;;8DAEtB,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAKxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAY;;;;;;;8DAEtB,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAKzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAY;;;;;;;8DAEtB,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAIjC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAAgB;;;;;;;;;;;;;yDAK9B;;8CACE,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAEzB,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4C;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAEzB,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAKxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAEzB,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAKzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAEzB,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAIjC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,GAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BAC<PERSON>,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}