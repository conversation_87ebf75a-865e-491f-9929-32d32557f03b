{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/about/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\ninterface AboutPageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: AboutPageProps): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  return {\n    title: isZh ? '关于我们 - G6PD缺乏症FAQ' : 'About Us - G6PD Deficiency FAQ',\n    description: isZh \n      ? '了解我们的使命：为G6PD缺乏症患者提供准确、可靠的医疗信息和用药指导，帮助患者安全用药。'\n      : 'Learn about our mission: providing accurate, reliable medical information and medication guidance for G6PD deficiency patients to help ensure safe medication use.',\n  }\n}\n\nexport default async function AboutPage({ params }: AboutPageProps) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">\n            {isZh ? '关于我们' : 'About Us'}\n          </h1>\n          \n          <div className=\"prose prose-lg max-w-none\">\n            {isZh ? (\n              <>\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">我们的使命</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  G6PD Guide致力于为G6PD缺乏症（蚕豆病）患者及其家属提供准确、可靠的医疗信息和用药指导。我们的目标是帮助患者了解这种遗传性疾病，避免可能引发溶血危机的药物和食物，确保安全用药。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">为什么选择我们</h2>\n                <div className=\"grid md:grid-cols-2 gap-6 mb-8\">\n                  <div className=\"bg-blue-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">专业可靠</h3>\n                    <p className=\"text-blue-800\">\n                      基于权威医学资料和临床指南，提供准确的医疗信息\n                    </p>\n                  </div>\n                  <div className=\"bg-green-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-green-900 mb-3\">全面覆盖</h3>\n                    <p className=\"text-green-800\">\n                      涵盖中药禁忌、口服液安全、症状识别、饮食指导等各个方面\n                    </p>\n                  </div>\n                  <div className=\"bg-purple-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-purple-900 mb-3\">易于理解</h3>\n                    <p className=\"text-purple-800\">\n                      用通俗易懂的语言解释复杂的医学概念\n                    </p>\n                  </div>\n                  <div className=\"bg-orange-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-orange-900 mb-3\">持续更新</h3>\n                    <p className=\"text-orange-800\">\n                      根据最新医学研究和临床实践不断更新内容\n                    </p>\n                  </div>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">重要提醒</h2>\n                <div className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\">\n                  <p className=\"text-red-800 font-semibold\">\n                    本网站提供的信息仅供参考，不能替代专业医疗建议。任何医疗决定都应咨询合格的医疗专业人员。\n                  </p>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">我们的承诺</h2>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>提供基于科学证据的准确信息</li>\n                  <li>保护用户隐私和数据安全</li>\n                  <li>持续改进网站功能和用户体验</li>\n                  <li>响应用户反馈和建议</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">联系我们</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  如果您有任何问题、建议或需要帮助，请随时联系我们：\n                </p>\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <p className=\"text-gray-700\">\n                    <strong>反馈：</strong> <EMAIL><br/>\n                  </p>\n                </div>\n              </>\n            ) : (\n              <>\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">Our Mission</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  G6PD Guide is dedicated to providing accurate, reliable medical information and medication guidance for G6PD deficiency (favism) patients and their families. Our goal is to help patients understand this genetic condition, avoid medications and foods that may trigger hemolytic crises, and ensure safe medication use.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">Why Choose Us</h2>\n                <div className=\"grid md:grid-cols-2 gap-6 mb-8\">\n                  <div className=\"bg-blue-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">Professional & Reliable</h3>\n                    <p className=\"text-blue-800\">\n                      Based on authoritative medical literature and clinical guidelines, providing accurate medical information\n                    </p>\n                  </div>\n                  <div className=\"bg-green-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-green-900 mb-3\">Comprehensive Coverage</h3>\n                    <p className=\"text-green-800\">\n                      Covering Chinese medicine contraindications, oral solution safety, symptom recognition, dietary guidance, and more\n                    </p>\n                  </div>\n                  <div className=\"bg-purple-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-purple-900 mb-3\">Easy to Understand</h3>\n                    <p className=\"text-purple-800\">\n                      Explaining complex medical concepts in plain, understandable language\n                    </p>\n                  </div>\n                  <div className=\"bg-orange-50 p-6 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-orange-900 mb-3\">Continuously Updated</h3>\n                    <p className=\"text-orange-800\">\n                      Constantly updating content based on latest medical research and clinical practice\n                    </p>\n                  </div>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">Important Notice</h2>\n                <div className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\">\n                  <p className=\"text-red-800 font-semibold\">\n                    The information provided on this website is for reference only and cannot replace professional medical advice. Any medical decisions should consult qualified medical professionals.\n                  </p>\n                </div>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">Our Commitment</h2>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>Provide accurate information based on scientific evidence</li>\n                  <li>Protect user privacy and data security</li>\n                  <li>Continuously improve website functionality and user experience</li>\n                  <li>Respond to user feedback and suggestions</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">Contact Us</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  If you have any questions, suggestions, or need help, please feel free to contact us:\n                </p>\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <p className=\"text-gray-700\">\n                    <strong>Feedback:</strong> <EMAIL><br/>\n                  </p>\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAkB;IAC/D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,OAAO;QACL,OAAO,OAAO,sBAAsB;QACpC,aAAa,OACT,kDACA;IACN;AACF;AAEe,eAAe,UAAU,EAAE,MAAM,EAAkB;IAChE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,OAAO,SAAS;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;kCACZ,qBACC;;8CACE,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAI/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4C;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;8DAAiB;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAkB;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAkB;;;;;;;;;;;;;;;;;;8CAMnC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;8CAK5C,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAAY;0DAAe,8OAAC;;;;;;;;;;;;;;;;;yDAK1C;;8CACE,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAI/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4C;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;8DAAiB;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAkB;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAkB;;;;;;;;;;;;;;;;;;8CAMnC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;8CAK5C,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAAkB;0DAAe,8OAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,KAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACNC,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}