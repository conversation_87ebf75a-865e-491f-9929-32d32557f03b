{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts"], "sourcesContent": ["import { FAQ, Category, Medication } from './types'\nimport fs from 'fs'\nimport path from 'path'\nimport matter from 'gray-matter'\n\n// FAQ数据管理\nexport class FAQManager {\n  private static instance: FAQManager\n  private faqs: Map<string, FAQ[]> = new Map()\n  private categories: Map<string, Category[]> = new Map()\n  private medications: Map<string, Medication[]> = new Map()\n\n  static getInstance(): FAQManager {\n    if (!FAQManager.instance) {\n      FAQManager.instance = new FAQManager()\n    }\n    return FAQManager.instance\n  }\n\n  // 加载FAQ数据\n  async loadFAQs(locale: string): Promise<FAQ[]> {\n    if (this.faqs.has(locale)) {\n      return this.faqs.get(locale)!\n    }\n\n    const faqsDir = path.join(process.cwd(), 'src/data/faqs', locale)\n    const faqs: FAQ[] = []\n\n    if (fs.existsSync(faqsDir)) {\n      const files = fs.readdirSync(faqsDir, { recursive: true })\n      \n      for (const file of files) {\n        if (typeof file === 'string' && file.endsWith('.md')) {\n          const filePath = path.join(faqsDir, file)\n          const fileContent = fs.readFileSync(filePath, 'utf8')\n          const { data, content } = matter(fileContent)\n          \n          const faq: FAQ = {\n            id: data.id || this.generateId(),\n            slug: data.slug || this.generateSlug(data.title),\n            title: data.title,\n            question: data.question || data.title,\n            answer: content,\n            shortAnswer: data.shortAnswer,\n            category: data.category,\n            subcategory: data.subcategory,\n            tags: data.tags || [],\n            difficulty: data.difficulty || 'basic',\n            priority: data.priority || 0,\n            relatedFaqs: data.relatedFaqs || [],\n            lastUpdated: data.lastUpdated || new Date().toISOString(),\n            author: data.author,\n            medicalReview: data.medicalReview || false,\n            sources: data.sources || [],\n            locale: locale as 'zh' | 'en'\n          }\n          \n          faqs.push(faq)\n        }\n      }\n    }\n\n    // 按优先级和更新时间排序\n    faqs.sort((a, b) => {\n      if (a.priority !== b.priority) {\n        return b.priority - a.priority\n      }\n      return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()\n    })\n\n    this.faqs.set(locale, faqs)\n    return faqs\n  }\n\n  // 获取单个FAQ\n  async getFAQ(slug: string, locale: string): Promise<FAQ | null> {\n    const faqs = await this.loadFAQs(locale)\n    return faqs.find(faq => faq.slug === slug) || null\n  }\n\n  // 按分类获取FAQ\n  async getFAQsByCategory(category: string, locale: string): Promise<FAQ[]> {\n    const faqs = await this.loadFAQs(locale)\n    return faqs.filter(faq => faq.category === category)\n  }\n\n  // 搜索FAQ\n  async searchFAQs(query: string, locale: string): Promise<FAQ[]> {\n    const faqs = await this.loadFAQs(locale)\n    const normalizedQuery = query.toLowerCase()\n    \n    return faqs.filter(faq => \n      faq.title.toLowerCase().includes(normalizedQuery) ||\n      faq.question.toLowerCase().includes(normalizedQuery) ||\n      faq.answer.toLowerCase().includes(normalizedQuery) ||\n      faq.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))\n    )\n  }\n\n  // 获取相关FAQ\n  async getRelatedFAQs(faqId: string, locale: string, limit: number = 5): Promise<FAQ[]> {\n    const faqs = await this.loadFAQs(locale)\n    const currentFaq = faqs.find(faq => faq.id === faqId)\n    \n    if (!currentFaq) return []\n\n    // 基于标签和分类的相关性计算\n    const related = faqs\n      .filter(faq => faq.id !== faqId)\n      .map(faq => {\n        let score = 0\n        \n        // 同分类加分\n        if (faq.category === currentFaq.category) score += 3\n        if (faq.subcategory === currentFaq.subcategory) score += 2\n        \n        // 共同标签加分\n        const commonTags = faq.tags.filter(tag => currentFaq.tags.includes(tag))\n        score += commonTags.length * 2\n        \n        return { faq, score }\n      })\n      .filter(item => item.score > 0)\n      .sort((a, b) => b.score - a.score)\n      .slice(0, limit)\n      .map(item => item.faq)\n\n    return related\n  }\n\n  // 生成ID\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9)\n  }\n\n  // 生成slug\n  private generateSlug(title: string): string {\n    return title\n      .toLowerCase()\n      .replace(/[^\\w\\s\\u4e00-\\u9fff]/g, '')\n      .replace(/\\s+/g, '-')\n      .replace(/^-+|-+$/g, '')\n  }\n}\n\n// 分类数据管理\nexport class CategoryManager {\n  private static instance: CategoryManager\n  private categories: Map<string, Category[]> = new Map()\n\n  static getInstance(): CategoryManager {\n    if (!CategoryManager.instance) {\n      CategoryManager.instance = new CategoryManager()\n    }\n    return CategoryManager.instance\n  }\n\n  async loadCategories(locale: string): Promise<Category[]> {\n    if (this.categories.has(locale)) {\n      return this.categories.get(locale)!\n    }\n\n    // 这里可以从文件或数据库加载分类数据\n    // 暂时使用硬编码的分类结构\n    const categories: Category[] = this.getDefaultCategories(locale)\n    \n    this.categories.set(locale, categories)\n    return categories\n  }\n\n  private getDefaultCategories(locale: string): Category[] {\n    if (locale === 'zh') {\n      return [\n        {\n          id: 'medications',\n          slug: 'medications',\n          name: '用药指导',\n          description: '关于G6PD缺乏症患者用药的专业指导',\n          icon: 'pill',\n          faqCount: 0,\n          priority: 1,\n          locale: 'zh',\n          children: [\n            {\n              id: 'chinese-medicine',\n              slug: 'chinese-medicine',\n              name: '中药相关',\n              description: '中药使用注意事项和禁忌',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 1,\n              locale: 'zh'\n            },\n            {\n              id: 'oral-solutions',\n              slug: 'oral-solutions',\n              name: '口服液相关',\n              description: '各类口服液的使用指导',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 2,\n              locale: 'zh'\n            },\n            {\n              id: 'western-medicine',\n              slug: 'western-medicine',\n              name: '西药相关',\n              description: '西药使用注意事项',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 3,\n              locale: 'zh'\n            }\n          ]\n        },\n        {\n          id: 'diet',\n          slug: 'diet',\n          name: '饮食指导',\n          description: 'G6PD缺乏症患者的饮食建议和禁忌',\n          icon: 'utensils',\n          faqCount: 0,\n          priority: 2,\n          locale: 'zh'\n        },\n        {\n          id: 'symptoms',\n          slug: 'symptoms',\n          name: '症状识别',\n          description: '如何识别和处理G6PD缺乏症相关症状',\n          icon: 'stethoscope',\n          faqCount: 0,\n          priority: 3,\n          locale: 'zh'\n        },\n        {\n          id: 'treatment',\n          slug: 'treatment',\n          name: '治疗方案',\n          description: 'G6PD缺乏症的治疗和管理方案',\n          icon: 'heart-pulse',\n          faqCount: 0,\n          priority: 4,\n          locale: 'zh'\n        }\n      ]\n    } else {\n      return [\n        {\n          id: 'medications',\n          slug: 'medications',\n          name: 'Medications',\n          description: 'Professional guidance on medications for G6PD deficiency patients',\n          icon: 'pill',\n          faqCount: 0,\n          priority: 1,\n          locale: 'en',\n          children: [\n            {\n              id: 'chinese-medicine',\n              slug: 'chinese-medicine',\n              name: 'Chinese Medicine',\n              description: 'Precautions and contraindications for Chinese medicine',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 1,\n              locale: 'en'\n            },\n            {\n              id: 'oral-solutions',\n              slug: 'oral-solutions',\n              name: 'Oral Solutions',\n              description: 'Guidance for various oral solutions',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 2,\n              locale: 'en'\n            },\n            {\n              id: 'western-medicine',\n              slug: 'western-medicine',\n              name: 'Western Medicine',\n              description: 'Precautions for western medications',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 3,\n              locale: 'en'\n            }\n          ]\n        },\n        {\n          id: 'diet',\n          slug: 'diet',\n          name: 'Diet',\n          description: 'Dietary recommendations and restrictions for G6PD deficiency patients',\n          icon: 'utensils',\n          faqCount: 0,\n          priority: 2,\n          locale: 'en'\n        },\n        {\n          id: 'symptoms',\n          slug: 'symptoms',\n          name: 'Symptoms',\n          description: 'How to recognize and handle G6PD deficiency related symptoms',\n          icon: 'stethoscope',\n          faqCount: 0,\n          priority: 3,\n          locale: 'en'\n        },\n        {\n          id: 'treatment',\n          slug: 'treatment',\n          name: 'Treatment',\n          description: 'Treatment and management options for G6PD deficiency',\n          icon: 'heart-pulse',\n          faqCount: 0,\n          priority: 4,\n          locale: 'en'\n        }\n      ]\n    }\n  }\n}\n\n// 导出单例实例\nexport const faqManager = FAQManager.getInstance()\nexport const categoryManager = CategoryManager.getInstance()\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;AAGO,MAAM;IACX,OAAe,SAAoB;IAC3B,OAA2B,IAAI,MAAK;IACpC,aAAsC,IAAI,MAAK;IAC/C,cAAyC,IAAI,MAAK;IAE1D,OAAO,cAA0B;QAC/B,IAAI,CAAC,WAAW,QAAQ,EAAE;YACxB,WAAW,QAAQ,GAAG,IAAI;QAC5B;QACA,OAAO,WAAW,QAAQ;IAC5B;IAEA,UAAU;IACV,MAAM,SAAS,MAAc,EAAkB;QAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACvB;QAEA,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAiB;QAC1D,MAAM,OAAc,EAAE;QAEtB,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;YAC1B,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC,SAAS;gBAAE,WAAW;YAAK;YAExD,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,OAAO,SAAS,YAAY,KAAK,QAAQ,CAAC,QAAQ;oBACpD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;oBACpC,MAAM,cAAc,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;oBAC9C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;oBAEjC,MAAM,MAAW;wBACf,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,UAAU;wBAC9B,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK;wBAC/C,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ,IAAI,KAAK,KAAK;wBACrC,QAAQ;wBACR,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,aAAa,KAAK,WAAW;wBAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;wBACrB,YAAY,KAAK,UAAU,IAAI;wBAC/B,UAAU,KAAK,QAAQ,IAAI;wBAC3B,aAAa,KAAK,WAAW,IAAI,EAAE;wBACnC,aAAa,KAAK,WAAW,IAAI,IAAI,OAAO,WAAW;wBACvD,QAAQ,KAAK,MAAM;wBACnB,eAAe,KAAK,aAAa,IAAI;wBACrC,SAAS,KAAK,OAAO,IAAI,EAAE;wBAC3B,QAAQ;oBACV;oBAEA,KAAK,IAAI,CAAC;gBACZ;YACF;QACF;QAEA,cAAc;QACd,KAAK,IAAI,CAAC,CAAC,GAAG;YACZ,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC;YACA,OAAO,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;QAC5E;QAEA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ;QACtB,OAAO;IACT;IAEA,UAAU;IACV,MAAM,OAAO,IAAY,EAAE,MAAc,EAAuB;QAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,SAAS;IAChD;IAEA,WAAW;IACX,MAAM,kBAAkB,QAAgB,EAAE,MAAc,EAAkB;QACxE,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;IAC7C;IAEA,QAAQ;IACR,MAAM,WAAW,KAAa,EAAE,MAAc,EAAkB;QAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,MAAM,kBAAkB,MAAM,WAAW;QAEzC,OAAO,KAAK,MAAM,CAAC,CAAA,MACjB,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACjC,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACpC,IAAI,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAClC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;IAEpD;IAEA,UAAU;IACV,MAAM,eAAe,KAAa,EAAE,MAAc,EAAE,QAAgB,CAAC,EAAkB;QACrF,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAE/C,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,gBAAgB;QAChB,MAAM,UAAU,KACb,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OACzB,GAAG,CAAC,CAAA;YACH,IAAI,QAAQ;YAEZ,QAAQ;YACR,IAAI,IAAI,QAAQ,KAAK,WAAW,QAAQ,EAAE,SAAS;YACnD,IAAI,IAAI,WAAW,KAAK,WAAW,WAAW,EAAE,SAAS;YAEzD,SAAS;YACT,MAAM,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,WAAW,IAAI,CAAC,QAAQ,CAAC;YACnE,SAAS,WAAW,MAAM,GAAG;YAE7B,OAAO;gBAAE;gBAAK;YAAM;QACtB,GACC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,GAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;QAEvB,OAAO;IACT;IAEA,OAAO;IACC,aAAqB;QAC3B,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAC9C;IAEA,SAAS;IACD,aAAa,KAAa,EAAU;QAC1C,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,yBAAyB,IACjC,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,YAAY;IACzB;AACF;AAGO,MAAM;IACX,OAAe,SAAyB;IAChC,aAAsC,IAAI,MAAK;IAEvD,OAAO,cAA+B;QACpC,IAAI,CAAC,gBAAgB,QAAQ,EAAE;YAC7B,gBAAgB,QAAQ,GAAG,IAAI;QACjC;QACA,OAAO,gBAAgB,QAAQ;IACjC;IAEA,MAAM,eAAe,MAAc,EAAuB;QACxD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS;YAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAC7B;QAEA,oBAAoB;QACpB,eAAe;QACf,MAAM,aAAyB,IAAI,CAAC,oBAAoB,CAAC;QAEzD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC5B,OAAO;IACT;IAEQ,qBAAqB,MAAc,EAAc;QACvD,IAAI,WAAW,MAAM;YACnB,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;oBACR,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;aACD;QACH,OAAO;YACL,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;oBACR,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;aACD;QACH;IACF;AACF;AAGO,MAAM,aAAa,WAAW,WAAW;AACzC,MAAM,kBAAkB,gBAAgB,WAAW", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { SEOData } from '@/lib/types'\n\ninterface SEOProps {\n  data: SEOData\n  locale: string\n}\n\nexport function generateSEOMetadata({ data, locale }: SEOProps): Metadata {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  \n  return {\n    title: data.title,\n    description: data.description,\n    keywords: data.keywords,\n    openGraph: {\n      title: data.title,\n      description: data.description,\n      url: data.canonical || `${baseUrl}/${locale}`,\n      siteName: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n      images: [\n        {\n          url: data.ogImage || `${baseUrl}/og-image.jpg`,\n          width: 1200,\n          height: 630,\n          alt: data.title,\n        },\n      ],\n      locale: locale,\n      type: 'website',\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title: data.title,\n      description: data.description,\n      images: [data.ogImage || `${baseUrl}/og-image.jpg`],\n    },\n    robots: {\n      index: !data.noindex,\n      follow: !data.noindex,\n      googleBot: {\n        index: !data.noindex,\n        follow: !data.noindex,\n        'max-video-preview': -1,\n        'max-image-preview': 'large',\n        'max-snippet': -1,\n      },\n    },\n    alternates: {\n      canonical: data.canonical,\n      languages: {\n        'zh': `${baseUrl}/zh`,\n        'en': `${baseUrl}/en`,\n      },\n    },\n    verification: {\n      google: process.env.GOOGLE_SITE_VERIFICATION,\n      yandex: process.env.YANDEX_VERIFICATION,\n      yahoo: process.env.YAHOO_VERIFICATION,\n    },\n  }\n}\n\nexport function generateStructuredData(data: Record<string, unknown>, type: string, locale: string) {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  \n  const baseStructuredData = {\n    '@context': 'https://schema.org',\n    '@type': type,\n    url: `${baseUrl}/${locale}`,\n    name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n    description: locale === 'zh' \n      ? '为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导'\n      : 'Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families',\n    inLanguage: locale,\n    isPartOf: {\n      '@type': 'WebSite',\n      name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n      url: baseUrl,\n    },\n  }\n\n  switch (type) {\n    case 'WebSite':\n      return {\n        ...baseStructuredData,\n        '@type': 'WebSite',\n        potentialAction: {\n          '@type': 'SearchAction',\n          target: {\n            '@type': 'EntryPoint',\n            urlTemplate: `${baseUrl}/${locale}/search?q={search_term_string}`,\n          },\n          'query-input': 'required name=search_term_string',\n        },\n      }\n\n    case 'FAQPage':\n      return {\n        ...baseStructuredData,\n        '@type': 'FAQPage',\n        mainEntity: (data.faqs as Array<Record<string, unknown>>)?.map((faq) => ({\n          '@type': 'Question',\n          name: faq.question,\n          acceptedAnswer: {\n            '@type': 'Answer',\n            text: faq.shortAnswer || (typeof faq.answer === 'string' ? faq.answer.substring(0, 200) + '...' : ''),\n          },\n        })) || [],\n      }\n\n    case 'MedicalWebPage':\n      return {\n        ...baseStructuredData,\n        '@type': 'MedicalWebPage',\n        medicalAudience: {\n          '@type': 'MedicalAudience',\n          audienceType: 'Patient',\n        },\n        about: {\n          '@type': 'MedicalCondition',\n          name: 'G6PD Deficiency',\n          alternateName: locale === 'zh' ? '蚕豆病' : 'Glucose-6-phosphate dehydrogenase deficiency',\n          description: locale === 'zh'\n            ? 'G6PD缺乏症是一种遗传性酶缺乏病，患者需要避免某些药物和食物以防止溶血反应'\n            : 'G6PD deficiency is a hereditary enzyme deficiency disease where patients need to avoid certain medications and foods to prevent hemolytic reactions',\n        },\n        lastReviewed: data.lastUpdated || new Date().toISOString(),\n        reviewedBy: {\n          '@type': 'Organization',\n          name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team',\n        },\n      }\n\n    case 'Article':\n      return {\n        ...baseStructuredData,\n        '@type': 'Article',\n        headline: data.title,\n        description: data.description,\n        datePublished: data.datePublished || new Date().toISOString(),\n        dateModified: data.lastUpdated || new Date().toISOString(),\n        author: {\n          '@type': 'Organization',\n          name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team',\n        },\n        publisher: {\n          '@type': 'Organization',\n          name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n          logo: {\n            '@type': 'ImageObject',\n            url: `${baseUrl}/logo.png`,\n          },\n        },\n        mainEntityOfPage: {\n          '@type': 'WebPage',\n          '@id': data.canonical || `${baseUrl}/${locale}`,\n        },\n      }\n\n    case 'BreadcrumbList':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: (data.breadcrumbs as Array<Record<string, unknown>>)?.map((item, index: number) => ({\n          '@type': 'ListItem',\n          position: index + 1,\n          name: item.title,\n          item: `${baseUrl}${item.href}`,\n        })) || [],\n      }\n\n    default:\n      return baseStructuredData\n  }\n}\n\nexport function StructuredDataScript({ data, type, locale }: { data: Record<string, unknown>; type: string; locale: string }) {\n  const structuredData = generateStructuredData(data, type, locale)\n  \n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(structuredData),\n      }}\n    />\n  )\n}\n\n// SEO utility functions\nexport function generateCanonicalUrl(pathname: string): string {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  return `${baseUrl}${pathname}`\n}\n\nexport function generateAlternateUrls(pathname: string): Record<string, string> {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  const pathWithoutLocale = pathname.replace(/^\\/(zh|en)/, '')\n  \n  return {\n    'zh': `${baseUrl}/zh${pathWithoutLocale}`,\n    'en': `${baseUrl}/en${pathWithoutLocale}`,\n  }\n}\n\nexport function generateSEOTitle(title: string, siteName: string, locale: string): string {\n  const separator = locale === 'zh' ? ' - ' : ' | '\n  return `${title}${separator}${siteName}`\n}\n\nexport function generateSEODescription(content: string, maxLength: number = 160): string {\n  // Remove markdown and HTML\n  const cleanContent = content\n    .replace(/#{1,6}\\s+/g, '')\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1')\n    .replace(/\\*(.*?)\\*/g, '$1')\n    .replace(/<[^>]*>/g, '')\n    .replace(/\\n+/g, ' ')\n    .trim()\n  \n  if (cleanContent.length <= maxLength) return cleanContent\n  return cleanContent.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAQO,SAAS,oBAAoB,EAAE,IAAI,EAAE,MAAM,EAAY;IAC5D,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IAEpD,OAAO;QACL,OAAO,KAAK,KAAK;QACjB,aAAa,KAAK,WAAW;QAC7B,UAAU,KAAK,QAAQ;QACvB,WAAW;YACT,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,KAAK,KAAK,SAAS,IAAI,GAAG,QAAQ,CAAC,EAAE,QAAQ;YAC7C,UAAU,WAAW,OAAO,eAAe;YAC3C,QAAQ;gBACN;oBACE,KAAK,KAAK,OAAO,IAAI,GAAG,QAAQ,aAAa,CAAC;oBAC9C,OAAO;oBACP,QAAQ;oBACR,KAAK,KAAK,KAAK;gBACjB;aACD;YACD,QAAQ;YACR,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,QAAQ;gBAAC,KAAK,OAAO,IAAI,GAAG,QAAQ,aAAa,CAAC;aAAC;QACrD;QACA,QAAQ;YACN,OAAO,CAAC,KAAK,OAAO;YACpB,QAAQ,CAAC,KAAK,OAAO;YACrB,WAAW;gBACT,OAAO,CAAC,KAAK,OAAO;gBACpB,QAAQ,CAAC,KAAK,OAAO;gBACrB,qBAAqB,CAAC;gBACtB,qBAAqB;gBACrB,eAAe,CAAC;YAClB;QACF;QACA,YAAY;YACV,WAAW,KAAK,SAAS;YACzB,WAAW;gBACT,MAAM,GAAG,QAAQ,GAAG,CAAC;gBACrB,MAAM,GAAG,QAAQ,GAAG,CAAC;YACvB;QACF;QACA,cAAc;YACZ,QAAQ,QAAQ,GAAG,CAAC,wBAAwB;YAC5C,QAAQ,QAAQ,GAAG,CAAC,mBAAmB;YACvC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;IACF;AACF;AAEO,SAAS,uBAAuB,IAA6B,EAAE,IAAY,EAAE,MAAc;IAChG,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IAEpD,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;QACT,KAAK,GAAG,QAAQ,CAAC,EAAE,QAAQ;QAC3B,MAAM,WAAW,OAAO,eAAe;QACvC,aAAa,WAAW,OACpB,mCACA;QACJ,YAAY;QACZ,UAAU;YACR,SAAS;YACT,MAAM,WAAW,OAAO,eAAe;YACvC,KAAK;QACP;IACF;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,iBAAiB;oBACf,SAAS;oBACT,QAAQ;wBACN,SAAS;wBACT,aAAa,GAAG,QAAQ,CAAC,EAAE,OAAO,8BAA8B,CAAC;oBACnE;oBACA,eAAe;gBACjB;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,YAAY,AAAC,KAAK,IAAI,EAAqC,IAAI,CAAC,MAAQ,CAAC;wBACvE,SAAS;wBACT,MAAM,IAAI,QAAQ;wBAClB,gBAAgB;4BACd,SAAS;4BACT,MAAM,IAAI,WAAW,IAAI,CAAC,OAAO,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ,EAAE;wBACtG;oBACF,CAAC,MAAM,EAAE;YACX;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,iBAAiB;oBACf,SAAS;oBACT,cAAc;gBAChB;gBACA,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,eAAe,WAAW,OAAO,QAAQ;oBACzC,aAAa,WAAW,OACpB,2CACA;gBACN;gBACA,cAAc,KAAK,WAAW,IAAI,IAAI,OAAO,WAAW;gBACxD,YAAY;oBACV,SAAS;oBACT,MAAM,WAAW,OAAO,WAAW;gBACrC;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,UAAU,KAAK,KAAK;gBACpB,aAAa,KAAK,WAAW;gBAC7B,eAAe,KAAK,aAAa,IAAI,IAAI,OAAO,WAAW;gBAC3D,cAAc,KAAK,WAAW,IAAI,IAAI,OAAO,WAAW;gBACxD,QAAQ;oBACN,SAAS;oBACT,MAAM,WAAW,OAAO,WAAW;gBACrC;gBACA,WAAW;oBACT,SAAS;oBACT,MAAM,WAAW,OAAO,eAAe;oBACvC,MAAM;wBACJ,SAAS;wBACT,KAAK,GAAG,QAAQ,SAAS,CAAC;oBAC5B;gBACF;gBACA,kBAAkB;oBAChB,SAAS;oBACT,OAAO,KAAK,SAAS,IAAI,GAAG,QAAQ,CAAC,EAAE,QAAQ;gBACjD;YACF;QAEF,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,iBAAiB,AAAC,KAAK,WAAW,EAAqC,IAAI,CAAC,MAAM,QAAkB,CAAC;wBACnG,SAAS;wBACT,UAAU,QAAQ;wBAClB,MAAM,KAAK,KAAK;wBAChB,MAAM,GAAG,UAAU,KAAK,IAAI,EAAE;oBAChC,CAAC,MAAM,EAAE;YACX;QAEF;YACE,OAAO;IACX;AACF;AAEO,SAAS,qBAAqB,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAmE;IAC1H,MAAM,iBAAiB,uBAAuB,MAAM,MAAM;IAE1D,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN;AAGO,SAAS,qBAAqB,QAAgB;IACnD,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IACpD,OAAO,GAAG,UAAU,UAAU;AAChC;AAEO,SAAS,sBAAsB,QAAgB;IACpD,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IACpD,MAAM,oBAAoB,SAAS,OAAO,CAAC,cAAc;IAEzD,OAAO;QACL,MAAM,GAAG,QAAQ,GAAG,EAAE,mBAAmB;QACzC,MAAM,GAAG,QAAQ,GAAG,EAAE,mBAAmB;IAC3C;AACF;AAEO,SAAS,iBAAiB,KAAa,EAAE,QAAgB,EAAE,MAAc;IAC9E,MAAM,YAAY,WAAW,OAAO,QAAQ;IAC5C,OAAO,GAAG,QAAQ,YAAY,UAAU;AAC1C;AAEO,SAAS,uBAAuB,OAAe,EAAE,YAAoB,GAAG;IAC7E,2BAA2B;IAC3B,MAAM,eAAe,QAClB,OAAO,CAAC,cAAc,IACtB,OAAO,CAAC,kBAAkB,MAC1B,OAAO,CAAC,cAAc,MACtB,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;IAEP,IAAI,aAAa,MAAM,IAAI,WAAW,OAAO;IAC7C,OAAO,aAAa,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AACnE", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/faq/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { getTranslations } from 'next-intl/server'\nimport Link from 'next/link'\nimport { faqManager } from '@/lib/data'\nimport { generateSEOMetadata, StructuredDataScript } from '@/components/seo'\nimport { \n  MagnifyingGlassIcon,\n  FunnelIcon,\n  BookOpenIcon,\n  ClockIcon,\n  TagIcon\n} from '@heroicons/react/24/outline'\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}): Promise<Metadata> {\n  const { locale } = await params\n  const seoData = {\n    title: locale === 'zh' ? '常见问题 - G6PD缺乏症FAQ' : 'FAQ - G6PD Deficiency FAQ',\n    description: locale === 'zh' \n      ? '浏览G6PD缺乏症（蚕豆病）的常见问题，包括用药指导、饮食建议、症状识别等专业内容。'\n      : '<PERSON><PERSON><PERSON> frequently asked questions about G6PD deficiency, including medication guidance, dietary advice, and symptom recognition.',\n    keywords: locale === 'zh'\n      ? 'G6PD缺乏症,蚕豆病,常见问题,用药指导,饮食建议,症状识别'\n      : 'G6PD deficiency,FAQ,medication guidance,dietary advice,symptom recognition',\n    canonical: `/${locale}/faq`\n  }\n\n  return generateSEOMetadata({ data: seoData, locale })\n}\n\nexport default async function FAQPage({\n  params,\n  searchParams\n}: {\n  params: Promise<{ locale: string }>\n  searchParams: Promise<{ category?: string; search?: string; page?: string }>\n}) {\n  const { locale } = await params\n  const searchParamsData = await searchParams\n  const t = await getTranslations({ locale })\n  const faqs = await faqManager.loadFAQs(locale)\n  \n  // Filter FAQs based on search params\n  let filteredFAQs = faqs\n  \n  if (searchParamsData.category) {\n    filteredFAQs = filteredFAQs.filter(faq => faq.category === searchParamsData.category)\n  }\n\n  if (searchParamsData.search) {\n    const searchTerm = searchParamsData.search.toLowerCase()\n    filteredFAQs = filteredFAQs.filter(faq => \n      faq.title.toLowerCase().includes(searchTerm) ||\n      faq.question.toLowerCase().includes(searchTerm) ||\n      faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))\n    )\n  }\n\n  // Pagination\n  const page = parseInt(searchParamsData.page || '1')\n  const itemsPerPage = 12\n  const totalPages = Math.ceil(filteredFAQs.length / itemsPerPage)\n  const startIndex = (page - 1) * itemsPerPage\n  const paginatedFAQs = filteredFAQs.slice(startIndex, startIndex + itemsPerPage)\n\n  const categories = [\n    { id: 'all', name: t('faq.categories.all'), count: faqs.length },\n    { id: 'medications', name: t('faq.categories.medications'), count: faqs.filter(f => f.category === 'medications').length },\n    { id: 'diet', name: t('faq.categories.diet'), count: faqs.filter(f => f.category === 'diet').length },\n    { id: 'symptoms', name: t('faq.categories.symptoms'), count: faqs.filter(f => f.category === 'symptoms').length },\n    { id: 'treatment', name: t('faq.categories.treatment'), count: faqs.filter(f => f.category === 'treatment').length },\n  ]\n\n  return (\n    <>\n      <StructuredDataScript \n        data={{ faqs: paginatedFAQs }} \n        type=\"FAQPage\" \n        locale={locale} \n      />\n      \n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <div className=\"text-center\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                {t('faq.title')}\n              </h1>\n              <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n                {t('faq.subtitle')}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"flex flex-col lg:flex-row gap-8\">\n            {/* Sidebar */}\n            <div className=\"lg:w-1/4\">\n              <div className=\"bg-white rounded-lg shadow-sm p-6 sticky top-4\">\n                {/* Search */}\n                <div className=\"mb-6\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {t('common.search')}\n                  </label>\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      placeholder={t('common.searchPlaceholder')}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                      defaultValue={searchParamsData.search || ''}\n                    />\n                    <MagnifyingGlassIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                  </div>\n                </div>\n\n                {/* Categories */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-sm font-medium text-gray-700 mb-3\">\n                    {t('faq.filters.category')}\n                  </h3>\n                  <div className=\"space-y-2\">\n                    {categories.map((category) => (\n                      <Link\n                        key={category.id}\n                        href={`/${locale}/faq${category.id !== 'all' ? `?category=${category.id}` : ''}`}\n                        className={`flex items-center justify-between p-2 rounded-md text-sm transition-colors ${\n                          (searchParamsData.category === category.id) || (category.id === 'all' && !searchParamsData.category)\n                            ? 'bg-blue-50 text-blue-700'\n                            : 'text-gray-600 hover:bg-gray-50'\n                        }`}\n                      >\n                        <span>{category.name}</span>\n                        <span className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\">\n                          {category.count}\n                        </span>\n                      </Link>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Difficulty Filter */}\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-700 mb-3\">\n                    {t('faq.filters.difficulty')}\n                  </h3>\n                  <div className=\"space-y-2\">\n                    {['basic', 'intermediate', 'advanced'].map((level) => (\n                      <label key={level} className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-600\">\n                          {t(`faq.difficulty.${level}`)}\n                        </span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Main Content */}\n            <div className=\"lg:w-3/4\">\n              {/* Results Header */}\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex items-center space-x-4\">\n                  <span className=\"text-sm text-gray-600\">\n                    {locale === 'zh' \n                      ? `显示 ${startIndex + 1}-${Math.min(startIndex + itemsPerPage, filteredFAQs.length)} 条，共 ${filteredFAQs.length} 条结果`\n                      : `Showing ${startIndex + 1}-${Math.min(startIndex + itemsPerPage, filteredFAQs.length)} of ${filteredFAQs.length} results`\n                    }\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <FunnelIcon className=\"h-5 w-5 text-gray-400\" />\n                  <select className=\"text-sm border border-gray-300 rounded-md px-3 py-1\">\n                    <option>{locale === 'zh' ? '最新更新' : 'Latest Updated'}</option>\n                    <option>{locale === 'zh' ? '最受欢迎' : 'Most Popular'}</option>\n                    <option>{locale === 'zh' ? '按难度' : 'By Difficulty'}</option>\n                  </select>\n                </div>\n              </div>\n\n              {/* FAQ Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n                {paginatedFAQs.map((faq) => (\n                  <div key={faq.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div className=\"flex items-center space-x-2\">\n                        <span className={`px-2 py-1 text-xs font-medium rounded ${\n                          faq.difficulty === 'basic' ? 'bg-green-100 text-green-800' :\n                          faq.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :\n                          'bg-red-100 text-red-800'\n                        }`}>\n                          {t(`faq.difficulty.${faq.difficulty}`)}\n                        </span>\n                        {faq.medicalReview && (\n                          <span className=\"px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded\">\n                            {locale === 'zh' ? '医学审核' : 'Medical Review'}\n                          </span>\n                        )}\n                      </div>\n                      <ClockIcon className=\"h-4 w-4 text-gray-400\" />\n                    </div>\n\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {faq.title}\n                    </h3>\n\n                    <p className=\"text-gray-600 text-sm mb-4\">\n                      {faq.shortAnswer || faq.answer.substring(0, 150) + '...'}\n                    </p>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex flex-wrap gap-1\">\n                        {faq.tags.slice(0, 2).map((tag) => (\n                          <span key={tag} className=\"inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\">\n                            <TagIcon className=\"h-3 w-3 mr-1\" />\n                            {tag}\n                          </span>\n                        ))}\n                      </div>\n                      <Link\n                        href={`/${locale}/faq/${faq.slug}`}\n                        className=\"inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium\"\n                      >\n                        {t('common.readMore')}\n                        <BookOpenIcon className=\"ml-1 h-4 w-4\" />\n                      </Link>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Pagination */}\n              {totalPages > 1 && (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (\n                    <Link\n                      key={pageNum}\n                      href={`/${locale}/faq?page=${pageNum}${searchParamsData.category ? `&category=${searchParamsData.category}` : ''}${searchParamsData.search ? `&search=${searchParamsData.search}` : ''}`}\n                      className={`px-3 py-2 text-sm rounded-md ${\n                        pageNum === page\n                          ? 'bg-blue-600 text-white'\n                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'\n                      }`}\n                    >\n                      {pageNum}\n                    </Link>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAQO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,UAAU;QACd,OAAO,WAAW,OAAO,sBAAsB;QAC/C,aAAa,WAAW,OACpB,+CACA;QACJ,UAAU,WAAW,OACjB,oCACA;QACJ,WAAW,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;IAC7B;IAEA,OAAO,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;QAAE,MAAM;QAAS;IAAO;AACrD;AAEe,eAAe,QAAQ,EACpC,MAAM,EACN,YAAY,EAIb;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,mBAAmB,MAAM;IAC/B,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;IAAO;IACzC,MAAM,OAAO,MAAM,kHAAA,CAAA,aAAU,CAAC,QAAQ,CAAC;IAEvC,qCAAqC;IACrC,IAAI,eAAe;IAEnB,IAAI,iBAAiB,QAAQ,EAAE;QAC7B,eAAe,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,iBAAiB,QAAQ;IACtF;IAEA,IAAI,iBAAiB,MAAM,EAAE;QAC3B,MAAM,aAAa,iBAAiB,MAAM,CAAC,WAAW;QACtD,eAAe,aAAa,MAAM,CAAC,CAAA,MACjC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACjC,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eACpC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;IAEpD;IAEA,aAAa;IACb,MAAM,OAAO,SAAS,iBAAiB,IAAI,IAAI;IAC/C,MAAM,eAAe;IACrB,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;IACnD,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;IAChC,MAAM,gBAAgB,aAAa,KAAK,CAAC,YAAY,aAAa;IAElE,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM,EAAE;YAAuB,OAAO,KAAK,MAAM;QAAC;QAC/D;YAAE,IAAI;YAAe,MAAM,EAAE;YAA+B,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,eAAe,MAAM;QAAC;QACzH;YAAE,IAAI;YAAQ,MAAM,EAAE;YAAwB,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;QAAC;QACpG;YAAE,IAAI;YAAY,MAAM,EAAE;YAA4B,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;QAAC;QAChH;YAAE,IAAI;YAAa,MAAM,EAAE;YAA6B,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,aAAa,MAAM;QAAC;KACpH;IAED,qBACE;;0BACE,8OAAC,yHAAA,CAAA,uBAAoB;gBACnB,MAAM;oBAAE,MAAM;gBAAc;gBAC5B,MAAK;gBACL,QAAQ;;;;;;0BAGV,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAMX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEACd,EAAE;;;;;;kEAEL,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,aAAa,EAAE;gEACf,WAAU;gEACV,cAAc,iBAAiB,MAAM,IAAI;;;;;;0EAE3C,8OAAC,qOAAA,CAAA,sBAAmB;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAKnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,EAAE;;;;;;kEAEL,8OAAC;wDAAI,WAAU;kEACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,KAAK,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE,GAAG,IAAI;gEAChF,WAAW,CAAC,2EAA2E,EACrF,AAAC,iBAAiB,QAAQ,KAAK,SAAS,EAAE,IAAM,SAAS,EAAE,KAAK,SAAS,CAAC,iBAAiB,QAAQ,GAC/F,6BACA,kCACJ;;kFAEF,8OAAC;kFAAM,SAAS,IAAI;;;;;;kFACpB,8OAAC;wEAAK,WAAU;kFACb,SAAS,KAAK;;;;;;;+DAVZ,SAAS,EAAE;;;;;;;;;;;;;;;;0DAkBxB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,EAAE;;;;;;kEAEL,8OAAC;wDAAI,WAAU;kEACZ;4DAAC;4DAAS;4DAAgB;yDAAW,CAAC,GAAG,CAAC,CAAC,sBAC1C,8OAAC;gEAAkB,WAAU;;kFAC3B,8OAAC;wEACC,MAAK;wEACL,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFACb,EAAE,CAAC,eAAe,EAAE,OAAO;;;;;;;+DANpB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAgBtB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,WAAW,OACR,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,aAAa,cAAc,aAAa,MAAM,EAAE,KAAK,EAAE,aAAa,MAAM,CAAC,IAAI,CAAC,GACjH,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,aAAa,cAAc,aAAa,MAAM,EAAE,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,CAAC;;;;;;;;;;;8DAIjI,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,mNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;8EAAQ,WAAW,OAAO,SAAS;;;;;;8EACpC,8OAAC;8EAAQ,WAAW,OAAO,SAAS;;;;;;8EACpC,8OAAC;8EAAQ,WAAW,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAMzC,8OAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,CAAC,sCAAsC,EACtD,IAAI,UAAU,KAAK,UAAU,gCAC7B,IAAI,UAAU,KAAK,iBAAiB,kCACpC,2BACA;sFACC,EAAE,CAAC,eAAe,EAAE,IAAI,UAAU,EAAE;;;;;;wEAEtC,IAAI,aAAa,kBAChB,8OAAC;4EAAK,WAAU;sFACb,WAAW,OAAO,SAAS;;;;;;;;;;;;8EAIlC,8OAAC,iNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;sEAGvB,8OAAC;4DAAG,WAAU;sEACX,IAAI,KAAK;;;;;;sEAGZ,8OAAC;4DAAE,WAAU;sEACV,IAAI,WAAW,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO;;;;;;sEAGrD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACzB,8OAAC;4EAAe,WAAU;;8FACxB,8OAAC,6MAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;gFAClB;;2EAFQ;;;;;;;;;;8EAMf,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,IAAI,IAAI,EAAE;oEAClC,WAAU;;wEAET,EAAE;sFACH,8OAAC,uNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;mDAzCpB,IAAI,EAAE;;;;;;;;;;wCAiDnB,aAAa,mBACZ,8OAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI,CAAC;gDAAE,QAAQ;4CAAW,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,wBACxD,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,EAAE,UAAU,iBAAiB,QAAQ,GAAG,CAAC,UAAU,EAAE,iBAAiB,QAAQ,EAAE,GAAG,KAAK,iBAAiB,MAAM,GAAG,CAAC,QAAQ,EAAE,iBAAiB,MAAM,EAAE,GAAG,IAAI;oDACxL,WAAW,CAAC,6BAA6B,EACvC,YAAY,OACR,2BACA,kEACJ;8DAED;mDARI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmB3B", "debugId": null}}]}