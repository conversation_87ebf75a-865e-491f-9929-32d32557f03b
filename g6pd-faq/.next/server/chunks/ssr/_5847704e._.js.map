{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import { getTranslations } from 'next-intl/server'\nimport Link from 'next/link'\nimport {\n  HeartIcon,\n  ShieldCheckIcon,\n  BookOpenIcon,\n  ExclamationTriangleIcon,\n  MagnifyingGlassIcon\n} from '@heroicons/react/24/outline'\n\nexport default async function HomePage({ params }: { params: Promise<{ locale: string }> }) {\n  const { locale } = await params\n  const t = await getTranslations({ locale })\n\n  const quickAccessItems = [\n    {\n      title: t('home.quickAccess.medications'),\n      href: `/${locale}/medications`,\n      icon: ShieldCheckIcon,\n      description: locale === 'zh' ? '查询药物安全性' : 'Check medication safety'\n    },\n    {\n      title: t('home.quickAccess.diet'),\n      href: `/${locale}/faq/diet`,\n      icon: HeartIcon,\n      description: locale === 'zh' ? '了解饮食禁忌' : 'Learn about dietary restrictions'\n    },\n    {\n      title: t('home.quickAccess.symptoms'),\n      href: `/${locale}/faq/symptoms`,\n      icon: ExclamationTriangleIcon,\n      description: locale === 'zh' ? '识别症状表现' : 'Identify symptoms'\n    }\n  ]\n\n  const featuredCategories = [\n    {\n      title: locale === 'zh' ? '中药相关' : 'Chinese Medicine',\n      href: `/${locale}/medications/chinese-medicine`,\n      icon: BookOpenIcon,\n      count: '50+',\n      description: locale === 'zh' ? '中药使用注意事项' : 'Chinese medicine precautions'\n    },\n    {\n      title: locale === 'zh' ? '口服液相关' : 'Oral Solutions',\n      href: `/${locale}/medications/oral-solutions`,\n      icon: ShieldCheckIcon,\n      count: '30+',\n      description: locale === 'zh' ? '口服液安全指导' : 'Oral solution safety guide'\n    },\n    {\n      title: locale === 'zh' ? '症状识别' : 'Symptom Recognition',\n      href: `/${locale}/faq/symptoms`,\n      icon: ExclamationTriangleIcon,\n      count: '20+',\n      description: locale === 'zh' ? '急性溶血症状' : 'Acute hemolysis symptoms'\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('home.hero.title')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90\">\n              {t('home.hero.subtitle')}\n            </p>\n            <Link\n              href={`/${locale}/faq`}\n              className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors\"\n            >\n              {t('home.hero.cta')}\n              <BookOpenIcon className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Emergency Alert */}\n      <section className=\"bg-red-50 border-b border-red-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-center\">\n            <ExclamationTriangleIcon className=\"h-6 w-6 text-red-600 mr-3\" />\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-semibold text-red-800\">\n                {t('home.emergencyAlert.title')}\n              </h3>\n              <p className=\"text-red-700 mt-1\">\n                {t('home.emergencyAlert.content')}\n              </p>\n              <Link\n                href={`/${locale}/emergency`}\n                className=\"inline-flex items-center mt-2 text-sm font-medium text-red-800 hover:text-red-900\"\n              >\n                {t('home.emergencyAlert.button')} →\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Quick Access */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              {t('home.quickAccess.title')}\n            </h2>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {quickAccessItems.map((item, index) => (\n              <Link\n                key={index}\n                href={item.href}\n                className=\"group p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <item.icon className=\"h-8 w-8 text-blue-600 group-hover:text-blue-700\" />\n                  <h3 className=\"ml-3 text-lg font-semibold text-gray-900 group-hover:text-blue-700\">\n                    {item.title}\n                  </h3>\n                </div>\n                <p className=\"text-gray-600\">\n                  {item.description}\n                </p>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Categories */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              {locale === 'zh' ? '热门分类' : 'Popular Categories'}\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              {locale === 'zh' ? '最常查询的问题分类' : 'Most frequently searched question categories'}\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {featuredCategories.map((category, index) => (\n              <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <category.icon className=\"h-8 w-8 text-blue-600\" />\n                  <span className=\"text-sm font-semibold text-blue-600 bg-blue-100 px-2 py-1 rounded\">\n                    {category.count}\n                  </span>\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  {category.title}\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  {category.description}\n                </p>\n                <Link\n                  href={category.href}\n                  className=\"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium\"\n                >\n                  {locale === 'zh' ? '查看更多' : 'Learn more'} →\n                </Link>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Search Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            {locale === 'zh' ? '找不到答案？' : 'Can\\'t find an answer?'}\n          </h2>\n          <p className=\"text-lg text-gray-600 mb-8\">\n            {locale === 'zh' ? '使用搜索功能快速找到您需要的信息' : 'Use our search function to quickly find the information you need'}\n          </p>\n          <Link\n            href={`/${locale}/search`}\n            className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n          >\n            <MagnifyingGlassIcon className=\"mr-2 h-5 w-5\" />\n            {locale === 'zh' ? '开始搜索' : 'Start searching'}\n          </Link>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-16 bg-blue-600 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">200+</div>\n              <div className=\"text-blue-100\">\n                {locale === 'zh' ? '常见问题' : 'FAQ Articles'}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">50+</div>\n              <div className=\"text-blue-100\">\n                {locale === 'zh' ? '药物信息' : 'Medications'}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">10+</div>\n              <div className=\"text-blue-100\">\n                {locale === 'zh' ? '专业分类' : 'Categories'}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">24/7</div>\n              <div className=\"text-blue-100\">\n                {locale === 'zh' ? '在线访问' : 'Online Access'}\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAQe,eAAe,SAAS,EAAE,MAAM,EAA2C;IACxF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;IAAO;IAEzC,MAAM,mBAAmB;QACvB;YACE,OAAO,EAAE;YACT,MAAM,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC;YAC9B,MAAM,6NAAA,CAAA,kBAAe;YACrB,aAAa,WAAW,OAAO,YAAY;QAC7C;QACA;YACE,OAAO,EAAE;YACT,MAAM,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC;YAC3B,MAAM,iNAAA,CAAA,YAAS;YACf,aAAa,WAAW,OAAO,WAAW;QAC5C;QACA;YACE,OAAO,EAAE;YACT,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC;YAC/B,MAAM,6OAAA,CAAA,0BAAuB;YAC7B,aAAa,WAAW,OAAO,WAAW;QAC5C;KACD;IAED,MAAM,qBAAqB;QACzB;YACE,OAAO,WAAW,OAAO,SAAS;YAClC,MAAM,CAAC,CAAC,EAAE,OAAO,6BAA6B,CAAC;YAC/C,MAAM,uNAAA,CAAA,eAAY;YAClB,OAAO;YACP,aAAa,WAAW,OAAO,aAAa;QAC9C;QACA;YACE,OAAO,WAAW,OAAO,UAAU;YACnC,MAAM,CAAC,CAAC,EAAE,OAAO,2BAA2B,CAAC;YAC7C,MAAM,6NAAA,CAAA,kBAAe;YACrB,OAAO;YACP,aAAa,WAAW,OAAO,YAAY;QAC7C;QACA;YACE,OAAO,WAAW,OAAO,SAAS;YAClC,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC;YAC/B,MAAM,6OAAA,CAAA,0BAAuB;YAC7B,OAAO;YACP,aAAa,WAAW,OAAO,WAAW;QAC5C;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;gCACtB,WAAU;;oCAET,EAAE;kDACH,8OAAC,uNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6OAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CACnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC;wCAC5B,WAAU;;4CAET,EAAE;4CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;mCAXd;;;;;;;;;;;;;;;;;;;;;0BAoBf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,WAAW,OAAO,SAAS;;;;;;8CAE9B,8OAAC;oCAAE,WAAU;8CACV,WAAW,OAAO,cAAc;;;;;;;;;;;;sCAIrC,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,SAAS,IAAI;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDAAK,WAAU;8DACb,SAAS,KAAK;;;;;;;;;;;;sDAGnB,8OAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAEjB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAEvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,SAAS,IAAI;4CACnB,WAAU;;gDAET,WAAW,OAAO,SAAS;gDAAa;;;;;;;;mCAjBnC;;;;;;;;;;;;;;;;;;;;;0BA0BlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,WAAW,OAAO,WAAW;;;;;;sCAEhC,8OAAC;4BAAE,WAAU;sCACV,WAAW,OAAO,qBAAqB;;;;;;sCAE1C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC;4BACzB,WAAU;;8CAEV,8OAAC,qOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAC9B,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,WAAW,OAAO,SAAS;;;;;;;;;;;;0CAGhC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,WAAW,OAAO,SAAS;;;;;;;;;;;;0CAGhC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,WAAW,OAAO,SAAS;;;;;;;;;;;;0CAGhC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/HeartIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction HeartIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(HeartIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/ShieldCheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/BookOpenIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction BookOpenIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BookOpenIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/ExclamationTriangleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,wBAAwB,EAC/B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/MagnifyingGlassIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,oBAAoB,EAC3B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;gBAAA,UAAA;oBAAA,IAAA;oBAAA;iBAAA;;eACNC,UAAU;;SACV,2CAA2C;cAC3CC,IAAAA;YAAAA,GAAY,GAAA;iBACZC,MAAAA,IAAU,IAAA;wBAAA;4BACVC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,EAAE,eAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACd,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACAC,MAAU,CAAA,YAAA,CAAA;;qBACRC,YAAYnB;aACd;QACF,CAAE;QAAA,aAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}