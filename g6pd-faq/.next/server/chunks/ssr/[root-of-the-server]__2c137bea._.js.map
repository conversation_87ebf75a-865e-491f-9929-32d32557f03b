{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/language-switcher.tsx"], "sourcesContent": ["'use client'\n\nimport { useLocale } from 'next-intl'\nimport { useRouter, usePathname } from 'next/navigation'\nimport { useState } from 'react'\nimport { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline'\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' }\n]\n\nexport function LanguageSwitcher() {\n  const locale = useLocale()\n  const router = useRouter()\n  const pathname = usePathname()\n  const [isOpen, setIsOpen] = useState(false)\n\n  const currentLanguage = languages.find(lang => lang.code === locale)\n\n  const switchLanguage = (newLocale: string) => {\n    // Remove current locale from pathname\n    const pathWithoutLocale = pathname.replace(`/${locale}`, '')\n    // Navigate to new locale\n    router.push(`/${newLocale}${pathWithoutLocale}`)\n    setIsOpen(false)\n  }\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\"\n        aria-expanded={isOpen}\n        aria-haspopup=\"true\"\n      >\n        <GlobeAltIcon className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">\n          {currentLanguage?.flag} {currentLanguage?.name}\n        </span>\n        <span className=\"sm:hidden\">\n          {currentLanguage?.flag}\n        </span>\n        <ChevronDownIcon \n          className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} \n        />\n      </button>\n\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 z-10\" \n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* Dropdown */}\n          <div className=\"absolute right-0 z-20 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n            <div className=\"py-1\" role=\"menu\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => switchLanguage(language.code)}\n                  className={`flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100 ${\n                    locale === language.code \n                      ? 'bg-blue-50 text-blue-700' \n                      : 'text-gray-700'\n                  }`}\n                  role=\"menuitem\"\n                >\n                  <span className=\"mr-3 text-lg\">{language.flag}</span>\n                  <span>{language.name}</span>\n                  {locale === language.code && (\n                    <span className=\"ml-auto\">\n                      <svg className=\"h-4 w-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n\nexport default LanguageSwitcher\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEM,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAE7D,MAAM,iBAAiB,CAAC;QACtB,sCAAsC;QACtC,MAAM,oBAAoB,SAAS,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE;QACzD,yBAAyB;QACzB,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,mBAAmB;QAC/C,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,iBAAe;gBACf,iBAAc;;kCAEd,8OAAC,uNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBAAK,WAAU;;4BACb,iBAAiB;4BAAK;4BAAE,iBAAiB;;;;;;;kCAE5C,8OAAC;wBAAK,WAAU;kCACb,iBAAiB;;;;;;kCAEpB,8OAAC,6NAAA,CAAA,kBAAe;wBACd,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAI1E,wBACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAO,MAAK;sCACxB,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;oCAEC,SAAS,IAAM,eAAe,SAAS,IAAI;oCAC3C,WAAW,CAAC,uEAAuE,EACjF,WAAW,SAAS,IAAI,GACpB,6BACA,iBACJ;oCACF,MAAK;;sDAEL,8OAAC;4CAAK,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC7C,8OAAC;sDAAM,SAAS,IAAI;;;;;;wCACnB,WAAW,SAAS,IAAI,kBACvB,8OAAC;4CAAK,WAAU;sDACd,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;mCAd1J,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AA0BpC;uCAEe", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useLocale, useTranslations } from 'next-intl'\nimport { usePathname } from 'next/navigation'\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'\nimport LanguageSwitcher from './language-switcher'\n\nexport function Navigation() {\n  const locale = useLocale()\n  const t = useTranslations('navigation')\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const navigationItems = [\n    { href: `/${locale}`, label: t('home') },\n    { \n      href: `/${locale}/faq`, \n      label: t('faq'),\n      children: [\n        { href: `/${locale}/faq/medications`, label: t('medications') },\n        { href: `/${locale}/faq/diet`, label: t('diet') },\n        { href: `/${locale}/faq/symptoms`, label: t('symptoms') },\n        { href: `/${locale}/faq/treatment`, label: t('treatment') }\n      ]\n    },\n    {\n      href: `/${locale}/medications`,\n      label: t('medications'),\n      children: [\n        { href: `/${locale}/medications/chinese-medicine`, label: t('chineseMedicine') },\n        { href: `/${locale}/medications/oral-solutions`, label: t('oralSolutions') },\n        { href: `/${locale}/medications/western-medicine`, label: t('westernMedicine') }\n      ]\n    },\n    { href: `/${locale}/search`, label: t('search') },\n    { href: `/${locale}/about`, label: t('about') }\n  ]\n\n  const isActiveLink = (href: string) => {\n    if (href === `/${locale}`) {\n      return pathname === href\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and main navigation */}\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href={`/${locale}`} className=\"text-xl font-bold text-blue-600\">\n                G6PD FAQ\n              </Link>\n            </div>\n            \n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-6 md:flex md:space-x-8\">\n              {navigationItems.map((item) => (\n                <div key={item.href} className=\"relative group\">\n                  <Link\n                    href={item.href}\n                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${\n                      isActiveLink(item.href)\n                        ? 'border-blue-500 text-gray-900'\n                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'\n                    }`}\n                  >\n                    {item.label}\n                  </Link>\n                  \n                  {/* Dropdown menu */}\n                  {item.children && (\n                    <div className=\"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-1\">\n                        {item.children.map((child) => (\n                          <Link\n                            key={child.href}\n                            href={child.href}\n                            className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            {child.label}\n                          </Link>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Right side - Language switcher and mobile menu button */}\n          <div className=\"flex items-center space-x-4\">\n            <LanguageSwitcher />\n            \n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n              >\n                {isMobileMenuOpen ? (\n                  <XMarkIcon className=\"h-6 w-6\" />\n                ) : (\n                  <Bars3Icon className=\"h-6 w-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            {navigationItems.map((item) => (\n              <div key={item.href}>\n                <Link\n                  href={item.href}\n                  className={`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${\n                    isActiveLink(item.href)\n                      ? 'bg-blue-50 border-blue-500 text-blue-700'\n                      : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'\n                  }`}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.label}\n                </Link>\n                \n                {/* Mobile submenu */}\n                {item.children && (\n                  <div className=\"pl-6\">\n                    {item.children.map((child) => (\n                      <Link\n                        key={child.href}\n                        href={child.href}\n                        className=\"block pl-3 pr-4 py-2 text-sm text-gray-600 hover:text-gray-900\"\n                        onClick={() => setIsMobileMenuOpen(false)}\n                      >\n                        {child.label}\n                      </Link>\n                    ))}\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,kBAAkB;QACtB;YAAE,MAAM,CAAC,CAAC,EAAE,QAAQ;YAAE,OAAO,EAAE;QAAQ;QACvC;YACE,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;YACtB,OAAO,EAAE;YACT,UAAU;gBACR;oBAAE,MAAM,CAAC,CAAC,EAAE,OAAO,gBAAgB,CAAC;oBAAE,OAAO,EAAE;gBAAe;gBAC9D;oBAAE,MAAM,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC;oBAAE,OAAO,EAAE;gBAAQ;gBAChD;oBAAE,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC;oBAAE,OAAO,EAAE;gBAAY;gBACxD;oBAAE,MAAM,CAAC,CAAC,EAAE,OAAO,cAAc,CAAC;oBAAE,OAAO,EAAE;gBAAa;aAC3D;QACH;QACA;YACE,MAAM,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC;YAC9B,OAAO,EAAE;YACT,UAAU;gBACR;oBAAE,MAAM,CAAC,CAAC,EAAE,OAAO,6BAA6B,CAAC;oBAAE,OAAO,EAAE;gBAAmB;gBAC/E;oBAAE,MAAM,CAAC,CAAC,EAAE,OAAO,2BAA2B,CAAC;oBAAE,OAAO,EAAE;gBAAiB;gBAC3E;oBAAE,MAAM,CAAC,CAAC,EAAE,OAAO,6BAA6B,CAAC;oBAAE,OAAO,EAAE;gBAAmB;aAChF;QACH;QACA;YAAE,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC;YAAE,OAAO,EAAE;QAAU;QAChD;YAAE,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;YAAE,OAAO,EAAE;QAAS;KAC/C;IAED,MAAM,eAAe,CAAC;QACpB,IAAI,SAAS,CAAC,CAAC,EAAE,QAAQ,EAAE;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;wCAAE,WAAU;kDAAkC;;;;;;;;;;;8CAMxE,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAC,oFAAoF,EAC9F,aAAa,KAAK,IAAI,IAClB,kCACA,8EACJ;8DAED,KAAK,KAAK;;;;;;gDAIZ,KAAK,QAAQ,kBACZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,MAAM,IAAI;gEAChB,WAAU;0EAET,MAAM,KAAK;+DAJP,MAAM,IAAI;;;;;;;;;;;;;;;;2CAlBjB,KAAK,IAAI;;;;;;;;;;;;;;;;sCAkCzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0IAAA,CAAA,UAAgB;;;;;8CAGjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAET,iCACC,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAShC,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;;8CACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,sDAAsD,EAChE,aAAa,KAAK,IAAI,IAClB,6CACA,+FACJ;oCACF,SAAS,IAAM,oBAAoB;8CAElC,KAAK,KAAK;;;;;;gCAIZ,KAAK,QAAQ,kBACZ,8OAAC;oCAAI,WAAU;8CACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,MAAM,IAAI;4CAChB,WAAU;4CACV,SAAS,IAAM,oBAAoB;sDAElC,MAAM,KAAK;2CALP,MAAM,IAAI;;;;;;;;;;;2BAlBf,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAmCjC;uCAEe", "debugId": null}}]}