{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/terms/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\ninterface TermsPageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: TermsPageProps): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  return {\n    title: isZh ? '使用条款 - G6PD缺乏症FAQ' : 'Terms of Service - G6PD Deficiency FAQ',\n    description: isZh \n      ? '了解使用本网站的条款和条件。请仔细阅读这些条款，使用本网站即表示您同意这些条款。'\n      : 'Learn about the terms and conditions for using this website. Please read these terms carefully, as using this website indicates your agreement to these terms.',\n  }\n}\n\nexport default async function TermsPage({ params }: TermsPageProps) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">\n            {isZh ? '使用条款' : 'Terms of Service'}\n          </h1>\n          \n          <div className=\"prose prose-lg max-w-none\">\n            {isZh ? (\n              <>\n                <p className=\"text-gray-600 mb-6\">\n                  最后更新：2024年6月28日\n                </p>\n                \n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">1. 接受条款</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  通过访问和使用本网站，您同意遵守这些使用条款。如果您不同意这些条款，请不要使用本网站。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">2. 医疗免责声明</h2>\n                <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6\">\n                  <p className=\"text-yellow-800 font-semibold\">\n                    重要提醒：本网站提供的信息仅供参考，不能替代专业医疗建议、诊断或治疗。\n                  </p>\n                </div>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>本网站内容不构成医疗建议</li>\n                  <li>任何医疗决定都应咨询合格的医疗专业人员</li>\n                  <li>紧急情况请立即就医或拨打急救电话</li>\n                  <li>不要因为本网站信息而延误就医</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">3. 内容使用</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  本网站内容受版权保护。您可以：\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>个人非商业用途浏览和打印内容</li>\n                  <li>分享链接到本网站</li>\n                  <li>引用内容时注明来源</li>\n                </ul>\n                <p className=\"text-gray-700 mb-6\">\n                  您不得复制、修改、分发或商业使用本网站内容，除非获得明确授权。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">4. 用户行为</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  使用本网站时，您同意不会：\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>违反任何适用的法律法规</li>\n                  <li>传播虚假或误导性信息</li>\n                  <li>干扰网站正常运行</li>\n                  <li>尝试未经授权访问系统</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">5. 责任限制</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  在法律允许的最大范围内，我们不对因使用本网站而产生的任何直接、间接、偶然或后果性损害承担责任。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">6. 条款变更</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  我们保留随时修改这些条款的权利。修改后的条款将在网站上公布，继续使用网站即表示接受修改后的条款。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">7. 联系我们</h2>\n                <p className=\"text-gray-700\">\n                  如有疑问，请联系：<EMAIL>\n                </p>\n              </>\n            ) : (\n              <>\n                <p className=\"text-gray-600 mb-6\">\n                  Last updated: June 28, 2024\n                </p>\n                \n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">1. Acceptance of Terms</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  By accessing and using this website, you agree to comply with these terms of service. If you do not agree to these terms, please do not use this website.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">2. Medical Disclaimer</h2>\n                <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6\">\n                  <p className=\"text-yellow-800 font-semibold\">\n                    Important Notice: The information provided on this website is for reference only and cannot replace professional medical advice, diagnosis, or treatment.\n                  </p>\n                </div>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>Website content does not constitute medical advice</li>\n                  <li>Any medical decisions should consult qualified medical professionals</li>\n                  <li>In emergencies, seek immediate medical attention or call emergency services</li>\n                  <li>Do not delay seeking medical care because of information on this website</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">3. Content Usage</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  Website content is protected by copyright. You may:\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>Browse and print content for personal non-commercial use</li>\n                  <li>Share links to this website</li>\n                  <li>Quote content with proper attribution</li>\n                </ul>\n                <p className=\"text-gray-700 mb-6\">\n                  You may not copy, modify, distribute, or commercially use website content without explicit authorization.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">4. User Conduct</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  When using this website, you agree not to:\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>Violate any applicable laws or regulations</li>\n                  <li>Spread false or misleading information</li>\n                  <li>Interfere with normal website operation</li>\n                  <li>Attempt unauthorized access to systems</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">5. Limitation of Liability</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  To the maximum extent permitted by law, we are not liable for any direct, indirect, incidental, or consequential damages arising from the use of this website.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">6. Terms Changes</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  We reserve the right to modify these terms at any time. Modified terms will be posted on the website, and continued use indicates acceptance of the modified terms.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">7. Contact Us</h2>\n                <p className=\"text-gray-700\">\n                  For questions, please contact: <EMAIL>\n                </p>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAkB;IAC/D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,OAAO;QACL,OAAO,OAAO,sBAAsB;QACpC,aAAa,OACT,6CACA;IACN;AACF;AAEe,eAAe,UAAU,EAAE,MAAM,EAAkB;IAChE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,OAAO,SAAS;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;kCACZ,qBACC;;8CACE,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;8CAI/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAEN,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;yDAK/B;;8CACE,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;8CAI/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAEN,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,KAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACNC,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}