{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\n\ninterface NavigationProps {\n  locale: string\n}\n\nexport default function Navigation({ locale }: NavigationProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const isZh = locale === 'zh'\n\n  const navigation = [\n    {\n      name: isZh ? '首页' : 'Home',\n      href: `/${locale}`,\n    },\n    {\n      name: isZh ? '常见问题' : 'FAQ',\n      href: `/${locale}/faq`,\n      children: [\n        {\n          name: isZh ? '中药禁忌' : 'Chinese Medicine',\n          href: `/${locale}/faq/medications/chinese-medicine`,\n        },\n        {\n          name: isZh ? '口服液安全' : 'Oral Solutions',\n          href: `/${locale}/faq/medications/oral-solutions`,\n        },\n        {\n          name: isZh ? '症状识别' : 'Symptoms',\n          href: `/${locale}/faq/symptoms`,\n        },\n        {\n          name: isZh ? '饮食指导' : 'Diet Guide',\n          href: `/${locale}/faq/diet`,\n        },\n      ],\n    },\n    {\n      name: isZh ? '用药指导' : 'Medication Guide',\n      href: `/${locale}/medications`,\n    },\n    {\n      name: isZh ? '搜索' : 'Search',\n      href: `/${locale}/search`,\n    },\n  ]\n\n  const otherLocale = locale === 'zh' ? 'en' : 'zh'\n  const otherLocaleName = locale === 'zh' ? 'English' : '中文'\n\n  return (\n    <nav className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href={`/${locale}`} className=\"flex-shrink-0\">\n              <div className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">G6</span>\n                </div>\n                <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  {isZh ? 'G6PD指导' : 'G6PD Guide'}\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                <Link\n                  href={item.href}\n                  className=\"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n                >\n                  {item.name}\n                </Link>\n                {item.children && (\n                  <div className=\"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                    <div className=\"py-1\">\n                      {item.children.map((child) => (\n                        <Link\n                          key={child.name}\n                          href={child.href}\n                          className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\"\n                        >\n                          {child.name}\n                        </Link>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n            \n            {/* Language Switcher */}\n            <Link\n              href={`/${otherLocale}`}\n              className=\"text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors border border-gray-300 rounded-md\"\n            >\n              {otherLocaleName}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                {isMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                  {item.children && (\n                    <div className=\"pl-6 space-y-1\">\n                      {item.children.map((child) => (\n                        <Link\n                          key={child.name}\n                          href={child.href}\n                          className=\"text-gray-600 hover:text-blue-600 block px-3 py-2 text-sm\"\n                          onClick={() => setIsMenuOpen(false)}\n                        >\n                          {child.name}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n              <Link\n                href={`/${otherLocale}`}\n                className=\"text-gray-500 hover:text-blue-600 block px-3 py-2 text-base font-medium border-t border-gray-200 mt-4 pt-4\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {otherLocaleName}\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,WAAW,EAAE,MAAM,EAAmB;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,OAAO,WAAW;IAExB,MAAM,aAAa;QACjB;YACE,MAAM,OAAO,OAAO;YACpB,MAAM,CAAC,CAAC,EAAE,QAAQ;QACpB;QACA;YACE,MAAM,OAAO,SAAS;YACtB,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;YACtB,UAAU;gBACR;oBACE,MAAM,OAAO,SAAS;oBACtB,MAAM,CAAC,CAAC,EAAE,OAAO,iCAAiC,CAAC;gBACrD;gBACA;oBACE,MAAM,OAAO,UAAU;oBACvB,MAAM,CAAC,CAAC,EAAE,OAAO,+BAA+B,CAAC;gBACnD;gBACA;oBACE,MAAM,OAAO,SAAS;oBACtB,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC;gBACjC;gBACA;oBACE,MAAM,OAAO,SAAS;oBACtB,MAAM,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC;gBAC7B;aACD;QACH;QACA;YACE,MAAM,OAAO,SAAS;YACtB,MAAM,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC;QAChC;QACA;YACE,MAAM,OAAO,OAAO;YACpB,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC;QAC3B;KACD;IAED,MAAM,cAAc,WAAW,OAAO,OAAO;IAC7C,MAAM,kBAAkB,WAAW,OAAO,YAAY;IAEtD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;gCAAE,WAAU;0CAClC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDACb,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;sCAO3B,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;4CAEX,KAAK,QAAQ,kBACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,MAAM,IAAI;4DAChB,WAAU;sEAET,MAAM,IAAI;2DAJN,MAAM,IAAI;;;;;;;;;;;;;;;;uCAZjB,KAAK,IAAI;;;;;8CA0BrB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,aAAa;oCACvB,WAAU;8CAET;;;;;;;;;;;;sCAKL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC7D,2BACC,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;6DAErE,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;;sDACC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,cAAc;sDAE5B,KAAK,IAAI;;;;;;wCAEX,KAAK,QAAQ,kBACZ,8OAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,MAAM,IAAI;oDAChB,WAAU;oDACV,SAAS,IAAM,cAAc;8DAE5B,MAAM,IAAI;mDALN,MAAM,IAAI;;;;;;;;;;;mCAZf,KAAK,IAAI;;;;;0CAwBrB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,aAAa;gCACvB,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB", "debugId": null}}]}