{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/faq-data.ts"], "sourcesContent": ["export interface FAQItem {\n  id: string\n  question: string\n  answer: string\n  category: string\n  subcategory?: string\n  keywords: string[]\n  searchVolume?: number\n  locale: 'zh' | 'en'\n}\n\nexport interface FAQCategory {\n  id: string\n  name: string\n  description: string\n  icon: string\n  subcategories?: FAQSubcategory[]\n}\n\nexport interface FAQSubcategory {\n  id: string\n  name: string\n  description: string\n}\n\n// FAQ Categories\nexport const faqCategories: Record<'zh' | 'en', FAQCategory[]> = {\n  zh: [\n    {\n      id: 'medications',\n      name: '用药安全',\n      description: '了解G6PD缺乏症患者的用药禁忌和安全指导',\n      icon: '💊',\n      subcategories: [\n        {\n          id: 'chinese-medicine',\n          name: '中药禁忌',\n          description: '不能使用的中药成分和药物'\n        },\n        {\n          id: 'oral-solutions',\n          name: '口服液安全',\n          description: '各种口服液药物的安全性评估'\n        },\n        {\n          id: 'western-medicine',\n          name: '西药指导',\n          description: '西药使用注意事项'\n        }\n      ]\n    },\n    {\n      id: 'symptoms',\n      name: '症状识别',\n      description: '学习识别G6PD缺乏症的症状和紧急情况',\n      icon: '🩺'\n    },\n    {\n      id: 'diet',\n      name: '饮食指导',\n      description: '安全的饮食建议和食物禁忌',\n      icon: '🥗'\n    },\n    {\n      id: 'treatment',\n      name: '治疗方案',\n      description: '治疗选择和医疗管理',\n      icon: '🏥'\n    }\n  ],\n  en: [\n    {\n      id: 'medications',\n      name: 'Medication Safety',\n      description: 'Learn about medication contraindications and safety guidelines for G6PD deficiency patients',\n      icon: '💊',\n      subcategories: [\n        {\n          id: 'chinese-medicine',\n          name: 'Chinese Medicine',\n          description: 'Chinese medicine ingredients and drugs to avoid'\n        },\n        {\n          id: 'oral-solutions',\n          name: 'Oral Solutions',\n          description: 'Safety assessment of various oral solution medications'\n        },\n        {\n          id: 'western-medicine',\n          name: 'Western Medicine',\n          description: 'Western medicine usage precautions'\n        }\n      ]\n    },\n    {\n      id: 'symptoms',\n      name: 'Symptom Recognition',\n      description: 'Learn to recognize G6PD deficiency symptoms and emergency situations',\n      icon: '🩺'\n    },\n    {\n      id: 'diet',\n      name: 'Diet Guide',\n      description: 'Safe dietary recommendations and food restrictions',\n      icon: '🥗'\n    },\n    {\n      id: 'treatment',\n      name: 'Treatment Options',\n      description: 'Treatment choices and medical management',\n      icon: '🏥'\n    }\n  ]\n}\n\n// Sample FAQ data based on long-tail keywords\nexport const faqData: FAQItem[] = [\n  // Chinese Medicine FAQs (based on 蚕豆病中药长尾词.md)\n  {\n    id: 'zh-chinese-medicine-1',\n    question: '蚕豆病不能吃的中药有哪些？',\n    answer: 'G6PD缺乏症（蚕豆病）患者需要避免以下中药成分：薄荷、金银花、黄连、大黄、麻黄、茵陈等。这些中药可能引起溶血性贫血发作。使用任何中药前，请务必咨询医生并告知您的G6PD缺乏症状况。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '中药', '禁忌', '不能吃'],\n    searchVolume: 472000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-chinese-medicine-2',\n    question: '蚕豆病哪些中药不能吃？',\n    answer: '蚕豆病患者应避免使用含有以下成分的中药：1. 薄荷类：薄荷、薄荷脑等；2. 清热解毒类：金银花、黄连、黄芩等；3. 泻下类：大黄、芒硝等；4. 发散风寒类：麻黄、桂枝等。建议在使用任何中药前咨询专业中医师。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '哪些中药', '不能吃'],\n    searchVolume: 235000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-chinese-medicine-3',\n    question: '蚕豆病能吃的中药有哪些？',\n    answer: '蚕豆病患者可以安全使用的中药包括：1. 补益类：人参、党参、黄芪、当归等；2. 健脾类：白术、茯苓、山药等；3. 养血类：熟地黄、白芍、阿胶等。但即使是安全的中药，也建议在医生指导下使用，并密切观察身体反应。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '能吃', '中药'],\n    searchVolume: 169000,\n    locale: 'zh'\n  },\n  \n  // Oral Solutions FAQs (based on 蚕豆病口服液长尾词.md)\n  {\n    id: 'zh-oral-solutions-1',\n    question: '蚕豆病能吃双黄连口服液吗？',\n    answer: '蚕豆病患者不建议使用双黄连口服液。双黄连口服液含有金银花、黄芩、连翘等成分，其中金银花和黄芩可能引起G6PD缺乏症患者发生溶血反应。如需治疗感冒等症状，请咨询医生选择安全的替代药物。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '双黄连口服液'],\n    searchVolume: 24200,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-oral-solutions-2',\n    question: '蚕豆病可以吃抗病毒口服液吗？',\n    answer: '蚕豆病患者使用抗病毒口服液需要谨慎。不同品牌的抗病毒口服液成分不同，有些含有板蓝根、金银花等可能引起溶血的成分。建议：1. 使用前仔细查看成分表；2. 咨询医生或药师；3. 选择不含禁忌成分的产品；4. 使用后密切观察身体反应。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '抗病毒口服液'],\n    searchVolume: 18700,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-oral-solutions-3',\n    question: '蚕豆病能吃保济口服液吗？',\n    answer: '蚕豆病患者可以谨慎使用保济口服液。保济口服液主要含有钩藤、薄荷、菊花等成分，其中薄荷可能对部分G6PD缺乏症患者有影响。建议：1. 首次使用时小剂量试用；2. 密切观察是否出现乏力、面色苍白等症状；3. 如有不适立即停用并就医。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '保济口服液'],\n    searchVolume: 1440,\n    locale: 'zh'\n  },\n\n  // Symptoms FAQs\n  {\n    id: 'zh-symptoms-1',\n    question: '蚕豆病发作有什么症状？',\n    answer: 'G6PD缺乏症急性发作的主要症状包括：1. 急性溶血症状：面色苍白、乏力、头晕；2. 黄疸：皮肤和眼白发黄；3. 尿液改变：尿色加深，呈茶色或酱油色；4. 其他症状：发热、恶心、呕吐、腹痛。如出现这些症状，应立即停用可疑药物并紧急就医。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '发作', '症状'],\n    searchVolume: 89000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-symptoms-2',\n    question: '蚕豆病黄疸多久能好？',\n    answer: '蚕豆病引起的黄疸恢复时间因人而异，通常需要1-2周。恢复时间取决于：1. 溶血程度：轻度溶血3-7天，重度溶血可能需要2-3周；2. 治疗及时性：早期治疗恢复更快；3. 个体差异：年龄、体质等因素影响恢复速度。期间需要充分休息、多饮水，避免再次接触诱发因素。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '黄疸', '多久', '恢复'],\n    searchVolume: 34500,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-symptoms-3',\n    question: '蚕豆病尿液什么颜色？',\n    answer: '蚕豆病发作时，尿液颜色会发生明显变化：1. 正常情况：淡黄色透明；2. 轻度溶血：尿液呈深黄色；3. 中度溶血：尿液呈茶色或可乐色；4. 重度溶血：尿液呈酱油色或红褐色。尿液颜色变化是血红蛋白尿的表现，提示红细胞大量破坏，需要立即就医。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '尿液', '颜色', '血红蛋白尿'],\n    searchVolume: 28900,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-symptoms-4',\n    question: '蚕豆病会遗传吗？',\n    answer: '蚕豆病（G6PD缺乏症）是一种X连锁隐性遗传病，具有明显的遗传特点：1. 男性患病率高：男性只需一个致病基因即可发病；2. 女性多为携带者：需要两个致病基因才发病，但可能有轻微症状；3. 遗传规律：患病男性的儿子不会患病，女儿都是携带者；携带者女性的儿子有50%概率患病。建议有家族史的夫妇进行遗传咨询。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '遗传', 'X连锁', '家族史'],\n    searchVolume: 67800,\n    locale: 'zh'\n  },\n\n  // Diet FAQs\n  {\n    id: 'zh-diet-1',\n    question: '蚕豆病不能吃什么食物？',\n    answer: '蚕豆病患者需要严格避免的食物包括：1. 绝对禁忌：蚕豆及其制品（蚕豆、蚕豆粉、蚕豆淀粉等）；2. 谨慎食用：苦瓜、丝瓜、冬瓜籽等部分蔬菜；3. 适量控制：蓝莓、葡萄等高抗氧化水果。此外，要仔细阅读食品标签，避免含有蚕豆成分的加工食品。',\n    category: 'diet',\n    keywords: ['蚕豆病', '不能吃', '食物', '禁忌'],\n    searchVolume: 156000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-diet-2',\n    question: '蚕豆病能吃豆腐吗？',\n    answer: '蚕豆病患者可以安全食用豆腐。豆腐是由大豆制成，与蚕豆完全不同：1. 大豆制品安全：豆腐、豆浆、豆干等大豆制品都可以正常食用；2. 营养价值高：豆腐富含优质蛋白质，有助于身体恢复；3. 注意区分：要确保购买的是纯大豆制品，不含蚕豆成分。建议选择正规厂家生产的豆制品。',\n    category: 'diet',\n    keywords: ['蚕豆病', '豆腐', '大豆', '安全'],\n    searchVolume: 45600,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-diet-3',\n    question: '蚕豆病可以吃蓝莓吗？',\n    answer: '蚕豆病患者可以适量食用蓝莓，但需要注意：1. 少量安全：偶尔少量食用通常没有问题；2. 避免大量：蓝莓含有高浓度抗氧化物质，大量食用可能引起不适；3. 个体差异：部分敏感患者可能需要完全避免；4. 观察反应：首次食用时应少量试用，观察身体反应。建议咨询医生后决定是否食用。',\n    category: 'diet',\n    keywords: ['蚕豆病', '蓝莓', '抗氧化', '适量'],\n    searchVolume: 23400,\n    locale: 'zh'\n  },\n\n  // Treatment FAQs\n  {\n    id: 'zh-treatment-1',\n    question: '蚕豆病怎么治疗？',\n    answer: '蚕豆病的治疗主要分为急性期和预防期：1. 急性期治疗：立即停用诱发药物，输液维持水电解质平衡，严重时需要输血，监测肾功能；2. 支持治疗：补充叶酸、维生素E等，促进红细胞生成；3. 预防措施：避免诱发因素，建立用药清单，定期体检；4. 长期管理：健康教育，家属培训，携带医疗卡片。治疗需要在医生指导下进行。',\n    category: 'treatment',\n    keywords: ['蚕豆病', '治疗', '急性期', '预防'],\n    searchVolume: 78900,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-treatment-2',\n    question: '蚕豆病需要住院吗？',\n    answer: '蚕豆病是否需要住院取决于病情严重程度：1. 轻度症状：轻微乏力、食欲不振，可门诊观察治疗；2. 中度症状：明显黄疸、尿液颜色改变，建议住院监测；3. 重度症状：严重贫血、呼吸困难、肾功能异常，必须住院治疗；4. 儿童患者：由于病情变化快，通常建议住院观察。住院期间可以得到及时的监测和治疗。',\n    category: 'treatment',\n    keywords: ['蚕豆病', '住院', '病情', '监测'],\n    searchVolume: 34200,\n    locale: 'zh'\n  }\n]\n\n// English FAQ data\nexport const englishFaqData: FAQItem[] = [\n  {\n    id: 'en-chinese-medicine-1',\n    question: 'What Chinese medicines should G6PD deficiency patients avoid?',\n    answer: 'G6PD deficiency patients should avoid Chinese medicines containing: mint, honeysuckle, coptis, rhubarb, ephedra, and artemisia. These ingredients may trigger hemolytic anemia attacks. Always consult a doctor before using any Chinese medicine and inform them of your G6PD deficiency status.',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['G6PD deficiency', 'Chinese medicine', 'contraindications', 'avoid'],\n    locale: 'en'\n  },\n  {\n    id: 'en-oral-solutions-1',\n    question: 'Can G6PD deficiency patients take Shuanghuanglian oral solution?',\n    answer: 'G6PD deficiency patients are not recommended to use Shuanghuanglian oral solution. It contains honeysuckle, scutellaria, and forsythia, where honeysuckle and scutellaria may cause hemolytic reactions in G6PD deficient patients. For cold treatment, please consult a doctor for safe alternative medications.',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['G6PD deficiency', 'Shuanghuanglian', 'oral solution'],\n    locale: 'en'\n  },\n  {\n    id: 'en-symptoms-1',\n    question: 'What are the symptoms of G6PD deficiency crisis?',\n    answer: 'Main symptoms of acute G6PD deficiency crisis include: 1. Acute hemolysis: pallor, fatigue, dizziness; 2. Jaundice: yellowing of skin and eyes; 3. Urine changes: dark urine (tea or cola-colored); 4. Other symptoms: fever, nausea, vomiting, abdominal pain. If these symptoms occur, immediately stop the suspected medication and seek emergency medical care.',\n    category: 'symptoms',\n    keywords: ['G6PD deficiency', 'crisis', 'symptoms'],\n    searchVolume: 89000,\n    locale: 'en'\n  },\n  {\n    id: 'en-symptoms-2',\n    question: 'How long does jaundice last in G6PD deficiency?',\n    answer: 'Jaundice recovery time in G6PD deficiency varies by individual, typically requiring 1-2 weeks. Recovery depends on: 1. Degree of hemolysis: mild hemolysis 3-7 days, severe hemolysis may take 2-3 weeks; 2. Treatment timeliness: early treatment leads to faster recovery; 3. Individual differences: age, constitution affect recovery speed. During this period, adequate rest and hydration are needed, avoiding re-exposure to triggering factors.',\n    category: 'symptoms',\n    keywords: ['G6PD deficiency', 'jaundice', 'recovery', 'duration'],\n    searchVolume: 34500,\n    locale: 'en'\n  },\n  {\n    id: 'en-symptoms-3',\n    question: 'What color is urine in G6PD deficiency?',\n    answer: 'During G6PD deficiency crisis, urine color changes significantly: 1. Normal: light yellow and clear; 2. Mild hemolysis: dark yellow urine; 3. Moderate hemolysis: tea or cola-colored urine; 4. Severe hemolysis: dark brown or reddish-brown urine. Urine color change indicates hemoglobinuria, suggesting massive red blood cell destruction requiring immediate medical attention.',\n    category: 'symptoms',\n    keywords: ['G6PD deficiency', 'urine', 'color', 'hemoglobinuria'],\n    searchVolume: 28900,\n    locale: 'en'\n  },\n  {\n    id: 'en-symptoms-4',\n    question: 'Is G6PD deficiency hereditary?',\n    answer: 'G6PD deficiency is an X-linked recessive genetic disorder with distinct inheritance patterns: 1. Higher male prevalence: males need only one disease gene to be affected; 2. Females mostly carriers: need two disease genes to be affected, but may have mild symptoms; 3. Inheritance pattern: affected males\\' sons won\\'t be affected, daughters are all carriers; carrier females\\' sons have 50% chance of being affected. Genetic counseling is recommended for couples with family history.',\n    category: 'symptoms',\n    keywords: ['G6PD deficiency', 'hereditary', 'X-linked', 'family history'],\n    searchVolume: 67800,\n    locale: 'en'\n  },\n\n  // Diet FAQs - English\n  {\n    id: 'en-diet-1',\n    question: 'What foods should G6PD deficiency patients avoid?',\n    answer: 'G6PD deficiency patients must strictly avoid: 1. Absolute contraindications: fava beans and products (fava beans, fava bean flour, fava bean starch, etc.); 2. Use with caution: bitter melon, loofah, winter melon seeds and some vegetables; 3. Moderate control: blueberries, grapes and other high-antioxidant fruits. Additionally, carefully read food labels to avoid processed foods containing fava bean ingredients.',\n    category: 'diet',\n    keywords: ['G6PD deficiency', 'avoid foods', 'contraindications', 'fava beans'],\n    searchVolume: 156000,\n    locale: 'en'\n  },\n  {\n    id: 'en-diet-2',\n    question: 'Can G6PD deficiency patients eat tofu?',\n    answer: 'G6PD deficiency patients can safely consume tofu. Tofu is made from soybeans, completely different from fava beans: 1. Soy products are safe: tofu, soy milk, dried tofu and other soy products can be consumed normally; 2. High nutritional value: tofu is rich in high-quality protein, helping body recovery; 3. Note distinction: ensure purchasing pure soy products without fava bean ingredients. Recommend choosing products from reputable manufacturers.',\n    category: 'diet',\n    keywords: ['G6PD deficiency', 'tofu', 'soybeans', 'safe'],\n    searchVolume: 45600,\n    locale: 'en'\n  },\n  {\n    id: 'en-diet-3',\n    question: 'Can G6PD deficiency patients eat blueberries?',\n    answer: 'G6PD deficiency patients can consume blueberries in moderation, but should note: 1. Small amounts are safe: occasional small consumption is usually fine; 2. Avoid large quantities: blueberries contain high concentrations of antioxidants, large amounts may cause discomfort; 3. Individual differences: some sensitive patients may need complete avoidance; 4. Observe reactions: first-time consumption should be in small amounts, observing body reactions. Recommend consulting a doctor before consumption.',\n    category: 'diet',\n    keywords: ['G6PD deficiency', 'blueberries', 'antioxidants', 'moderation'],\n    searchVolume: 23400,\n    locale: 'en'\n  },\n\n  // Treatment FAQs - English\n  {\n    id: 'en-treatment-1',\n    question: 'How is G6PD deficiency treated?',\n    answer: 'G6PD deficiency treatment is divided into acute phase and prevention: 1. Acute treatment: immediately stop triggering medications, IV fluids for electrolyte balance, blood transfusion if severe, monitor kidney function; 2. Supportive treatment: supplement folic acid, vitamin E, promote red blood cell production; 3. Prevention: avoid triggering factors, establish medication list, regular checkups; 4. Long-term management: health education, family training, carry medical alert card. Treatment must be under medical supervision.',\n    category: 'treatment',\n    keywords: ['G6PD deficiency', 'treatment', 'acute phase', 'prevention'],\n    searchVolume: 78900,\n    locale: 'en'\n  },\n  {\n    id: 'en-treatment-2',\n    question: 'Does G6PD deficiency require hospitalization?',\n    answer: 'Whether G6PD deficiency requires hospitalization depends on severity: 1. Mild symptoms: slight fatigue, poor appetite, can be observed as outpatient; 2. Moderate symptoms: obvious jaundice, urine color changes, recommend hospitalization for monitoring; 3. Severe symptoms: severe anemia, breathing difficulties, kidney dysfunction, must be hospitalized; 4. Pediatric patients: due to rapid condition changes, usually recommend hospitalization for observation. Hospitalization provides timely monitoring and treatment.',\n    category: 'treatment',\n    keywords: ['G6PD deficiency', 'hospitalization', 'severity', 'monitoring'],\n    searchVolume: 34200,\n    locale: 'en'\n  }\n]\n\n// Combine all FAQ data\nexport const allFaqData = [...faqData, ...englishFaqData]\n\n// Helper functions\nexport function getFaqsByCategory(category: string, locale: 'zh' | 'en'): FAQItem[] {\n  return allFaqData.filter(faq => faq.category === category && faq.locale === locale)\n}\n\nexport function getFaqsBySubcategory(category: string, subcategory: string, locale: 'zh' | 'en'): FAQItem[] {\n  return allFaqData.filter(faq => \n    faq.category === category && \n    faq.subcategory === subcategory && \n    faq.locale === locale\n  )\n}\n\nexport function searchFaqs(query: string, locale: 'zh' | 'en', category?: string): FAQItem[] {\n  if (!query.trim()) return []\n\n  const searchTerm = query.toLowerCase().trim()\n\n  return allFaqData.filter(faq => {\n    if (faq.locale !== locale) return false\n    if (category && faq.category !== category) return false\n\n    // Search in question, answer, and keywords\n    const questionMatch = faq.question.toLowerCase().includes(searchTerm)\n    const answerMatch = faq.answer.toLowerCase().includes(searchTerm)\n    const keywordMatch = faq.keywords.some(keyword =>\n      keyword.toLowerCase().includes(searchTerm)\n    )\n\n    return questionMatch || answerMatch || keywordMatch\n  }).sort((a, b) => {\n    // Sort by search volume (higher first) and then by relevance\n    const aVolume = a.searchVolume || 0\n    const bVolume = b.searchVolume || 0\n    return bVolume - aVolume\n  })\n}\n\nexport function getPopularSearchTerms(locale: 'zh' | 'en'): string[] {\n  const terms = allFaqData\n    .filter(faq => faq.locale === locale && faq.searchVolume)\n    .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))\n    .slice(0, 10)\n    .flatMap(faq => faq.keywords)\n\n  // Remove duplicates and return top terms\n  return [...new Set(terms)].slice(0, 8)\n}\n\nexport function getSuggestedQuestions(query: string, locale: 'zh' | 'en'): FAQItem[] {\n  if (!query.trim()) return getPopularFaqs(locale, 5)\n\n  const searchTerm = query.toLowerCase().trim()\n\n  return allFaqData\n    .filter(faq => {\n      if (faq.locale !== locale) return false\n      return faq.keywords.some(keyword =>\n        keyword.toLowerCase().includes(searchTerm)\n      )\n    })\n    .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))\n    .slice(0, 5)\n}\n\nexport function getPopularFaqs(locale: 'zh' | 'en', limit: number = 10): FAQItem[] {\n  return allFaqData\n    .filter(faq => faq.locale === locale && faq.searchVolume)\n    .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))\n    .slice(0, limit)\n}\n\nexport function getFaqById(id: string): FAQItem | undefined {\n  return allFaqData.find(faq => faq.id === id)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA0BO,MAAM,gBAAoD;IAC/D,IAAI;QACF;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;KACD;IACD,IAAI;QACF;YACE,IAAI;Y<PERSON><PERSON>,MAAM;Y<PERSON><PERSON>,aAAa;YACb,MAAM;YAC<PERSON>,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;KACD;AACH;AAGO,MAAM,UAAqB;IAChC,+CAA+C;IAC/C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAM;YAAM;SAAM;QACpC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAQ;SAAM;QAChC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAM;SAAK;QAC7B,cAAc;QACd,QAAQ;IACV;IAEA,8CAA8C;IAC9C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAS;QAC3B,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAS;QAC3B,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAQ;QAC1B,cAAc;QACd,QAAQ;IACV;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;SAAK;QAC7B,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAM;SAAK;QACnC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAM;SAAQ;QACtC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAO;SAAM;QACrC,cAAc;QACd,QAAQ;IACV;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAO;YAAM;SAAK;QACpC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAM;SAAK;QACnC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAO;SAAK;QACpC,cAAc;QACd,QAAQ;IACV;IAEA,iBAAiB;IACjB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAO;SAAK;QACpC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAM;SAAK;QACnC,cAAc;QACd,QAAQ;IACV;CACD;AAGM,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAmB;YAAoB;YAAqB;SAAQ;QAC/E,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAmB;YAAmB;SAAgB;QACjE,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAU;SAAW;QACnD,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAY;YAAY;SAAW;QACjE,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAS;YAAS;SAAiB;QACjE,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAc;YAAY;SAAiB;QACzE,cAAc;QACd,QAAQ;IACV;IAEA,sBAAsB;IACtB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAe;YAAqB;SAAa;QAC/E,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAQ;YAAY;SAAO;QACzD,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAe;YAAgB;SAAa;QAC1E,cAAc;QACd,QAAQ;IACV;IAEA,2BAA2B;IAC3B;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAa;YAAe;SAAa;QACvE,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAmB;YAAY;SAAa;QAC1E,cAAc;QACd,QAAQ;IACV;CACD;AAGM,MAAM,aAAa;OAAI;OAAY;CAAe;AAGlD,SAAS,kBAAkB,QAAgB,EAAE,MAAmB;IACrE,OAAO,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,YAAY,IAAI,MAAM,KAAK;AAC9E;AAEO,SAAS,qBAAqB,QAAgB,EAAE,WAAmB,EAAE,MAAmB;IAC7F,OAAO,WAAW,MAAM,CAAC,CAAA,MACvB,IAAI,QAAQ,KAAK,YACjB,IAAI,WAAW,KAAK,eACpB,IAAI,MAAM,KAAK;AAEnB;AAEO,SAAS,WAAW,KAAa,EAAE,MAAmB,EAAE,QAAiB;IAC9E,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO,EAAE;IAE5B,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,WAAW,MAAM,CAAC,CAAA;QACvB,IAAI,IAAI,MAAM,KAAK,QAAQ,OAAO;QAClC,IAAI,YAAY,IAAI,QAAQ,KAAK,UAAU,OAAO;QAElD,2CAA2C;QAC3C,MAAM,gBAAgB,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC1D,MAAM,cAAc,IAAI,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;QACtD,MAAM,eAAe,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAA,UACrC,QAAQ,WAAW,GAAG,QAAQ,CAAC;QAGjC,OAAO,iBAAiB,eAAe;IACzC,GAAG,IAAI,CAAC,CAAC,GAAG;QACV,6DAA6D;QAC7D,MAAM,UAAU,EAAE,YAAY,IAAI;QAClC,MAAM,UAAU,EAAE,YAAY,IAAI;QAClC,OAAO,UAAU;IACnB;AACF;AAEO,SAAS,sBAAsB,MAAmB;IACvD,MAAM,QAAQ,WACX,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,UAAU,IAAI,YAAY,EACvD,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG,IACT,OAAO,CAAC,CAAA,MAAO,IAAI,QAAQ;IAE9B,yCAAyC;IACzC,OAAO;WAAI,IAAI,IAAI;KAAO,CAAC,KAAK,CAAC,GAAG;AACtC;AAEO,SAAS,sBAAsB,KAAa,EAAE,MAAmB;IACtE,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO,eAAe,QAAQ;IAEjD,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,WACJ,MAAM,CAAC,CAAA;QACN,IAAI,IAAI,MAAM,KAAK,QAAQ,OAAO;QAClC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAA,UACvB,QAAQ,WAAW,GAAG,QAAQ,CAAC;IAEnC,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,eAAe,MAAmB,EAAE,QAAgB,EAAE;IACpE,OAAO,WACJ,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,UAAU,IAAI,YAAY,EACvD,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;AAC3C", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/faq/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { faqCategories, allFaqData } from '@/lib/faq-data'\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  const title = isZh ? '常见问题解答 - G6PD缺乏症（蚕豆病）' : 'FAQ - G6PD Deficiency (Favism)'\n  const description = isZh\n    ? '关于G6PD缺乏症（蚕豆病）的专业问题解答，包括用药安全、症状识别、饮食指导等'\n    : 'Professional answers about G6PD deficiency (favism), including medication safety, symptom recognition, dietary guidance and more'\n\n  return {\n    title,\n    description,\n    openGraph: {\n      title,\n      description,\n      type: 'website',\n    },\n  }\n}\n\nexport default async function FAQPage({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n              {isZh ? '常见问题解答' : 'Frequently Asked Questions'}\n            </h1>\n            <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n              {isZh\n                ? '关于G6PD缺乏症（蚕豆病）的专业问题解答，帮助您更好地了解和管理这种疾病'\n                : 'Professional answers about G6PD deficiency (favism) to help you better understand and manage this condition'\n              }\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* FAQ Categories */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n          {faqCategories[locale as 'zh' | 'en'].map((category) => (\n            <Link\n              key={category.id}\n              href={`/${locale}/faq/${category.id}`}\n              className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow group\"\n            >\n              <div className=\"text-4xl mb-4\">{category.icon}</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-gray-900 group-hover:text-blue-600\">\n                {category.name}\n              </h3>\n              <p className=\"text-gray-600 text-sm\">\n                {category.description}\n              </p>\n            </Link>\n          ))}\n        </div>\n\n        {/* Popular Questions */}\n        <div className=\"bg-white rounded-lg shadow-md p-8\">\n          <h2 className=\"text-2xl font-bold mb-6 text-gray-900\">\n            {isZh ? '热门问题' : 'Popular Questions'}\n          </h2>\n          <div className=\"space-y-6\">\n            {allFaqData\n              .filter(faq => faq.locale === locale)\n              .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))\n              .slice(0, 6)\n              .map((faq) => (\n                <div key={faq.id} className=\"border-b border-gray-200 pb-6 last:border-b-0\">\n                  <h3 className=\"text-lg font-semibold mb-3 text-gray-900\">\n                    {faq.question}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {faq.answer}\n                  </p>\n                  <div className=\"mt-3 flex flex-wrap gap-2\">\n                    {faq.keywords.map((keyword) => (\n                      <span\n                        key={keyword}\n                        className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\"\n                      >\n                        {keyword}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              ))}\n          </div>\n        </div>\n\n        {/* Search Section */}\n        <div className=\"mt-12 bg-blue-50 rounded-lg p-8 text-center\">\n          <h2 className=\"text-2xl font-bold mb-4 text-gray-900\">\n            {isZh ? '找不到您要的答案？' : \"Can't find what you're looking for?\"}\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            {isZh\n              ? '使用我们的搜索功能查找更多相关信息'\n              : 'Use our search function to find more relevant information'\n            }\n          </p>\n          <Link\n            href={`/${locale}/search`}\n            className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block\"\n          >\n            {isZh ? '搜索FAQ' : 'Search FAQ'}\n          </Link>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,MAAM,QAAQ,OAAO,0BAA0B;IAC/C,MAAM,cAAc,OAChB,4CACA;IAEJ,OAAO;QACL;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;QACR;IACF;AACF;AAEe,eAAe,QAAQ,EACpC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,OAAO,WAAW;;;;;;0CAErB,8OAAC;gCAAE,WAAU;0CACV,OACG,0CACA;;;;;;;;;;;;;;;;;;;;;;0BAQZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,yHAAA,CAAA,gBAAa,CAAC,OAAsB,CAAC,GAAG,CAAC,CAAC,yBACzC,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,SAAS,EAAE,EAAE;gCACrC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAiB,SAAS,IAAI;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;kDACX,SAAS,IAAI;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,SAAS,WAAW;;;;;;;+BATlB,SAAS,EAAE;;;;;;;;;;kCAgBtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,OAAO,SAAS;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;0CACZ,yHAAA,CAAA,aAAU,CACR,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,QAC7B,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,oBACJ,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;uCAVH,IAAI,EAAE;;;;;;;;;;;;;;;;kCAuBxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,OAAO,cAAc;;;;;;0CAExB,8OAAC;gCAAE,WAAU;0CACV,OACG,sBACA;;;;;;0CAGN,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC;gCACzB,WAAU;0CAET,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,OAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACNC,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}