module.exports = {

"[project]/.next-internal/server/app/[locale]/faq/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryManager": (()=>CategoryManager),
    "FAQManager": (()=>FAQManager),
    "categoryManager": (()=>categoryManager),
    "faqManager": (()=>faqManager)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gray$2d$matter$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/gray-matter/index.js [app-rsc] (ecmascript)");
;
;
;
class FAQManager {
    static instance;
    faqs = new Map();
    categories = new Map();
    medications = new Map();
    static getInstance() {
        if (!FAQManager.instance) {
            FAQManager.instance = new FAQManager();
        }
        return FAQManager.instance;
    }
    // 加载FAQ数据
    async loadFAQs(locale) {
        if (this.faqs.has(locale)) {
            return this.faqs.get(locale);
        }
        const faqsDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'src/data/faqs', locale);
        const faqs = [];
        if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(faqsDir)) {
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(faqsDir, {
                recursive: true
            });
            for (const file of files){
                if (typeof file === 'string' && file.endsWith('.md')) {
                    const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(faqsDir, file);
                    const fileContent = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf8');
                    const { data, content } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gray$2d$matter$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])(fileContent);
                    const faq = {
                        id: data.id || this.generateId(),
                        slug: data.slug || this.generateSlug(data.title),
                        title: data.title,
                        question: data.question || data.title,
                        answer: content,
                        shortAnswer: data.shortAnswer,
                        category: data.category,
                        subcategory: data.subcategory,
                        tags: data.tags || [],
                        difficulty: data.difficulty || 'basic',
                        priority: data.priority || 0,
                        relatedFaqs: data.relatedFaqs || [],
                        lastUpdated: data.lastUpdated || new Date().toISOString(),
                        author: data.author,
                        medicalReview: data.medicalReview || false,
                        sources: data.sources || [],
                        locale: locale
                    };
                    faqs.push(faq);
                }
            }
        }
        // 按优先级和更新时间排序
        faqs.sort((a, b)=>{
            if (a.priority !== b.priority) {
                return b.priority - a.priority;
            }
            return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
        });
        this.faqs.set(locale, faqs);
        return faqs;
    }
    // 获取单个FAQ
    async getFAQ(slug, locale) {
        const faqs = await this.loadFAQs(locale);
        return faqs.find((faq)=>faq.slug === slug) || null;
    }
    // 按分类获取FAQ
    async getFAQsByCategory(category, locale) {
        const faqs = await this.loadFAQs(locale);
        return faqs.filter((faq)=>faq.category === category);
    }
    // 搜索FAQ
    async searchFAQs(query, locale) {
        const faqs = await this.loadFAQs(locale);
        const normalizedQuery = query.toLowerCase();
        return faqs.filter((faq)=>faq.title.toLowerCase().includes(normalizedQuery) || faq.question.toLowerCase().includes(normalizedQuery) || faq.answer.toLowerCase().includes(normalizedQuery) || faq.tags.some((tag)=>tag.toLowerCase().includes(normalizedQuery)));
    }
    // 获取相关FAQ
    async getRelatedFAQs(faqId, locale, limit = 5) {
        const faqs = await this.loadFAQs(locale);
        const currentFaq = faqs.find((faq)=>faq.id === faqId);
        if (!currentFaq) return [];
        // 基于标签和分类的相关性计算
        const related = faqs.filter((faq)=>faq.id !== faqId).map((faq)=>{
            let score = 0;
            // 同分类加分
            if (faq.category === currentFaq.category) score += 3;
            if (faq.subcategory === currentFaq.subcategory) score += 2;
            // 共同标签加分
            const commonTags = faq.tags.filter((tag)=>currentFaq.tags.includes(tag));
            score += commonTags.length * 2;
            return {
                faq,
                score
            };
        }).filter((item)=>item.score > 0).sort((a, b)=>b.score - a.score).slice(0, limit).map((item)=>item.faq);
        return related;
    }
    // 生成ID
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
    // 生成slug
    generateSlug(title) {
        return title.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g, '').replace(/\s+/g, '-').replace(/^-+|-+$/g, '');
    }
}
class CategoryManager {
    static instance;
    categories = new Map();
    static getInstance() {
        if (!CategoryManager.instance) {
            CategoryManager.instance = new CategoryManager();
        }
        return CategoryManager.instance;
    }
    async loadCategories(locale) {
        if (this.categories.has(locale)) {
            return this.categories.get(locale);
        }
        // 这里可以从文件或数据库加载分类数据
        // 暂时使用硬编码的分类结构
        const categories = this.getDefaultCategories(locale);
        this.categories.set(locale, categories);
        return categories;
    }
    getDefaultCategories(locale) {
        if (locale === 'zh') {
            return [
                {
                    id: 'medications',
                    slug: 'medications',
                    name: '用药指导',
                    description: '关于G6PD缺乏症患者用药的专业指导',
                    icon: 'pill',
                    faqCount: 0,
                    priority: 1,
                    locale: 'zh',
                    children: [
                        {
                            id: 'chinese-medicine',
                            slug: 'chinese-medicine',
                            name: '中药相关',
                            description: '中药使用注意事项和禁忌',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 1,
                            locale: 'zh'
                        },
                        {
                            id: 'oral-solutions',
                            slug: 'oral-solutions',
                            name: '口服液相关',
                            description: '各类口服液的使用指导',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 2,
                            locale: 'zh'
                        },
                        {
                            id: 'western-medicine',
                            slug: 'western-medicine',
                            name: '西药相关',
                            description: '西药使用注意事项',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 3,
                            locale: 'zh'
                        }
                    ]
                },
                {
                    id: 'diet',
                    slug: 'diet',
                    name: '饮食指导',
                    description: 'G6PD缺乏症患者的饮食建议和禁忌',
                    icon: 'utensils',
                    faqCount: 0,
                    priority: 2,
                    locale: 'zh'
                },
                {
                    id: 'symptoms',
                    slug: 'symptoms',
                    name: '症状识别',
                    description: '如何识别和处理G6PD缺乏症相关症状',
                    icon: 'stethoscope',
                    faqCount: 0,
                    priority: 3,
                    locale: 'zh'
                },
                {
                    id: 'treatment',
                    slug: 'treatment',
                    name: '治疗方案',
                    description: 'G6PD缺乏症的治疗和管理方案',
                    icon: 'heart-pulse',
                    faqCount: 0,
                    priority: 4,
                    locale: 'zh'
                }
            ];
        } else {
            return [
                {
                    id: 'medications',
                    slug: 'medications',
                    name: 'Medications',
                    description: 'Professional guidance on medications for G6PD deficiency patients',
                    icon: 'pill',
                    faqCount: 0,
                    priority: 1,
                    locale: 'en',
                    children: [
                        {
                            id: 'chinese-medicine',
                            slug: 'chinese-medicine',
                            name: 'Chinese Medicine',
                            description: 'Precautions and contraindications for Chinese medicine',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 1,
                            locale: 'en'
                        },
                        {
                            id: 'oral-solutions',
                            slug: 'oral-solutions',
                            name: 'Oral Solutions',
                            description: 'Guidance for various oral solutions',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 2,
                            locale: 'en'
                        },
                        {
                            id: 'western-medicine',
                            slug: 'western-medicine',
                            name: 'Western Medicine',
                            description: 'Precautions for western medications',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 3,
                            locale: 'en'
                        }
                    ]
                },
                {
                    id: 'diet',
                    slug: 'diet',
                    name: 'Diet',
                    description: 'Dietary recommendations and restrictions for G6PD deficiency patients',
                    icon: 'utensils',
                    faqCount: 0,
                    priority: 2,
                    locale: 'en'
                },
                {
                    id: 'symptoms',
                    slug: 'symptoms',
                    name: 'Symptoms',
                    description: 'How to recognize and handle G6PD deficiency related symptoms',
                    icon: 'stethoscope',
                    faqCount: 0,
                    priority: 3,
                    locale: 'en'
                },
                {
                    id: 'treatment',
                    slug: 'treatment',
                    name: 'Treatment',
                    description: 'Treatment and management options for G6PD deficiency',
                    icon: 'heart-pulse',
                    faqCount: 0,
                    priority: 4,
                    locale: 'en'
                }
            ];
        }
    }
}
const faqManager = FAQManager.getInstance();
const categoryManager = CategoryManager.getInstance();
}}),
"[project]/src/components/seo.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "StructuredDataScript": (()=>StructuredDataScript),
    "generateAlternateUrls": (()=>generateAlternateUrls),
    "generateCanonicalUrl": (()=>generateCanonicalUrl),
    "generateSEODescription": (()=>generateSEODescription),
    "generateSEOMetadata": (()=>generateSEOMetadata),
    "generateSEOTitle": (()=>generateSEOTitle),
    "generateStructuredData": (()=>generateStructuredData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
function generateSEOMetadata({ data, locale }) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    return {
        title: data.title,
        description: data.description,
        keywords: data.keywords,
        openGraph: {
            title: data.title,
            description: data.description,
            url: data.canonical || `${baseUrl}/${locale}`,
            siteName: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
            images: [
                {
                    url: data.ogImage || `${baseUrl}/og-image.jpg`,
                    width: 1200,
                    height: 630,
                    alt: data.title
                }
            ],
            locale: locale,
            type: 'website'
        },
        twitter: {
            card: 'summary_large_image',
            title: data.title,
            description: data.description,
            images: [
                data.ogImage || `${baseUrl}/og-image.jpg`
            ]
        },
        robots: {
            index: !data.noindex,
            follow: !data.noindex,
            googleBot: {
                index: !data.noindex,
                follow: !data.noindex,
                'max-video-preview': -1,
                'max-image-preview': 'large',
                'max-snippet': -1
            }
        },
        alternates: {
            canonical: data.canonical,
            languages: {
                'zh': `${baseUrl}/zh`,
                'en': `${baseUrl}/en`
            }
        },
        verification: {
            google: process.env.GOOGLE_SITE_VERIFICATION,
            yandex: process.env.YANDEX_VERIFICATION,
            yahoo: process.env.YAHOO_VERIFICATION
        }
    };
}
function generateStructuredData(data, type, locale) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    const baseStructuredData = {
        '@context': 'https://schema.org',
        '@type': type,
        url: `${baseUrl}/${locale}`,
        name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
        description: locale === 'zh' ? '为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导' : 'Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families',
        inLanguage: locale,
        isPartOf: {
            '@type': 'WebSite',
            name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
            url: baseUrl
        }
    };
    switch(type){
        case 'WebSite':
            return {
                ...baseStructuredData,
                '@type': 'WebSite',
                potentialAction: {
                    '@type': 'SearchAction',
                    target: {
                        '@type': 'EntryPoint',
                        urlTemplate: `${baseUrl}/${locale}/search?q={search_term_string}`
                    },
                    'query-input': 'required name=search_term_string'
                }
            };
        case 'FAQPage':
            return {
                ...baseStructuredData,
                '@type': 'FAQPage',
                mainEntity: data.faqs?.map((faq)=>({
                        '@type': 'Question',
                        name: faq.question,
                        acceptedAnswer: {
                            '@type': 'Answer',
                            text: faq.shortAnswer || (typeof faq.answer === 'string' ? faq.answer.substring(0, 200) + '...' : '')
                        }
                    })) || []
            };
        case 'MedicalWebPage':
            return {
                ...baseStructuredData,
                '@type': 'MedicalWebPage',
                medicalAudience: {
                    '@type': 'MedicalAudience',
                    audienceType: 'Patient'
                },
                about: {
                    '@type': 'MedicalCondition',
                    name: 'G6PD Deficiency',
                    alternateName: locale === 'zh' ? '蚕豆病' : 'Glucose-6-phosphate dehydrogenase deficiency',
                    description: locale === 'zh' ? 'G6PD缺乏症是一种遗传性酶缺乏病，患者需要避免某些药物和食物以防止溶血反应' : 'G6PD deficiency is a hereditary enzyme deficiency disease where patients need to avoid certain medications and foods to prevent hemolytic reactions'
                },
                lastReviewed: data.lastUpdated || new Date().toISOString(),
                reviewedBy: {
                    '@type': 'Organization',
                    name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team'
                }
            };
        case 'Article':
            return {
                ...baseStructuredData,
                '@type': 'Article',
                headline: data.title,
                description: data.description,
                datePublished: data.datePublished || new Date().toISOString(),
                dateModified: data.lastUpdated || new Date().toISOString(),
                author: {
                    '@type': 'Organization',
                    name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team'
                },
                publisher: {
                    '@type': 'Organization',
                    name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
                    logo: {
                        '@type': 'ImageObject',
                        url: `${baseUrl}/logo.png`
                    }
                },
                mainEntityOfPage: {
                    '@type': 'WebPage',
                    '@id': data.canonical || `${baseUrl}/${locale}`
                }
            };
        case 'BreadcrumbList':
            return {
                '@context': 'https://schema.org',
                '@type': 'BreadcrumbList',
                itemListElement: data.breadcrumbs?.map((item, index)=>({
                        '@type': 'ListItem',
                        position: index + 1,
                        name: item.title,
                        item: `${baseUrl}${item.href}`
                    })) || []
            };
        default:
            return baseStructuredData;
    }
}
function StructuredDataScript({ data, type, locale }) {
    const structuredData = generateStructuredData(data, type, locale);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(structuredData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo.tsx",
        lineNumber: 182,
        columnNumber: 5
    }, this);
}
function generateCanonicalUrl(pathname) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    return `${baseUrl}${pathname}`;
}
function generateAlternateUrls(pathname) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    const pathWithoutLocale = pathname.replace(/^\/(zh|en)/, '');
    return {
        'zh': `${baseUrl}/zh${pathWithoutLocale}`,
        'en': `${baseUrl}/en${pathWithoutLocale}`
    };
}
function generateSEOTitle(title, siteName, locale) {
    const separator = locale === 'zh' ? ' - ' : ' | ';
    return `${title}${separator}${siteName}`;
}
function generateSEODescription(content, maxLength = 160) {
    // Remove markdown and HTML
    const cleanContent = content.replace(/#{1,6}\s+/g, '').replace(/\*\*(.*?)\*\*/g, '$1').replace(/\*(.*?)\*/g, '$1').replace(/<[^>]*>/g, '').replace(/\n+/g, ' ').trim();
    if (cleanContent.length <= maxLength) return cleanContent;
    return cleanContent.slice(0, maxLength).replace(/\s+\S*$/, '') + '...';
}
}}),
"[project]/src/app/[locale]/faq/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FAQPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-rsc] (ecmascript) <export default as getTranslations>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MagnifyingGlassIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MagnifyingGlassIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js [app-rsc] (ecmascript) <export default as MagnifyingGlassIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$FunnelIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__FunnelIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js [app-rsc] (ecmascript) <export default as FunnelIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$BookOpenIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpenIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js [app-rsc] (ecmascript) <export default as BookOpenIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClockIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClockIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ClockIcon.js [app-rsc] (ecmascript) <export default as ClockIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$TagIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__TagIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/TagIcon.js [app-rsc] (ecmascript) <export default as TagIcon>");
;
;
;
;
;
;
async function generateMetadata({ params }) {
    const { locale } = await params;
    const seoData = {
        title: locale === 'zh' ? '常见问题 - G6PD缺乏症FAQ' : 'FAQ - G6PD Deficiency FAQ',
        description: locale === 'zh' ? '浏览G6PD缺乏症（蚕豆病）的常见问题，包括用药指导、饮食建议、症状识别等专业内容。' : 'Browse frequently asked questions about G6PD deficiency, including medication guidance, dietary advice, and symptom recognition.',
        keywords: locale === 'zh' ? 'G6PD缺乏症,蚕豆病,常见问题,用药指导,饮食建议,症状识别' : 'G6PD deficiency,FAQ,medication guidance,dietary advice,symptom recognition',
        canonical: `/${locale}/faq`
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateSEOMetadata"])({
        data: seoData,
        locale
    });
}
async function FAQPage({ params, searchParams }) {
    const { locale } = await params;
    const searchParamsData = await searchParams;
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale
    });
    const faqs = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faqManager"].loadFAQs(locale);
    // Filter FAQs based on search params
    let filteredFAQs = faqs;
    if (searchParamsData.category) {
        filteredFAQs = filteredFAQs.filter((faq)=>faq.category === searchParamsData.category);
    }
    if (searchParamsData.search) {
        const searchTerm = searchParamsData.search.toLowerCase();
        filteredFAQs = filteredFAQs.filter((faq)=>faq.title.toLowerCase().includes(searchTerm) || faq.question.toLowerCase().includes(searchTerm) || faq.tags.some((tag)=>tag.toLowerCase().includes(searchTerm)));
    }
    // Pagination
    const page = parseInt(searchParamsData.page || '1');
    const itemsPerPage = 12;
    const totalPages = Math.ceil(filteredFAQs.length / itemsPerPage);
    const startIndex = (page - 1) * itemsPerPage;
    const paginatedFAQs = filteredFAQs.slice(startIndex, startIndex + itemsPerPage);
    const categories = [
        {
            id: 'all',
            name: t('faq.categories.all'),
            count: faqs.length
        },
        {
            id: 'medications',
            name: t('faq.categories.medications'),
            count: faqs.filter((f)=>f.category === 'medications').length
        },
        {
            id: 'diet',
            name: t('faq.categories.diet'),
            count: faqs.filter((f)=>f.category === 'diet').length
        },
        {
            id: 'symptoms',
            name: t('faq.categories.symptoms'),
            count: faqs.filter((f)=>f.category === 'symptoms').length
        },
        {
            id: 'treatment',
            name: t('faq.categories.treatment'),
            count: faqs.filter((f)=>f.category === 'treatment').length
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StructuredDataScript"], {
                data: {
                    faqs: paginatedFAQs
                },
                type: "FAQPage",
                locale: locale
            }, void 0, false, {
                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen bg-gray-50",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white shadow-sm",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-3xl font-bold text-gray-900 mb-4",
                                        children: t('faq.title')
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                        lineNumber: 90,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-lg text-gray-600 max-w-2xl mx-auto",
                                        children: t('faq.subtitle')
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                        lineNumber: 93,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                lineNumber: 89,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                            lineNumber: 88,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                        lineNumber: 87,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col lg:flex-row gap-8",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "lg:w-1/4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-white rounded-lg shadow-sm p-6 sticky top-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mb-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                                        children: t('common.search')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                        lineNumber: 107,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "text",
                                                                placeholder: t('common.searchPlaceholder'),
                                                                className: "w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",
                                                                defaultValue: searchParamsData.search || ''
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                lineNumber: 111,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MagnifyingGlassIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MagnifyingGlassIcon$3e$__["MagnifyingGlassIcon"], {
                                                                className: "absolute left-3 top-2.5 h-5 w-5 text-gray-400"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                lineNumber: 117,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                        lineNumber: 110,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                lineNumber: 106,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mb-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-medium text-gray-700 mb-3",
                                                        children: t('faq.filters.category')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                        lineNumber: 123,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-2",
                                                        children: categories.map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                                href: `/${locale}/faq${category.id !== 'all' ? `?category=${category.id}` : ''}`,
                                                                className: `flex items-center justify-between p-2 rounded-md text-sm transition-colors ${searchParamsData.category === category.id || category.id === 'all' && !searchParamsData.category ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'}`,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        children: category.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                        lineNumber: 137,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",
                                                                        children: category.count
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                        lineNumber: 138,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, category.id, true, {
                                                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                lineNumber: 128,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                        lineNumber: 126,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                lineNumber: 122,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-medium text-gray-700 mb-3",
                                                        children: t('faq.filters.difficulty')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                        lineNumber: 148,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-2",
                                                        children: [
                                                            'basic',
                                                            'intermediate',
                                                            'advanced'
                                                        ].map((level)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "flex items-center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                        type: "checkbox",
                                                                        className: "rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                        lineNumber: 154,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "ml-2 text-sm text-gray-600",
                                                                        children: t(`faq.difficulty.${level}`)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                        lineNumber: 158,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, level, true, {
                                                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                lineNumber: 153,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                        lineNumber: 151,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                lineNumber: 147,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                        lineNumber: 104,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                    lineNumber: 103,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "lg:w-3/4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between mb-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-4",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm text-gray-600",
                                                        children: locale === 'zh' ? `显示 ${startIndex + 1}-${Math.min(startIndex + itemsPerPage, filteredFAQs.length)} 条，共 ${filteredFAQs.length} 条结果` : `Showing ${startIndex + 1}-${Math.min(startIndex + itemsPerPage, filteredFAQs.length)} of ${filteredFAQs.length} results`
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                        lineNumber: 173,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                    lineNumber: 172,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$FunnelIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__FunnelIcon$3e$__["FunnelIcon"], {
                                                            className: "h-5 w-5 text-gray-400"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                            lineNumber: 181,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                            className: "text-sm border border-gray-300 rounded-md px-3 py-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    children: locale === 'zh' ? '最新更新' : 'Latest Updated'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                    lineNumber: 183,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    children: locale === 'zh' ? '最受欢迎' : 'Most Popular'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                    lineNumber: 184,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    children: locale === 'zh' ? '按难度' : 'By Difficulty'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                    lineNumber: 185,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                            lineNumber: 182,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                    lineNumber: 180,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                            lineNumber: 171,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",
                                            children: paginatedFAQs.map((faq)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-start justify-between mb-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: `px-2 py-1 text-xs font-medium rounded ${faq.difficulty === 'basic' ? 'bg-green-100 text-green-800' : faq.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,
                                                                            children: t(`faq.difficulty.${faq.difficulty}`)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                            lineNumber: 196,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        faq.medicalReview && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded",
                                                                            children: locale === 'zh' ? '医学审核' : 'Medical Review'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                            lineNumber: 204,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                    lineNumber: 195,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClockIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClockIcon$3e$__["ClockIcon"], {
                                                                    className: "h-4 w-4 text-gray-400"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                    lineNumber: 209,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                            lineNumber: 194,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-lg font-semibold text-gray-900 mb-2",
                                                            children: faq.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                            lineNumber: 212,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-gray-600 text-sm mb-4",
                                                            children: faq.shortAnswer || faq.answer.substring(0, 150) + '...'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                            lineNumber: 216,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center justify-between",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex flex-wrap gap-1",
                                                                    children: faq.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$TagIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__TagIcon$3e$__["TagIcon"], {
                                                                                    className: "h-3 w-3 mr-1"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                                    lineNumber: 224,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                tag
                                                                            ]
                                                                        }, tag, true, {
                                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                            lineNumber: 223,
                                                                            columnNumber: 27
                                                                        }, this))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                    lineNumber: 221,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: `/${locale}/faq/${faq.slug}`,
                                                                    className: "inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium",
                                                                    children: [
                                                                        t('common.readMore'),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$BookOpenIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpenIcon$3e$__["BookOpenIcon"], {
                                                                            className: "ml-1 h-4 w-4"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                            lineNumber: 234,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                                    lineNumber: 229,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                            lineNumber: 220,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, faq.id, true, {
                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                    lineNumber: 193,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                            lineNumber: 191,
                                            columnNumber: 15
                                        }, this),
                                        totalPages > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-center space-x-2",
                                            children: Array.from({
                                                length: totalPages
                                            }, (_, i)=>i + 1).map((pageNum)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: `/${locale}/faq?page=${pageNum}${searchParamsData.category ? `&category=${searchParamsData.category}` : ''}${searchParamsData.search ? `&search=${searchParamsData.search}` : ''}`,
                                                    className: `px-3 py-2 text-sm rounded-md ${pageNum === page ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'}`,
                                                    children: pageNum
                                                }, pageNum, false, {
                                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                                    lineNumber: 245,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                            lineNumber: 243,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/[locale]/faq/page.tsx",
                                    lineNumber: 169,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/[locale]/faq/page.tsx",
                            lineNumber: 101,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/faq/page.tsx",
                        lineNumber: 100,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/[locale]/faq/page.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
}}),
"[project]/src/app/[locale]/faq/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/faq/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ddc0056e._.js.map