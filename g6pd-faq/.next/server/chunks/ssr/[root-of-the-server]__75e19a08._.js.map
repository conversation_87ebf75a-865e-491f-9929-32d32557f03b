{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/data.ts"], "sourcesContent": ["import { FAQ, Category, Medication } from './types'\nimport fs from 'fs'\nimport path from 'path'\nimport matter from 'gray-matter'\n\n// FAQ数据管理\nexport class FAQManager {\n  private static instance: FAQManager\n  private faqs: Map<string, FAQ[]> = new Map()\n  private categories: Map<string, Category[]> = new Map()\n  private medications: Map<string, Medication[]> = new Map()\n\n  static getInstance(): FAQManager {\n    if (!FAQManager.instance) {\n      FAQManager.instance = new FAQManager()\n    }\n    return FAQManager.instance\n  }\n\n  // 加载FAQ数据\n  async loadFAQs(locale: string): Promise<FAQ[]> {\n    if (this.faqs.has(locale)) {\n      return this.faqs.get(locale)!\n    }\n\n    const faqsDir = path.join(process.cwd(), 'src/data/faqs', locale)\n    const faqs: FAQ[] = []\n\n    if (fs.existsSync(faqsDir)) {\n      const files = fs.readdirSync(faqsDir, { recursive: true })\n      \n      for (const file of files) {\n        if (typeof file === 'string' && file.endsWith('.md')) {\n          const filePath = path.join(faqsDir, file)\n          const fileContent = fs.readFileSync(filePath, 'utf8')\n          const { data, content } = matter(fileContent)\n          \n          const faq: FAQ = {\n            id: data.id || this.generateId(),\n            slug: data.slug || this.generateSlug(data.title),\n            title: data.title,\n            question: data.question || data.title,\n            answer: content,\n            shortAnswer: data.shortAnswer,\n            category: data.category,\n            subcategory: data.subcategory,\n            tags: data.tags || [],\n            difficulty: data.difficulty || 'basic',\n            priority: data.priority || 0,\n            relatedFaqs: data.relatedFaqs || [],\n            lastUpdated: data.lastUpdated || new Date().toISOString(),\n            author: data.author,\n            medicalReview: data.medicalReview || false,\n            sources: data.sources || [],\n            locale: locale as 'zh' | 'en'\n          }\n          \n          faqs.push(faq)\n        }\n      }\n    }\n\n    // 按优先级和更新时间排序\n    faqs.sort((a, b) => {\n      if (a.priority !== b.priority) {\n        return b.priority - a.priority\n      }\n      return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()\n    })\n\n    this.faqs.set(locale, faqs)\n    return faqs\n  }\n\n  // 获取单个FAQ\n  async getFAQ(slug: string, locale: string): Promise<FAQ | null> {\n    const faqs = await this.loadFAQs(locale)\n    return faqs.find(faq => faq.slug === slug) || null\n  }\n\n  // 按分类获取FAQ\n  async getFAQsByCategory(category: string, locale: string): Promise<FAQ[]> {\n    const faqs = await this.loadFAQs(locale)\n    return faqs.filter(faq => faq.category === category)\n  }\n\n  // 搜索FAQ\n  async searchFAQs(query: string, locale: string): Promise<FAQ[]> {\n    const faqs = await this.loadFAQs(locale)\n    const normalizedQuery = query.toLowerCase()\n    \n    return faqs.filter(faq => \n      faq.title.toLowerCase().includes(normalizedQuery) ||\n      faq.question.toLowerCase().includes(normalizedQuery) ||\n      faq.answer.toLowerCase().includes(normalizedQuery) ||\n      faq.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))\n    )\n  }\n\n  // 获取相关FAQ\n  async getRelatedFAQs(faqId: string, locale: string, limit: number = 5): Promise<FAQ[]> {\n    const faqs = await this.loadFAQs(locale)\n    const currentFaq = faqs.find(faq => faq.id === faqId)\n    \n    if (!currentFaq) return []\n\n    // 基于标签和分类的相关性计算\n    const related = faqs\n      .filter(faq => faq.id !== faqId)\n      .map(faq => {\n        let score = 0\n        \n        // 同分类加分\n        if (faq.category === currentFaq.category) score += 3\n        if (faq.subcategory === currentFaq.subcategory) score += 2\n        \n        // 共同标签加分\n        const commonTags = faq.tags.filter(tag => currentFaq.tags.includes(tag))\n        score += commonTags.length * 2\n        \n        return { faq, score }\n      })\n      .filter(item => item.score > 0)\n      .sort((a, b) => b.score - a.score)\n      .slice(0, limit)\n      .map(item => item.faq)\n\n    return related\n  }\n\n  // 生成ID\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9)\n  }\n\n  // 生成slug\n  private generateSlug(title: string): string {\n    return title\n      .toLowerCase()\n      .replace(/[^\\w\\s\\u4e00-\\u9fff]/g, '')\n      .replace(/\\s+/g, '-')\n      .replace(/^-+|-+$/g, '')\n  }\n}\n\n// 分类数据管理\nexport class CategoryManager {\n  private static instance: CategoryManager\n  private categories: Map<string, Category[]> = new Map()\n\n  static getInstance(): CategoryManager {\n    if (!CategoryManager.instance) {\n      CategoryManager.instance = new CategoryManager()\n    }\n    return CategoryManager.instance\n  }\n\n  async loadCategories(locale: string): Promise<Category[]> {\n    if (this.categories.has(locale)) {\n      return this.categories.get(locale)!\n    }\n\n    // 这里可以从文件或数据库加载分类数据\n    // 暂时使用硬编码的分类结构\n    const categories: Category[] = this.getDefaultCategories(locale)\n    \n    this.categories.set(locale, categories)\n    return categories\n  }\n\n  private getDefaultCategories(locale: string): Category[] {\n    if (locale === 'zh') {\n      return [\n        {\n          id: 'medications',\n          slug: 'medications',\n          name: '用药指导',\n          description: '关于G6PD缺乏症患者用药的专业指导',\n          icon: 'pill',\n          faqCount: 0,\n          priority: 1,\n          locale: 'zh',\n          children: [\n            {\n              id: 'chinese-medicine',\n              slug: 'chinese-medicine',\n              name: '中药相关',\n              description: '中药使用注意事项和禁忌',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 1,\n              locale: 'zh'\n            },\n            {\n              id: 'oral-solutions',\n              slug: 'oral-solutions',\n              name: '口服液相关',\n              description: '各类口服液的使用指导',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 2,\n              locale: 'zh'\n            },\n            {\n              id: 'western-medicine',\n              slug: 'western-medicine',\n              name: '西药相关',\n              description: '西药使用注意事项',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 3,\n              locale: 'zh'\n            }\n          ]\n        },\n        {\n          id: 'diet',\n          slug: 'diet',\n          name: '饮食指导',\n          description: 'G6PD缺乏症患者的饮食建议和禁忌',\n          icon: 'utensils',\n          faqCount: 0,\n          priority: 2,\n          locale: 'zh'\n        },\n        {\n          id: 'symptoms',\n          slug: 'symptoms',\n          name: '症状识别',\n          description: '如何识别和处理G6PD缺乏症相关症状',\n          icon: 'stethoscope',\n          faqCount: 0,\n          priority: 3,\n          locale: 'zh'\n        },\n        {\n          id: 'treatment',\n          slug: 'treatment',\n          name: '治疗方案',\n          description: 'G6PD缺乏症的治疗和管理方案',\n          icon: 'heart-pulse',\n          faqCount: 0,\n          priority: 4,\n          locale: 'zh'\n        }\n      ]\n    } else {\n      return [\n        {\n          id: 'medications',\n          slug: 'medications',\n          name: 'Medications',\n          description: 'Professional guidance on medications for G6PD deficiency patients',\n          icon: 'pill',\n          faqCount: 0,\n          priority: 1,\n          locale: 'en',\n          children: [\n            {\n              id: 'chinese-medicine',\n              slug: 'chinese-medicine',\n              name: 'Chinese Medicine',\n              description: 'Precautions and contraindications for Chinese medicine',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 1,\n              locale: 'en'\n            },\n            {\n              id: 'oral-solutions',\n              slug: 'oral-solutions',\n              name: 'Oral Solutions',\n              description: 'Guidance for various oral solutions',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 2,\n              locale: 'en'\n            },\n            {\n              id: 'western-medicine',\n              slug: 'western-medicine',\n              name: 'Western Medicine',\n              description: 'Precautions for western medications',\n              parentId: 'medications',\n              faqCount: 0,\n              priority: 3,\n              locale: 'en'\n            }\n          ]\n        },\n        {\n          id: 'diet',\n          slug: 'diet',\n          name: 'Diet',\n          description: 'Dietary recommendations and restrictions for G6PD deficiency patients',\n          icon: 'utensils',\n          faqCount: 0,\n          priority: 2,\n          locale: 'en'\n        },\n        {\n          id: 'symptoms',\n          slug: 'symptoms',\n          name: 'Symptoms',\n          description: 'How to recognize and handle G6PD deficiency related symptoms',\n          icon: 'stethoscope',\n          faqCount: 0,\n          priority: 3,\n          locale: 'en'\n        },\n        {\n          id: 'treatment',\n          slug: 'treatment',\n          name: 'Treatment',\n          description: 'Treatment and management options for G6PD deficiency',\n          icon: 'heart-pulse',\n          faqCount: 0,\n          priority: 4,\n          locale: 'en'\n        }\n      ]\n    }\n  }\n}\n\n// 导出单例实例\nexport const faqManager = FAQManager.getInstance()\nexport const categoryManager = CategoryManager.getInstance()\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;AAGO,MAAM;IACX,OAAe,SAAoB;IAC3B,OAA2B,IAAI,MAAK;IACpC,aAAsC,IAAI,MAAK;IAC/C,cAAyC,IAAI,MAAK;IAE1D,OAAO,cAA0B;QAC/B,IAAI,CAAC,WAAW,QAAQ,EAAE;YACxB,WAAW,QAAQ,GAAG,IAAI;QAC5B;QACA,OAAO,WAAW,QAAQ;IAC5B;IAEA,UAAU;IACV,MAAM,SAAS,MAAc,EAAkB;QAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACvB;QAEA,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAiB;QAC1D,MAAM,OAAc,EAAE;QAEtB,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;YAC1B,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC,SAAS;gBAAE,WAAW;YAAK;YAExD,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,OAAO,SAAS,YAAY,KAAK,QAAQ,CAAC,QAAQ;oBACpD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;oBACpC,MAAM,cAAc,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;oBAC9C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;oBAEjC,MAAM,MAAW;wBACf,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,UAAU;wBAC9B,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK;wBAC/C,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ,IAAI,KAAK,KAAK;wBACrC,QAAQ;wBACR,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,aAAa,KAAK,WAAW;wBAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;wBACrB,YAAY,KAAK,UAAU,IAAI;wBAC/B,UAAU,KAAK,QAAQ,IAAI;wBAC3B,aAAa,KAAK,WAAW,IAAI,EAAE;wBACnC,aAAa,KAAK,WAAW,IAAI,IAAI,OAAO,WAAW;wBACvD,QAAQ,KAAK,MAAM;wBACnB,eAAe,KAAK,aAAa,IAAI;wBACrC,SAAS,KAAK,OAAO,IAAI,EAAE;wBAC3B,QAAQ;oBACV;oBAEA,KAAK,IAAI,CAAC;gBACZ;YACF;QACF;QAEA,cAAc;QACd,KAAK,IAAI,CAAC,CAAC,GAAG;YACZ,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC;YACA,OAAO,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;QAC5E;QAEA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ;QACtB,OAAO;IACT;IAEA,UAAU;IACV,MAAM,OAAO,IAAY,EAAE,MAAc,EAAuB;QAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,SAAS;IAChD;IAEA,WAAW;IACX,MAAM,kBAAkB,QAAgB,EAAE,MAAc,EAAkB;QACxE,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;IAC7C;IAEA,QAAQ;IACR,MAAM,WAAW,KAAa,EAAE,MAAc,EAAkB;QAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,MAAM,kBAAkB,MAAM,WAAW;QAEzC,OAAO,KAAK,MAAM,CAAC,CAAA,MACjB,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACjC,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACpC,IAAI,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAClC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;IAEpD;IAEA,UAAU;IACV,MAAM,eAAe,KAAa,EAAE,MAAc,EAAE,QAAgB,CAAC,EAAkB;QACrF,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QACjC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAE/C,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,gBAAgB;QAChB,MAAM,UAAU,KACb,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OACzB,GAAG,CAAC,CAAA;YACH,IAAI,QAAQ;YAEZ,QAAQ;YACR,IAAI,IAAI,QAAQ,KAAK,WAAW,QAAQ,EAAE,SAAS;YACnD,IAAI,IAAI,WAAW,KAAK,WAAW,WAAW,EAAE,SAAS;YAEzD,SAAS;YACT,MAAM,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,WAAW,IAAI,CAAC,QAAQ,CAAC;YACnE,SAAS,WAAW,MAAM,GAAG;YAE7B,OAAO;gBAAE;gBAAK;YAAM;QACtB,GACC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,GAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;QAEvB,OAAO;IACT;IAEA,OAAO;IACC,aAAqB;QAC3B,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAC9C;IAEA,SAAS;IACD,aAAa,KAAa,EAAU;QAC1C,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,yBAAyB,IACjC,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,YAAY;IACzB;AACF;AAGO,MAAM;IACX,OAAe,SAAyB;IAChC,aAAsC,IAAI,MAAK;IAEvD,OAAO,cAA+B;QACpC,IAAI,CAAC,gBAAgB,QAAQ,EAAE;YAC7B,gBAAgB,QAAQ,GAAG,IAAI;QACjC;QACA,OAAO,gBAAgB,QAAQ;IACjC;IAEA,MAAM,eAAe,MAAc,EAAuB;QACxD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS;YAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAC7B;QAEA,oBAAoB;QACpB,eAAe;QACf,MAAM,aAAyB,IAAI,CAAC,oBAAoB,CAAC;QAEzD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC5B,OAAO;IACT;IAEQ,qBAAqB,MAAc,EAAc;QACvD,IAAI,WAAW,MAAM;YACnB,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;oBACR,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;aACD;QACH,OAAO;YACL,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;oBACR,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;aACD;QACH;IACF;AACF;AAGO,MAAM,aAAa,WAAW,WAAW;AACzC,MAAM,kBAAkB,gBAAgB,WAAW", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/seo.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { SEOData } from '@/lib/types'\n\ninterface SEOProps {\n  data: SEOData\n  locale: string\n}\n\nexport function generateSEOMetadata({ data, locale }: SEOProps): Metadata {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  \n  return {\n    title: data.title,\n    description: data.description,\n    keywords: data.keywords,\n    openGraph: {\n      title: data.title,\n      description: data.description,\n      url: data.canonical || `${baseUrl}/${locale}`,\n      siteName: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n      images: [\n        {\n          url: data.ogImage || `${baseUrl}/og-image.jpg`,\n          width: 1200,\n          height: 630,\n          alt: data.title,\n        },\n      ],\n      locale: locale,\n      type: 'website',\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title: data.title,\n      description: data.description,\n      images: [data.ogImage || `${baseUrl}/og-image.jpg`],\n    },\n    robots: {\n      index: !data.noindex,\n      follow: !data.noindex,\n      googleBot: {\n        index: !data.noindex,\n        follow: !data.noindex,\n        'max-video-preview': -1,\n        'max-image-preview': 'large',\n        'max-snippet': -1,\n      },\n    },\n    alternates: {\n      canonical: data.canonical,\n      languages: {\n        'zh': `${baseUrl}/zh`,\n        'en': `${baseUrl}/en`,\n      },\n    },\n    verification: {\n      google: process.env.GOOGLE_SITE_VERIFICATION,\n      yandex: process.env.YANDEX_VERIFICATION,\n      yahoo: process.env.YAHOO_VERIFICATION,\n    },\n  }\n}\n\nexport function generateStructuredData(data: Record<string, unknown>, type: string, locale: string) {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  \n  const baseStructuredData = {\n    '@context': 'https://schema.org',\n    '@type': type,\n    url: `${baseUrl}/${locale}`,\n    name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n    description: locale === 'zh' \n      ? '为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导'\n      : 'Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families',\n    inLanguage: locale,\n    isPartOf: {\n      '@type': 'WebSite',\n      name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n      url: baseUrl,\n    },\n  }\n\n  switch (type) {\n    case 'WebSite':\n      return {\n        ...baseStructuredData,\n        '@type': 'WebSite',\n        potentialAction: {\n          '@type': 'SearchAction',\n          target: {\n            '@type': 'EntryPoint',\n            urlTemplate: `${baseUrl}/${locale}/search?q={search_term_string}`,\n          },\n          'query-input': 'required name=search_term_string',\n        },\n      }\n\n    case 'FAQPage':\n      return {\n        ...baseStructuredData,\n        '@type': 'FAQPage',\n        mainEntity: (data.faqs as Array<Record<string, unknown>>)?.map((faq) => ({\n          '@type': 'Question',\n          name: faq.question,\n          acceptedAnswer: {\n            '@type': 'Answer',\n            text: faq.shortAnswer || (typeof faq.answer === 'string' ? faq.answer.substring(0, 200) + '...' : ''),\n          },\n        })) || [],\n      }\n\n    case 'MedicalWebPage':\n      return {\n        ...baseStructuredData,\n        '@type': 'MedicalWebPage',\n        medicalAudience: {\n          '@type': 'MedicalAudience',\n          audienceType: 'Patient',\n        },\n        about: {\n          '@type': 'MedicalCondition',\n          name: 'G6PD Deficiency',\n          alternateName: locale === 'zh' ? '蚕豆病' : 'Glucose-6-phosphate dehydrogenase deficiency',\n          description: locale === 'zh'\n            ? 'G6PD缺乏症是一种遗传性酶缺乏病，患者需要避免某些药物和食物以防止溶血反应'\n            : 'G6PD deficiency is a hereditary enzyme deficiency disease where patients need to avoid certain medications and foods to prevent hemolytic reactions',\n        },\n        lastReviewed: data.lastUpdated || new Date().toISOString(),\n        reviewedBy: {\n          '@type': 'Organization',\n          name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team',\n        },\n      }\n\n    case 'Article':\n      return {\n        ...baseStructuredData,\n        '@type': 'Article',\n        headline: data.title,\n        description: data.description,\n        datePublished: data.datePublished || new Date().toISOString(),\n        dateModified: data.lastUpdated || new Date().toISOString(),\n        author: {\n          '@type': 'Organization',\n          name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team',\n        },\n        publisher: {\n          '@type': 'Organization',\n          name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',\n          logo: {\n            '@type': 'ImageObject',\n            url: `${baseUrl}/logo.png`,\n          },\n        },\n        mainEntityOfPage: {\n          '@type': 'WebPage',\n          '@id': data.canonical || `${baseUrl}/${locale}`,\n        },\n      }\n\n    case 'BreadcrumbList':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: (data.breadcrumbs as Array<Record<string, unknown>>)?.map((item, index: number) => ({\n          '@type': 'ListItem',\n          position: index + 1,\n          name: item.title,\n          item: `${baseUrl}${item.href}`,\n        })) || [],\n      }\n\n    default:\n      return baseStructuredData\n  }\n}\n\nexport function StructuredDataScript({ data, type, locale }: { data: Record<string, unknown>; type: string; locale: string }) {\n  const structuredData = generateStructuredData(data, type, locale)\n  \n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(structuredData),\n      }}\n    />\n  )\n}\n\n// SEO utility functions\nexport function generateCanonicalUrl(pathname: string): string {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  return `${baseUrl}${pathname}`\n}\n\nexport function generateAlternateUrls(pathname: string): Record<string, string> {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'\n  const pathWithoutLocale = pathname.replace(/^\\/(zh|en)/, '')\n  \n  return {\n    'zh': `${baseUrl}/zh${pathWithoutLocale}`,\n    'en': `${baseUrl}/en${pathWithoutLocale}`,\n  }\n}\n\nexport function generateSEOTitle(title: string, siteName: string, locale: string): string {\n  const separator = locale === 'zh' ? ' - ' : ' | '\n  return `${title}${separator}${siteName}`\n}\n\nexport function generateSEODescription(content: string, maxLength: number = 160): string {\n  // Remove markdown and HTML\n  const cleanContent = content\n    .replace(/#{1,6}\\s+/g, '')\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1')\n    .replace(/\\*(.*?)\\*/g, '$1')\n    .replace(/<[^>]*>/g, '')\n    .replace(/\\n+/g, ' ')\n    .trim()\n  \n  if (cleanContent.length <= maxLength) return cleanContent\n  return cleanContent.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAQO,SAAS,oBAAoB,EAAE,IAAI,EAAE,MAAM,EAAY;IAC5D,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IAEpD,OAAO;QACL,OAAO,KAAK,KAAK;QACjB,aAAa,KAAK,WAAW;QAC7B,UAAU,KAAK,QAAQ;QACvB,WAAW;YACT,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,KAAK,KAAK,SAAS,IAAI,GAAG,QAAQ,CAAC,EAAE,QAAQ;YAC7C,UAAU,WAAW,OAAO,eAAe;YAC3C,QAAQ;gBACN;oBACE,KAAK,KAAK,OAAO,IAAI,GAAG,QAAQ,aAAa,CAAC;oBAC9C,OAAO;oBACP,QAAQ;oBACR,KAAK,KAAK,KAAK;gBACjB;aACD;YACD,QAAQ;YACR,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,QAAQ;gBAAC,KAAK,OAAO,IAAI,GAAG,QAAQ,aAAa,CAAC;aAAC;QACrD;QACA,QAAQ;YACN,OAAO,CAAC,KAAK,OAAO;YACpB,QAAQ,CAAC,KAAK,OAAO;YACrB,WAAW;gBACT,OAAO,CAAC,KAAK,OAAO;gBACpB,QAAQ,CAAC,KAAK,OAAO;gBACrB,qBAAqB,CAAC;gBACtB,qBAAqB;gBACrB,eAAe,CAAC;YAClB;QACF;QACA,YAAY;YACV,WAAW,KAAK,SAAS;YACzB,WAAW;gBACT,MAAM,GAAG,QAAQ,GAAG,CAAC;gBACrB,MAAM,GAAG,QAAQ,GAAG,CAAC;YACvB;QACF;QACA,cAAc;YACZ,QAAQ,QAAQ,GAAG,CAAC,wBAAwB;YAC5C,QAAQ,QAAQ,GAAG,CAAC,mBAAmB;YACvC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;IACF;AACF;AAEO,SAAS,uBAAuB,IAA6B,EAAE,IAAY,EAAE,MAAc;IAChG,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IAEpD,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;QACT,KAAK,GAAG,QAAQ,CAAC,EAAE,QAAQ;QAC3B,MAAM,WAAW,OAAO,eAAe;QACvC,aAAa,WAAW,OACpB,mCACA;QACJ,YAAY;QACZ,UAAU;YACR,SAAS;YACT,MAAM,WAAW,OAAO,eAAe;YACvC,KAAK;QACP;IACF;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,iBAAiB;oBACf,SAAS;oBACT,QAAQ;wBACN,SAAS;wBACT,aAAa,GAAG,QAAQ,CAAC,EAAE,OAAO,8BAA8B,CAAC;oBACnE;oBACA,eAAe;gBACjB;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,YAAY,AAAC,KAAK,IAAI,EAAqC,IAAI,CAAC,MAAQ,CAAC;wBACvE,SAAS;wBACT,MAAM,IAAI,QAAQ;wBAClB,gBAAgB;4BACd,SAAS;4BACT,MAAM,IAAI,WAAW,IAAI,CAAC,OAAO,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ,EAAE;wBACtG;oBACF,CAAC,MAAM,EAAE;YACX;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,iBAAiB;oBACf,SAAS;oBACT,cAAc;gBAChB;gBACA,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,eAAe,WAAW,OAAO,QAAQ;oBACzC,aAAa,WAAW,OACpB,2CACA;gBACN;gBACA,cAAc,KAAK,WAAW,IAAI,IAAI,OAAO,WAAW;gBACxD,YAAY;oBACV,SAAS;oBACT,MAAM,WAAW,OAAO,WAAW;gBACrC;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,kBAAkB;gBACrB,SAAS;gBACT,UAAU,KAAK,KAAK;gBACpB,aAAa,KAAK,WAAW;gBAC7B,eAAe,KAAK,aAAa,IAAI,IAAI,OAAO,WAAW;gBAC3D,cAAc,KAAK,WAAW,IAAI,IAAI,OAAO,WAAW;gBACxD,QAAQ;oBACN,SAAS;oBACT,MAAM,WAAW,OAAO,WAAW;gBACrC;gBACA,WAAW;oBACT,SAAS;oBACT,MAAM,WAAW,OAAO,eAAe;oBACvC,MAAM;wBACJ,SAAS;wBACT,KAAK,GAAG,QAAQ,SAAS,CAAC;oBAC5B;gBACF;gBACA,kBAAkB;oBAChB,SAAS;oBACT,OAAO,KAAK,SAAS,IAAI,GAAG,QAAQ,CAAC,EAAE,QAAQ;gBACjD;YACF;QAEF,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,iBAAiB,AAAC,KAAK,WAAW,EAAqC,IAAI,CAAC,MAAM,QAAkB,CAAC;wBACnG,SAAS;wBACT,UAAU,QAAQ;wBAClB,MAAM,KAAK,KAAK;wBAChB,MAAM,GAAG,UAAU,KAAK,IAAI,EAAE;oBAChC,CAAC,MAAM,EAAE;YACX;QAEF;YACE,OAAO;IACX;AACF;AAEO,SAAS,qBAAqB,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAmE;IAC1H,MAAM,iBAAiB,uBAAuB,MAAM,MAAM;IAE1D,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN;AAGO,SAAS,qBAAqB,QAAgB;IACnD,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IACpD,OAAO,GAAG,UAAU,UAAU;AAChC;AAEO,SAAS,sBAAsB,QAAgB;IACpD,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IACpD,MAAM,oBAAoB,SAAS,OAAO,CAAC,cAAc;IAEzD,OAAO;QACL,MAAM,GAAG,QAAQ,GAAG,EAAE,mBAAmB;QACzC,MAAM,GAAG,QAAQ,GAAG,EAAE,mBAAmB;IAC3C;AACF;AAEO,SAAS,iBAAiB,KAAa,EAAE,QAAgB,EAAE,MAAc;IAC9E,MAAM,YAAY,WAAW,OAAO,QAAQ;IAC5C,OAAO,GAAG,QAAQ,YAAY,UAAU;AAC1C;AAEO,SAAS,uBAAuB,OAAe,EAAE,YAAoB,GAAG;IAC7E,2BAA2B;IAC3B,MAAM,eAAe,QAClB,OAAO,CAAC,cAAc,IACtB,OAAO,CAAC,kBAAkB,MAC1B,OAAO,CAAC,cAAc,MACtB,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;IAEP,IAAI,aAAa,MAAM,IAAI,WAAW,OAAO;IAC7C,OAAO,aAAa,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AACnE", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n}\n\nexport function formatDate(date: string | Date, locale: string = 'zh'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  \n  if (locale === 'zh') {\n    return dateObj.toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n  \n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...'\n}\n\nexport function generateExcerpt(content: string, maxLength: number = 160): string {\n  // Remove markdown syntax and HTML tags\n  const cleanContent = content\n    .replace(/#{1,6}\\s+/g, '') // Remove markdown headers\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // Remove bold\n    .replace(/\\*(.*?)\\*/g, '$1') // Remove italic\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\n+/g, ' ') // Replace newlines with spaces\n    .trim()\n  \n  return truncateText(cleanContent, maxLength)\n}\n\nexport function normalizeSearchQuery(query: string): string {\n  return query\n    .toLowerCase()\n    .trim()\n    .replace(/[^\\w\\s\\u4e00-\\u9fff]/g, '') // Keep only letters, numbers, spaces, and Chinese characters\n    .replace(/\\s+/g, ' ')\n}\n\nexport function highlightSearchTerms(text: string, searchTerms: string[]): string {\n  if (!searchTerms.length) return text\n  \n  const regex = new RegExp(`(${searchTerms.join('|')})`, 'gi')\n  return text.replace(regex, '<mark>$1</mark>')\n}\n\nexport function calculateReadingTime(content: string, locale: string = 'zh'): number {\n  // Average reading speeds (words per minute)\n  const wpm = locale === 'zh' ? 300 : 200 // Chinese vs English\n  \n  const wordCount = locale === 'zh' \n    ? content.length // For Chinese, count characters\n    : content.split(/\\s+/).length // For English, count words\n  \n  return Math.ceil(wordCount / wpm)\n}\n\nexport function generateBreadcrumbs(pathname: string, locale: string): Array<{title: string, href: string}> {\n  const segments = pathname.split('/').filter(Boolean)\n  const breadcrumbs = []\n  \n  // Remove locale from segments\n  if (segments[0] === locale) {\n    segments.shift()\n  }\n  \n  // Add home\n  breadcrumbs.push({\n    title: locale === 'zh' ? '首页' : 'Home',\n    href: `/${locale}`\n  })\n  \n  // Add intermediate segments\n  let currentPath = `/${locale}`\n  for (let i = 0; i < segments.length; i++) {\n    currentPath += `/${segments[i]}`\n    \n    // Map segment to title\n    const segmentTitles: Record<string, Record<string, string>> = {\n      zh: {\n        faq: '常见问题',\n        medications: '用药指导',\n        'chinese-medicine': '中药相关',\n        'oral-solutions': '口服液相关',\n        'western-medicine': '西药相关',\n        diet: '饮食指导',\n        symptoms: '症状识别',\n        treatment: '治疗方案',\n        categories: '分类浏览',\n        search: '搜索',\n        about: '关于我们'\n      },\n      en: {\n        faq: 'FAQ',\n        medications: 'Medications',\n        'chinese-medicine': 'Chinese Medicine',\n        'oral-solutions': 'Oral Solutions',\n        'western-medicine': 'Western Medicine',\n        diet: 'Diet',\n        symptoms: 'Symptoms',\n        treatment: 'Treatment',\n        categories: 'Categories',\n        search: 'Search',\n        about: 'About'\n      }\n    }\n    \n    const title = segmentTitles[locale]?.[segments[i]] || segments[i]\n    breadcrumbs.push({\n      title,\n      href: currentPath\n    })\n  }\n  \n  return breadcrumbs\n}\n\nexport function generateSEOTitle(title: string, siteName: string, locale: string): string {\n  const separator = locale === 'zh' ? ' - ' : ' | '\n  return `${title}${separator}${siteName}`\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => inThrottle = false, limit)\n    }\n  }\n}\n\nexport function getRelativeTimeString(date: Date | string, locale: string = 'zh'): string {\n  const now = new Date()\n  const targetDate = typeof date === 'string' ? new Date(date) : date\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n  \n  const intervals = {\n    zh: [\n      { label: '年', seconds: 31536000 },\n      { label: '个月', seconds: 2592000 },\n      { label: '天', seconds: 86400 },\n      { label: '小时', seconds: 3600 },\n      { label: '分钟', seconds: 60 },\n      { label: '秒', seconds: 1 }\n    ],\n    en: [\n      { label: 'year', seconds: 31536000 },\n      { label: 'month', seconds: 2592000 },\n      { label: 'day', seconds: 86400 },\n      { label: 'hour', seconds: 3600 },\n      { label: 'minute', seconds: 60 },\n      { label: 'second', seconds: 1 }\n    ]\n  }\n  \n  for (const interval of intervals[locale as keyof typeof intervals]) {\n    const count = Math.floor(diffInSeconds / interval.seconds)\n    if (count > 0) {\n      if (locale === 'zh') {\n        return `${count}${interval.label}前`\n      } else {\n        return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`\n      }\n    }\n  }\n  \n  return locale === 'zh' ? '刚刚' : 'just now'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,IAAI,kCAAkC;;AAC/D;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAiB,IAAI;IACnE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,IAAI,WAAW,MAAM;QACnB,OAAO,QAAQ,kBAAkB,CAAC,SAAS;YACzC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC3D;AAEO,SAAS,gBAAgB,OAAe,EAAE,YAAoB,GAAG;IACtE,uCAAuC;IACvC,MAAM,eAAe,QAClB,OAAO,CAAC,cAAc,IAAI,0BAA0B;KACpD,OAAO,CAAC,kBAAkB,MAAM,cAAc;KAC9C,OAAO,CAAC,cAAc,MAAM,gBAAgB;KAC5C,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,+BAA+B;KACpD,IAAI;IAEP,OAAO,aAAa,cAAc;AACpC;AAEO,SAAS,qBAAqB,KAAa;IAChD,OAAO,MACJ,WAAW,GACX,IAAI,GACJ,OAAO,CAAC,yBAAyB,IAAI,6DAA6D;KAClG,OAAO,CAAC,QAAQ;AACrB;AAEO,SAAS,qBAAqB,IAAY,EAAE,WAAqB;IACtE,IAAI,CAAC,YAAY,MAAM,EAAE,OAAO;IAEhC,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IACvD,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAEO,SAAS,qBAAqB,OAAe,EAAE,SAAiB,IAAI;IACzE,4CAA4C;IAC5C,MAAM,MAAM,WAAW,OAAO,MAAM,IAAI,qBAAqB;;IAE7D,MAAM,YAAY,WAAW,OACzB,QAAQ,MAAM,CAAC,gCAAgC;OAC/C,QAAQ,KAAK,CAAC,OAAO,MAAM,CAAC,2BAA2B;;IAE3D,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B;AAEO,SAAS,oBAAoB,QAAgB,EAAE,MAAc;IAClE,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAc,EAAE;IAEtB,8BAA8B;IAC9B,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ;QAC1B,SAAS,KAAK;IAChB;IAEA,WAAW;IACX,YAAY,IAAI,CAAC;QACf,OAAO,WAAW,OAAO,OAAO;QAChC,MAAM,CAAC,CAAC,EAAE,QAAQ;IACpB;IAEA,4BAA4B;IAC5B,IAAI,cAAc,CAAC,CAAC,EAAE,QAAQ;IAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,eAAe,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE;QAEhC,uBAAuB;QACvB,MAAM,gBAAwD;YAC5D,IAAI;gBACF,KAAK;gBACL,aAAa;gBACb,oBAAoB;gBACpB,kBAAkB;gBAClB,oBAAoB;gBACpB,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,QAAQ;gBACR,OAAO;YACT;YACA,IAAI;gBACF,KAAK;gBACL,aAAa;gBACb,oBAAoB;gBACpB,kBAAkB;gBAClB,oBAAoB;gBACpB,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,QAAQ;gBACR,OAAO;YACT;QACF;QAEA,MAAM,QAAQ,aAAa,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,QAAQ,CAAC,EAAE;QACjE,YAAY,IAAI,CAAC;YACf;YACA,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,KAAa,EAAE,QAAgB,EAAE,MAAc;IAC9E,MAAM,YAAY,WAAW,OAAO,QAAQ;IAC5C,OAAO,GAAG,QAAQ,YAAY,UAAU;AAC1C;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAEO,SAAS,sBAAsB,IAAmB,EAAE,SAAiB,IAAI;IAC9E,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC/D,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,MAAM,YAAY;QAChB,IAAI;YACF;gBAAE,OAAO;gBAAK,SAAS;YAAS;YAChC;gBAAE,OAAO;gBAAM,SAAS;YAAQ;YAChC;gBAAE,OAAO;gBAAK,SAAS;YAAM;YAC7B;gBAAE,OAAO;gBAAM,SAAS;YAAK;YAC7B;gBAAE,OAAO;gBAAM,SAAS;YAAG;YAC3B;gBAAE,OAAO;gBAAK,SAAS;YAAE;SAC1B;QACD,IAAI;YACF;gBAAE,OAAO;gBAAQ,SAAS;YAAS;YACnC;gBAAE,OAAO;gBAAS,SAAS;YAAQ;YACnC;gBAAE,OAAO;gBAAO,SAAS;YAAM;YAC/B;gBAAE,OAAO;gBAAQ,SAAS;YAAK;YAC/B;gBAAE,OAAO;gBAAU,SAAS;YAAG;YAC/B;gBAAE,OAAO;gBAAU,SAAS;YAAE;SAC/B;IACH;IAEA,KAAK,MAAM,YAAY,SAAS,CAAC,OAAiC,CAAE;QAClE,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB,SAAS,OAAO;QACzD,IAAI,QAAQ,GAAG;YACb,IAAI,WAAW,MAAM;gBACnB,OAAO,GAAG,QAAQ,SAAS,KAAK,CAAC,CAAC,CAAC;YACrC,OAAO;gBACL,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS,KAAK,GAAG,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;YAChE;QACF;IACF;IAEA,OAAO,WAAW,OAAO,OAAO;AAClC", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/faq/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { notFound } from 'next/navigation'\nimport Link from 'next/link'\nimport { faqManager } from '@/lib/data'\nimport { generateSEOMetadata, StructuredDataScript } from '@/components/seo'\nimport { generateBreadcrumbs, formatDate } from '@/lib/utils'\nimport {\n  ClockIcon,\n  TagIcon,\n  ShareIcon,\n  PrinterIcon,\n  ChevronLeftIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline'\n\n// 临时注释掉generateStaticParams以解决路由冲突\n// export async function generateStaticParams() {\n//   const zhFaqs = await faqManager.loadFAQs('zh')\n//   const enFaqs = await faqManager.loadFAQs('en')\n//\n//   const params = []\n//\n//   for (const faq of zhFaqs) {\n//     params.push({ locale: 'zh', slug: faq.slug })\n//   }\n//\n//   for (const faq of enFaqs) {\n//     params.push({ locale: 'en', slug: faq.slug })\n//   }\n//\n//   return params\n// }\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string; slug: string }>\n}): Promise<Metadata> {\n  const { locale, slug } = await params\n  const faq = await faqManager.getFAQ(slug, locale)\n  \n  if (!faq) {\n    return {\n      title: 'FAQ Not Found',\n      description: 'The requested FAQ could not be found.'\n    }\n  }\n\n  const seoData = {\n    title: faq.title,\n    description: faq.shortAnswer || faq.answer.substring(0, 160),\n    keywords: faq.tags.join(', '),\n    canonical: `/${locale}/faq/${slug}`\n  }\n\n  return generateSEOMetadata({ data: seoData, locale })\n}\n\nexport default async function FAQDetailPage({\n  params,\n}: {\n  params: Promise<{ locale: string; slug: string }>;\n}) {\n  const { locale, slug } = await params;\n  const faq = await faqManager.getFAQ(slug, locale);\n  \n  if (!faq) {\n    notFound();\n  }\n\n  const relatedFaqs = await faqManager.getRelatedFAQs(faq.id, locale, 4)\n  const breadcrumbs = generateBreadcrumbs(`/${locale}/faq/${slug}`, locale)\n\n  return (\n    <>\n      <StructuredDataScript \n        data={{ \n          title: faq.title,\n          description: faq.shortAnswer || faq.answer.substring(0, 200),\n          lastUpdated: faq.lastUpdated,\n          breadcrumbs\n        }} \n        type=\"Article\" \n        locale={locale} \n      />\n      \n      <StructuredDataScript \n        data={{ breadcrumbs }} \n        type=\"BreadcrumbList\" \n        locale={locale} \n      />\n\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Breadcrumbs */}\n        <div className=\"bg-white border-b\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n            <nav className=\"flex\" aria-label=\"Breadcrumb\">\n              <ol className=\"flex items-center space-x-2\">\n                {breadcrumbs.map((item, index) => (\n                  <li key={item.href} className=\"flex items-center\">\n                    {index > 0 && (\n                      <svg className=\"flex-shrink-0 h-4 w-4 text-gray-400 mx-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    )}\n                    {index === breadcrumbs.length - 1 ? (\n                      <span className=\"text-sm font-medium text-gray-500 truncate max-w-xs\">\n                        {item.title}\n                      </span>\n                    ) : (\n                      <Link href={item.href} className=\"text-sm font-medium text-blue-600 hover:text-blue-700\">\n                        {item.title}\n                      </Link>\n                    )}\n                  </li>\n                ))}\n              </ol>\n            </nav>\n          </div>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {/* Back button */}\n          <div className=\"mb-6\">\n            <Link\n              href={`/${locale}/faq`}\n              className=\"inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium\"\n            >\n              <ChevronLeftIcon className=\"h-4 w-4 mr-1\" />\n              {locale === 'zh' ? '返回FAQ列表' : 'Back to FAQ'}\n            </Link>\n          </div>\n\n          {/* Main content */}\n          <article className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            {/* Header */}\n            <header className=\"px-6 py-8 border-b border-gray-200\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${\n                    faq.difficulty === 'basic' ? 'bg-green-100 text-green-800' :\n                    faq.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-red-100 text-red-800'\n                  }`}>\n                    {locale === 'zh' ? \n                      (faq.difficulty === 'basic' ? '基础' : faq.difficulty === 'intermediate' ? '中级' : '高级') :\n                      (faq.difficulty === 'basic' ? 'Basic' : faq.difficulty === 'intermediate' ? 'Intermediate' : 'Advanced')\n                    }\n                  </span>\n                  {faq.medicalReview && (\n                    <span className=\"inline-flex items-center px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full\">\n                      <CheckCircleIcon className=\"h-4 w-4 mr-1\" />\n                      {locale === 'zh' ? '医学审核' : 'Medical Review'}\n                    </span>\n                  )}\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <button className=\"p-2 text-gray-400 hover:text-gray-600 rounded-md\">\n                    <ShareIcon className=\"h-5 w-5\" />\n                  </button>\n                  <button className=\"p-2 text-gray-400 hover:text-gray-600 rounded-md\">\n                    <PrinterIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n              \n              <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                {faq.title}\n              </h1>\n              \n              <div className=\"flex items-center text-sm text-gray-500 space-x-4\">\n                <div className=\"flex items-center\">\n                  <ClockIcon className=\"h-4 w-4 mr-1\" />\n                  {locale === 'zh' ? '最后更新：' : 'Last updated: '}\n                  {formatDate(faq.lastUpdated, locale)}\n                </div>\n                {faq.author && (\n                  <div>\n                    {locale === 'zh' ? '作者：' : 'Author: '}{faq.author}\n                  </div>\n                )}\n              </div>\n            </header>\n\n            {/* Content */}\n            <div className=\"px-6 py-8\">\n              {/* Emergency warning for certain topics */}\n              {(faq.category === 'symptoms' || faq.tags.includes('急性溶血') || faq.tags.includes('emergency')) && (\n                <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-md\">\n                  <div className=\"flex\">\n                    <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400 mt-0.5\" />\n                    <div className=\"ml-3\">\n                      <h3 className=\"text-sm font-medium text-red-800\">\n                        {locale === 'zh' ? '紧急提醒' : 'Emergency Notice'}\n                      </h3>\n                      <p className=\"mt-1 text-sm text-red-700\">\n                        {locale === 'zh' \n                          ? '如出现急性溶血症状，请立即就医。本内容仅供参考，不能替代专业医疗建议。'\n                          : 'If you experience acute hemolysis symptoms, seek immediate medical attention. This content is for reference only and cannot replace professional medical advice.'\n                        }\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* FAQ Content */}\n              <div className=\"prose prose-lg max-w-none\">\n                <div dangerouslySetInnerHTML={{ __html: faq.answer }} />\n              </div>\n\n              {/* Tags */}\n              {faq.tags.length > 0 && (\n                <div className=\"mt-8 pt-6 border-t border-gray-200\">\n                  <h3 className=\"text-sm font-medium text-gray-700 mb-3\">\n                    {locale === 'zh' ? '相关标签' : 'Related Tags'}\n                  </h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {faq.tags.map((tag) => (\n                      <span key={tag} className=\"inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full\">\n                        <TagIcon className=\"h-3 w-3 mr-1\" />\n                        {tag}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Sources */}\n              {faq.sources && faq.sources.length > 0 && (\n                <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                  <h3 className=\"text-sm font-medium text-gray-700 mb-3\">\n                    {locale === 'zh' ? '参考来源' : 'Sources'}\n                  </h3>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    {faq.sources.map((source, index) => (\n                      <li key={index}>• {source}</li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          </article>\n\n          {/* Related FAQs */}\n          {relatedFaqs.length > 0 && (\n            <div className=\"mt-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                {locale === 'zh' ? '相关问题' : 'Related Questions'}\n              </h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {relatedFaqs.map((relatedFaq) => (\n                  <Link\n                    key={relatedFaq.id}\n                    href={`/${locale}/faq/${relatedFaq.slug}`}\n                    className=\"block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\"\n                  >\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {relatedFaq.title}\n                    </h3>\n                    <p className=\"text-gray-600 text-sm\">\n                      {relatedFaq.shortAnswer || relatedFaq.answer.substring(0, 100) + '...'}\n                    </p>\n                  </Link>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AA4BO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAC/B,MAAM,MAAM,MAAM,kHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM;IAE1C,IAAI,CAAC,KAAK;QACR,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,MAAM,UAAU;QACd,OAAO,IAAI,KAAK;QAChB,aAAa,IAAI,WAAW,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG;QACxD,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC;QACxB,WAAW,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,MAAM;IACrC;IAEA,OAAO,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;QAAE,MAAM;QAAS;IAAO;AACrD;AAEe,eAAe,cAAc,EAC1C,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAC/B,MAAM,MAAM,MAAM,kHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM;IAE1C,IAAI,CAAC,KAAK;QACR,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,cAAc,MAAM,kHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,QAAQ;IACpE,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,MAAM,EAAE;IAElE,qBACE;;0BACE,8OAAC,yHAAA,CAAA,uBAAoB;gBACnB,MAAM;oBACJ,OAAO,IAAI,KAAK;oBAChB,aAAa,IAAI,WAAW,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG;oBACxD,aAAa,IAAI,WAAW;oBAC5B;gBACF;gBACA,MAAK;gBACL,QAAQ;;;;;;0BAGV,8OAAC,yHAAA,CAAA,uBAAoB;gBACnB,MAAM;oBAAE;gBAAY;gBACpB,MAAK;gBACL,QAAQ;;;;;;0BAGV,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAO,cAAW;0CAC/B,cAAA,8OAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;4CAAmB,WAAU;;gDAC3B,QAAQ,mBACP,8OAAC;oDAAI,WAAU;oDAA2C,MAAK;oDAAe,SAAQ;8DACpF,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDAG5J,UAAU,YAAY,MAAM,GAAG,kBAC9B,8OAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;yEAGb,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,KAAK,IAAI;oDAAE,WAAU;8DAC9B,KAAK,KAAK;;;;;;;2CAZR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;kCAsB5B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;oCACtB,WAAU;;sDAEV,8OAAC,6NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAC1B,WAAW,OAAO,YAAY;;;;;;;;;;;;0CAKnC,8OAAC;gCAAQ,WAAU;;kDAEjB,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,UAAU,KAAK,UAAU,gCAC7B,IAAI,UAAU,KAAK,iBAAiB,kCACpC,2BACA;0EACC,WAAW,OACT,IAAI,UAAU,KAAK,UAAU,OAAO,IAAI,UAAU,KAAK,iBAAiB,OAAO,OAC/E,IAAI,UAAU,KAAK,UAAU,UAAU,IAAI,UAAU,KAAK,iBAAiB,iBAAiB;;;;;;4DAGhG,IAAI,aAAa,kBAChB,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,6NAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;oEAC1B,WAAW,OAAO,SAAS;;;;;;;;;;;;;kEAIlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAChB,cAAA,8OAAC,iNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAEvB,8OAAC;gEAAO,WAAU;0EAChB,cAAA,8OAAC,qNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAK7B,8OAAC;gDAAG,WAAU;0DACX,IAAI,KAAK;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DACpB,WAAW,OAAO,UAAU;4DAC5B,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,WAAW,EAAE;;;;;;;oDAE9B,IAAI,MAAM,kBACT,8OAAC;;4DACE,WAAW,OAAO,QAAQ;4DAAY,IAAI,MAAM;;;;;;;;;;;;;;;;;;;kDAOzD,8OAAC;wCAAI,WAAU;;4CAEZ,CAAC,IAAI,QAAQ,KAAK,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,mBAC1F,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6OAAA,CAAA,0BAAuB;4DAAC,WAAU;;;;;;sEACnC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,WAAW,OAAO,SAAS;;;;;;8EAE9B,8OAAC;oEAAE,WAAU;8EACV,WAAW,OACR,wCACA;;;;;;;;;;;;;;;;;;;;;;;0DASd,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,yBAAyB;wDAAE,QAAQ,IAAI,MAAM;oDAAC;;;;;;;;;;;4CAIpD,IAAI,IAAI,CAAC,MAAM,GAAG,mBACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,WAAW,OAAO,SAAS;;;;;;kEAE9B,8OAAC;wDAAI,WAAU;kEACZ,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,oBACb,8OAAC;gEAAe,WAAU;;kFACxB,8OAAC,6MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAClB;;+DAFQ;;;;;;;;;;;;;;;;4CAUlB,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,mBACnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,WAAW,OAAO,SAAS;;;;;;kEAE9B,8OAAC;wDAAG,WAAU;kEACX,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;;oEAAe;oEAAG;;+DAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASpB,YAAY,MAAM,GAAG,mBACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,SAAS;;;;;;kDAE9B,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,WAAW,IAAI,EAAE;gDACzC,WAAU;;kEAEV,8OAAC;wDAAG,WAAU;kEACX,WAAW,KAAK;;;;;;kEAEnB,8OAAC;wDAAE,WAAU;kEACV,WAAW,WAAW,IAAI,WAAW,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO;;;;;;;+CAR9D,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBtC", "debugId": null}}]}