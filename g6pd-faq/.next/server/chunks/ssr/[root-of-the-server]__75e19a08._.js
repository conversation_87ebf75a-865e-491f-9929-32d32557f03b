module.exports = {

"[project]/.next-internal/server/app/[locale]/faq/[slug]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryManager": (()=>CategoryManager),
    "FAQManager": (()=>FAQManager),
    "categoryManager": (()=>categoryManager),
    "faqManager": (()=>faqManager)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gray$2d$matter$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/gray-matter/index.js [app-rsc] (ecmascript)");
;
;
;
class FAQManager {
    static instance;
    faqs = new Map();
    categories = new Map();
    medications = new Map();
    static getInstance() {
        if (!FAQManager.instance) {
            FAQManager.instance = new FAQManager();
        }
        return FAQManager.instance;
    }
    // 加载FAQ数据
    async loadFAQs(locale) {
        if (this.faqs.has(locale)) {
            return this.faqs.get(locale);
        }
        const faqsDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'src/data/faqs', locale);
        const faqs = [];
        if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(faqsDir)) {
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(faqsDir, {
                recursive: true
            });
            for (const file of files){
                if (typeof file === 'string' && file.endsWith('.md')) {
                    const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(faqsDir, file);
                    const fileContent = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf8');
                    const { data, content } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gray$2d$matter$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])(fileContent);
                    const faq = {
                        id: data.id || this.generateId(),
                        slug: data.slug || this.generateSlug(data.title),
                        title: data.title,
                        question: data.question || data.title,
                        answer: content,
                        shortAnswer: data.shortAnswer,
                        category: data.category,
                        subcategory: data.subcategory,
                        tags: data.tags || [],
                        difficulty: data.difficulty || 'basic',
                        priority: data.priority || 0,
                        relatedFaqs: data.relatedFaqs || [],
                        lastUpdated: data.lastUpdated || new Date().toISOString(),
                        author: data.author,
                        medicalReview: data.medicalReview || false,
                        sources: data.sources || [],
                        locale: locale
                    };
                    faqs.push(faq);
                }
            }
        }
        // 按优先级和更新时间排序
        faqs.sort((a, b)=>{
            if (a.priority !== b.priority) {
                return b.priority - a.priority;
            }
            return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
        });
        this.faqs.set(locale, faqs);
        return faqs;
    }
    // 获取单个FAQ
    async getFAQ(slug, locale) {
        const faqs = await this.loadFAQs(locale);
        return faqs.find((faq)=>faq.slug === slug) || null;
    }
    // 按分类获取FAQ
    async getFAQsByCategory(category, locale) {
        const faqs = await this.loadFAQs(locale);
        return faqs.filter((faq)=>faq.category === category);
    }
    // 搜索FAQ
    async searchFAQs(query, locale) {
        const faqs = await this.loadFAQs(locale);
        const normalizedQuery = query.toLowerCase();
        return faqs.filter((faq)=>faq.title.toLowerCase().includes(normalizedQuery) || faq.question.toLowerCase().includes(normalizedQuery) || faq.answer.toLowerCase().includes(normalizedQuery) || faq.tags.some((tag)=>tag.toLowerCase().includes(normalizedQuery)));
    }
    // 获取相关FAQ
    async getRelatedFAQs(faqId, locale, limit = 5) {
        const faqs = await this.loadFAQs(locale);
        const currentFaq = faqs.find((faq)=>faq.id === faqId);
        if (!currentFaq) return [];
        // 基于标签和分类的相关性计算
        const related = faqs.filter((faq)=>faq.id !== faqId).map((faq)=>{
            let score = 0;
            // 同分类加分
            if (faq.category === currentFaq.category) score += 3;
            if (faq.subcategory === currentFaq.subcategory) score += 2;
            // 共同标签加分
            const commonTags = faq.tags.filter((tag)=>currentFaq.tags.includes(tag));
            score += commonTags.length * 2;
            return {
                faq,
                score
            };
        }).filter((item)=>item.score > 0).sort((a, b)=>b.score - a.score).slice(0, limit).map((item)=>item.faq);
        return related;
    }
    // 生成ID
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
    // 生成slug
    generateSlug(title) {
        return title.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g, '').replace(/\s+/g, '-').replace(/^-+|-+$/g, '');
    }
}
class CategoryManager {
    static instance;
    categories = new Map();
    static getInstance() {
        if (!CategoryManager.instance) {
            CategoryManager.instance = new CategoryManager();
        }
        return CategoryManager.instance;
    }
    async loadCategories(locale) {
        if (this.categories.has(locale)) {
            return this.categories.get(locale);
        }
        // 这里可以从文件或数据库加载分类数据
        // 暂时使用硬编码的分类结构
        const categories = this.getDefaultCategories(locale);
        this.categories.set(locale, categories);
        return categories;
    }
    getDefaultCategories(locale) {
        if (locale === 'zh') {
            return [
                {
                    id: 'medications',
                    slug: 'medications',
                    name: '用药指导',
                    description: '关于G6PD缺乏症患者用药的专业指导',
                    icon: 'pill',
                    faqCount: 0,
                    priority: 1,
                    locale: 'zh',
                    children: [
                        {
                            id: 'chinese-medicine',
                            slug: 'chinese-medicine',
                            name: '中药相关',
                            description: '中药使用注意事项和禁忌',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 1,
                            locale: 'zh'
                        },
                        {
                            id: 'oral-solutions',
                            slug: 'oral-solutions',
                            name: '口服液相关',
                            description: '各类口服液的使用指导',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 2,
                            locale: 'zh'
                        },
                        {
                            id: 'western-medicine',
                            slug: 'western-medicine',
                            name: '西药相关',
                            description: '西药使用注意事项',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 3,
                            locale: 'zh'
                        }
                    ]
                },
                {
                    id: 'diet',
                    slug: 'diet',
                    name: '饮食指导',
                    description: 'G6PD缺乏症患者的饮食建议和禁忌',
                    icon: 'utensils',
                    faqCount: 0,
                    priority: 2,
                    locale: 'zh'
                },
                {
                    id: 'symptoms',
                    slug: 'symptoms',
                    name: '症状识别',
                    description: '如何识别和处理G6PD缺乏症相关症状',
                    icon: 'stethoscope',
                    faqCount: 0,
                    priority: 3,
                    locale: 'zh'
                },
                {
                    id: 'treatment',
                    slug: 'treatment',
                    name: '治疗方案',
                    description: 'G6PD缺乏症的治疗和管理方案',
                    icon: 'heart-pulse',
                    faqCount: 0,
                    priority: 4,
                    locale: 'zh'
                }
            ];
        } else {
            return [
                {
                    id: 'medications',
                    slug: 'medications',
                    name: 'Medications',
                    description: 'Professional guidance on medications for G6PD deficiency patients',
                    icon: 'pill',
                    faqCount: 0,
                    priority: 1,
                    locale: 'en',
                    children: [
                        {
                            id: 'chinese-medicine',
                            slug: 'chinese-medicine',
                            name: 'Chinese Medicine',
                            description: 'Precautions and contraindications for Chinese medicine',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 1,
                            locale: 'en'
                        },
                        {
                            id: 'oral-solutions',
                            slug: 'oral-solutions',
                            name: 'Oral Solutions',
                            description: 'Guidance for various oral solutions',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 2,
                            locale: 'en'
                        },
                        {
                            id: 'western-medicine',
                            slug: 'western-medicine',
                            name: 'Western Medicine',
                            description: 'Precautions for western medications',
                            parentId: 'medications',
                            faqCount: 0,
                            priority: 3,
                            locale: 'en'
                        }
                    ]
                },
                {
                    id: 'diet',
                    slug: 'diet',
                    name: 'Diet',
                    description: 'Dietary recommendations and restrictions for G6PD deficiency patients',
                    icon: 'utensils',
                    faqCount: 0,
                    priority: 2,
                    locale: 'en'
                },
                {
                    id: 'symptoms',
                    slug: 'symptoms',
                    name: 'Symptoms',
                    description: 'How to recognize and handle G6PD deficiency related symptoms',
                    icon: 'stethoscope',
                    faqCount: 0,
                    priority: 3,
                    locale: 'en'
                },
                {
                    id: 'treatment',
                    slug: 'treatment',
                    name: 'Treatment',
                    description: 'Treatment and management options for G6PD deficiency',
                    icon: 'heart-pulse',
                    faqCount: 0,
                    priority: 4,
                    locale: 'en'
                }
            ];
        }
    }
}
const faqManager = FAQManager.getInstance();
const categoryManager = CategoryManager.getInstance();
}}),
"[project]/src/components/seo.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "StructuredDataScript": (()=>StructuredDataScript),
    "generateAlternateUrls": (()=>generateAlternateUrls),
    "generateCanonicalUrl": (()=>generateCanonicalUrl),
    "generateSEODescription": (()=>generateSEODescription),
    "generateSEOMetadata": (()=>generateSEOMetadata),
    "generateSEOTitle": (()=>generateSEOTitle),
    "generateStructuredData": (()=>generateStructuredData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
function generateSEOMetadata({ data, locale }) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    return {
        title: data.title,
        description: data.description,
        keywords: data.keywords,
        openGraph: {
            title: data.title,
            description: data.description,
            url: data.canonical || `${baseUrl}/${locale}`,
            siteName: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
            images: [
                {
                    url: data.ogImage || `${baseUrl}/og-image.jpg`,
                    width: 1200,
                    height: 630,
                    alt: data.title
                }
            ],
            locale: locale,
            type: 'website'
        },
        twitter: {
            card: 'summary_large_image',
            title: data.title,
            description: data.description,
            images: [
                data.ogImage || `${baseUrl}/og-image.jpg`
            ]
        },
        robots: {
            index: !data.noindex,
            follow: !data.noindex,
            googleBot: {
                index: !data.noindex,
                follow: !data.noindex,
                'max-video-preview': -1,
                'max-image-preview': 'large',
                'max-snippet': -1
            }
        },
        alternates: {
            canonical: data.canonical,
            languages: {
                'zh': `${baseUrl}/zh`,
                'en': `${baseUrl}/en`
            }
        },
        verification: {
            google: process.env.GOOGLE_SITE_VERIFICATION,
            yandex: process.env.YANDEX_VERIFICATION,
            yahoo: process.env.YAHOO_VERIFICATION
        }
    };
}
function generateStructuredData(data, type, locale) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    const baseStructuredData = {
        '@context': 'https://schema.org',
        '@type': type,
        url: `${baseUrl}/${locale}`,
        name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
        description: locale === 'zh' ? '为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导' : 'Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families',
        inLanguage: locale,
        isPartOf: {
            '@type': 'WebSite',
            name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
            url: baseUrl
        }
    };
    switch(type){
        case 'WebSite':
            return {
                ...baseStructuredData,
                '@type': 'WebSite',
                potentialAction: {
                    '@type': 'SearchAction',
                    target: {
                        '@type': 'EntryPoint',
                        urlTemplate: `${baseUrl}/${locale}/search?q={search_term_string}`
                    },
                    'query-input': 'required name=search_term_string'
                }
            };
        case 'FAQPage':
            return {
                ...baseStructuredData,
                '@type': 'FAQPage',
                mainEntity: data.faqs?.map((faq)=>({
                        '@type': 'Question',
                        name: faq.question,
                        acceptedAnswer: {
                            '@type': 'Answer',
                            text: faq.shortAnswer || (typeof faq.answer === 'string' ? faq.answer.substring(0, 200) + '...' : '')
                        }
                    })) || []
            };
        case 'MedicalWebPage':
            return {
                ...baseStructuredData,
                '@type': 'MedicalWebPage',
                medicalAudience: {
                    '@type': 'MedicalAudience',
                    audienceType: 'Patient'
                },
                about: {
                    '@type': 'MedicalCondition',
                    name: 'G6PD Deficiency',
                    alternateName: locale === 'zh' ? '蚕豆病' : 'Glucose-6-phosphate dehydrogenase deficiency',
                    description: locale === 'zh' ? 'G6PD缺乏症是一种遗传性酶缺乏病，患者需要避免某些药物和食物以防止溶血反应' : 'G6PD deficiency is a hereditary enzyme deficiency disease where patients need to avoid certain medications and foods to prevent hemolytic reactions'
                },
                lastReviewed: data.lastUpdated || new Date().toISOString(),
                reviewedBy: {
                    '@type': 'Organization',
                    name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team'
                }
            };
        case 'Article':
            return {
                ...baseStructuredData,
                '@type': 'Article',
                headline: data.title,
                description: data.description,
                datePublished: data.datePublished || new Date().toISOString(),
                dateModified: data.lastUpdated || new Date().toISOString(),
                author: {
                    '@type': 'Organization',
                    name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team'
                },
                publisher: {
                    '@type': 'Organization',
                    name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
                    logo: {
                        '@type': 'ImageObject',
                        url: `${baseUrl}/logo.png`
                    }
                },
                mainEntityOfPage: {
                    '@type': 'WebPage',
                    '@id': data.canonical || `${baseUrl}/${locale}`
                }
            };
        case 'BreadcrumbList':
            return {
                '@context': 'https://schema.org',
                '@type': 'BreadcrumbList',
                itemListElement: data.breadcrumbs?.map((item, index)=>({
                        '@type': 'ListItem',
                        position: index + 1,
                        name: item.title,
                        item: `${baseUrl}${item.href}`
                    })) || []
            };
        default:
            return baseStructuredData;
    }
}
function StructuredDataScript({ data, type, locale }) {
    const structuredData = generateStructuredData(data, type, locale);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(structuredData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo.tsx",
        lineNumber: 182,
        columnNumber: 5
    }, this);
}
function generateCanonicalUrl(pathname) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    return `${baseUrl}${pathname}`;
}
function generateAlternateUrls(pathname) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app';
    const pathWithoutLocale = pathname.replace(/^\/(zh|en)/, '');
    return {
        'zh': `${baseUrl}/zh${pathWithoutLocale}`,
        'en': `${baseUrl}/en${pathWithoutLocale}`
    };
}
function generateSEOTitle(title, siteName, locale) {
    const separator = locale === 'zh' ? ' - ' : ' | ';
    return `${title}${separator}${siteName}`;
}
function generateSEODescription(content, maxLength = 160) {
    // Remove markdown and HTML
    const cleanContent = content.replace(/#{1,6}\s+/g, '').replace(/\*\*(.*?)\*\*/g, '$1').replace(/\*(.*?)\*/g, '$1').replace(/<[^>]*>/g, '').replace(/\n+/g, ' ').trim();
    if (cleanContent.length <= maxLength) return cleanContent;
    return cleanContent.slice(0, maxLength).replace(/\s+\S*$/, '') + '...';
}
}}),
"[project]/src/lib/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateReadingTime": (()=>calculateReadingTime),
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatDate": (()=>formatDate),
    "generateBreadcrumbs": (()=>generateBreadcrumbs),
    "generateExcerpt": (()=>generateExcerpt),
    "generateSEOTitle": (()=>generateSEOTitle),
    "getRelativeTimeString": (()=>getRelativeTimeString),
    "highlightSearchTerms": (()=>highlightSearchTerms),
    "isValidEmail": (()=>isValidEmail),
    "normalizeSearchQuery": (()=>normalizeSearchQuery),
    "slugify": (()=>slugify),
    "throttle": (()=>throttle),
    "truncateText": (()=>truncateText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-rsc] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function slugify(text) {
    return text.toLowerCase().replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    ;
}
function formatDate(date, locale = 'zh') {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (locale === 'zh') {
        return dateObj.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength).replace(/\s+\S*$/, '') + '...';
}
function generateExcerpt(content, maxLength = 160) {
    // Remove markdown syntax and HTML tags
    const cleanContent = content.replace(/#{1,6}\s+/g, '') // Remove markdown headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim();
    return truncateText(cleanContent, maxLength);
}
function normalizeSearchQuery(query) {
    return query.toLowerCase().trim().replace(/[^\w\s\u4e00-\u9fff]/g, '') // Keep only letters, numbers, spaces, and Chinese characters
    .replace(/\s+/g, ' ');
}
function highlightSearchTerms(text, searchTerms) {
    if (!searchTerms.length) return text;
    const regex = new RegExp(`(${searchTerms.join('|')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}
function calculateReadingTime(content, locale = 'zh') {
    // Average reading speeds (words per minute)
    const wpm = locale === 'zh' ? 300 : 200 // Chinese vs English
    ;
    const wordCount = locale === 'zh' ? content.length // For Chinese, count characters
     : content.split(/\s+/).length // For English, count words
    ;
    return Math.ceil(wordCount / wpm);
}
function generateBreadcrumbs(pathname, locale) {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    // Remove locale from segments
    if (segments[0] === locale) {
        segments.shift();
    }
    // Add home
    breadcrumbs.push({
        title: locale === 'zh' ? '首页' : 'Home',
        href: `/${locale}`
    });
    // Add intermediate segments
    let currentPath = `/${locale}`;
    for(let i = 0; i < segments.length; i++){
        currentPath += `/${segments[i]}`;
        // Map segment to title
        const segmentTitles = {
            zh: {
                faq: '常见问题',
                medications: '用药指导',
                'chinese-medicine': '中药相关',
                'oral-solutions': '口服液相关',
                'western-medicine': '西药相关',
                diet: '饮食指导',
                symptoms: '症状识别',
                treatment: '治疗方案',
                categories: '分类浏览',
                search: '搜索',
                about: '关于我们'
            },
            en: {
                faq: 'FAQ',
                medications: 'Medications',
                'chinese-medicine': 'Chinese Medicine',
                'oral-solutions': 'Oral Solutions',
                'western-medicine': 'Western Medicine',
                diet: 'Diet',
                symptoms: 'Symptoms',
                treatment: 'Treatment',
                categories: 'Categories',
                search: 'Search',
                about: 'About'
            }
        };
        const title = segmentTitles[locale]?.[segments[i]] || segments[i];
        breadcrumbs.push({
            title,
            href: currentPath
        });
    }
    return breadcrumbs;
}
function generateSEOTitle(title, siteName, locale) {
    const separator = locale === 'zh' ? ' - ' : ' | ';
    return `${title}${separator}${siteName}`;
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function debounce(func, wait) {
    let timeout = null;
    return (...args)=>{
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function getRelativeTimeString(date, locale = 'zh') {
    const now = new Date();
    const targetDate = typeof date === 'string' ? new Date(date) : date;
    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);
    const intervals = {
        zh: [
            {
                label: '年',
                seconds: 31536000
            },
            {
                label: '个月',
                seconds: 2592000
            },
            {
                label: '天',
                seconds: 86400
            },
            {
                label: '小时',
                seconds: 3600
            },
            {
                label: '分钟',
                seconds: 60
            },
            {
                label: '秒',
                seconds: 1
            }
        ],
        en: [
            {
                label: 'year',
                seconds: 31536000
            },
            {
                label: 'month',
                seconds: 2592000
            },
            {
                label: 'day',
                seconds: 86400
            },
            {
                label: 'hour',
                seconds: 3600
            },
            {
                label: 'minute',
                seconds: 60
            },
            {
                label: 'second',
                seconds: 1
            }
        ]
    };
    for (const interval of intervals[locale]){
        const count = Math.floor(diffInSeconds / interval.seconds);
        if (count > 0) {
            if (locale === 'zh') {
                return `${count}${interval.label}前`;
            } else {
                return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`;
            }
        }
    }
    return locale === 'zh' ? '刚刚' : 'just now';
}
}}),
"[project]/src/app/[locale]/faq/[slug]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FAQDetailPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClockIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClockIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ClockIcon.js [app-rsc] (ecmascript) <export default as ClockIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$TagIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__TagIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/TagIcon.js [app-rsc] (ecmascript) <export default as TagIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ShareIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ShareIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ShareIcon.js [app-rsc] (ecmascript) <export default as ShareIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PrinterIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__PrinterIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/PrinterIcon.js [app-rsc] (ecmascript) <export default as PrinterIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChevronLeftIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeftIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js [app-rsc] (ecmascript) <export default as ChevronLeftIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ExclamationTriangleIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ExclamationTriangleIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js [app-rsc] (ecmascript) <export default as ExclamationTriangleIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$CheckCircleIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircleIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js [app-rsc] (ecmascript) <export default as CheckCircleIcon>");
;
;
;
;
;
;
;
async function generateMetadata({ params }) {
    const { locale, slug } = await params;
    const faq = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faqManager"].getFAQ(slug, locale);
    if (!faq) {
        return {
            title: 'FAQ Not Found',
            description: 'The requested FAQ could not be found.'
        };
    }
    const seoData = {
        title: faq.title,
        description: faq.shortAnswer || faq.answer.substring(0, 160),
        keywords: faq.tags.join(', '),
        canonical: `/${locale}/faq/${slug}`
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateSEOMetadata"])({
        data: seoData,
        locale
    });
}
async function FAQDetailPage({ params }) {
    const { locale, slug } = await params;
    const faq = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faqManager"].getFAQ(slug, locale);
    if (!faq) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    const relatedFaqs = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faqManager"].getRelatedFAQs(faq.id, locale, 4);
    const breadcrumbs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateBreadcrumbs"])(`/${locale}/faq/${slug}`, locale);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StructuredDataScript"], {
                data: {
                    title: faq.title,
                    description: faq.shortAnswer || faq.answer.substring(0, 200),
                    lastUpdated: faq.lastUpdated,
                    breadcrumbs
                },
                type: "Article",
                locale: locale
            }, void 0, false, {
                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StructuredDataScript"], {
                data: {
                    breadcrumbs
                },
                type: "BreadcrumbList",
                locale: locale
            }, void 0, false, {
                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                lineNumber: 88,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen bg-gray-50",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white border-b",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                className: "flex",
                                "aria-label": "Breadcrumb",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
                                    className: "flex items-center space-x-2",
                                    children: breadcrumbs.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            className: "flex items-center",
                                            children: [
                                                index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                    className: "flex-shrink-0 h-4 w-4 text-gray-400 mx-2",
                                                    fill: "currentColor",
                                                    viewBox: "0 0 20 20",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                        fillRule: "evenodd",
                                                        d: "M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",
                                                        clipRule: "evenodd"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 104,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                    lineNumber: 103,
                                                    columnNumber: 23
                                                }, this),
                                                index === breadcrumbs.length - 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-sm font-medium text-gray-500 truncate max-w-xs",
                                                    children: item.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                    lineNumber: 108,
                                                    columnNumber: 23
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: item.href,
                                                    className: "text-sm font-medium text-blue-600 hover:text-blue-700",
                                                    children: item.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                    lineNumber: 112,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, item.href, true, {
                                            fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                            lineNumber: 101,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                    lineNumber: 99,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                lineNumber: 98,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                            lineNumber: 97,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    href: `/${locale}/faq`,
                                    className: "inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChevronLeftIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeftIcon$3e$__["ChevronLeftIcon"], {
                                            className: "h-4 w-4 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                            lineNumber: 130,
                                            columnNumber: 15
                                        }, this),
                                        locale === 'zh' ? '返回FAQ列表' : 'Back to FAQ'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                    lineNumber: 126,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                lineNumber: 125,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("article", {
                                className: "bg-white rounded-lg shadow-sm border border-gray-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                                        className: "px-6 py-8 border-b border-gray-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-start justify-between mb-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `px-3 py-1 text-sm font-medium rounded-full ${faq.difficulty === 'basic' ? 'bg-green-100 text-green-800' : faq.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,
                                                                children: locale === 'zh' ? faq.difficulty === 'basic' ? '基础' : faq.difficulty === 'intermediate' ? '中级' : '高级' : faq.difficulty === 'basic' ? 'Basic' : faq.difficulty === 'intermediate' ? 'Intermediate' : 'Advanced'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                lineNumber: 141,
                                                                columnNumber: 19
                                                            }, this),
                                                            faq.medicalReview && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "inline-flex items-center px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$CheckCircleIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircleIcon$3e$__["CheckCircleIcon"], {
                                                                        className: "h-4 w-4 mr-1"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                        lineNumber: 153,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    locale === 'zh' ? '医学审核' : 'Medical Review'
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                lineNumber: 152,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 140,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "p-2 text-gray-400 hover:text-gray-600 rounded-md",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ShareIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ShareIcon$3e$__["ShareIcon"], {
                                                                    className: "h-5 w-5"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                    lineNumber: 160,
                                                                    columnNumber: 21
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                lineNumber: 159,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "p-2 text-gray-400 hover:text-gray-600 rounded-md",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PrinterIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__PrinterIcon$3e$__["PrinterIcon"], {
                                                                    className: "h-5 w-5"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                    lineNumber: 163,
                                                                    columnNumber: 21
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                lineNumber: 162,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 158,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 139,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-3xl font-bold text-gray-900 mb-4",
                                                children: faq.title
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 168,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center text-sm text-gray-500 space-x-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClockIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClockIcon$3e$__["ClockIcon"], {
                                                                className: "h-4 w-4 mr-1"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                lineNumber: 174,
                                                                columnNumber: 19
                                                            }, this),
                                                            locale === 'zh' ? '最后更新：' : 'Last updated: ',
                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(faq.lastUpdated, locale)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 173,
                                                        columnNumber: 17
                                                    }, this),
                                                    faq.author && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            locale === 'zh' ? '作者：' : 'Author: ',
                                                            faq.author
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 179,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 172,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                        lineNumber: 138,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-6 py-8",
                                        children: [
                                            (faq.category === 'symptoms' || faq.tags.includes('急性溶血') || faq.tags.includes('emergency')) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mb-6 p-4 bg-red-50 border border-red-200 rounded-md",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ExclamationTriangleIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ExclamationTriangleIcon$3e$__["ExclamationTriangleIcon"], {
                                                            className: "h-5 w-5 text-red-400 mt-0.5"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                            lineNumber: 192,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "ml-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                    className: "text-sm font-medium text-red-800",
                                                                    children: locale === 'zh' ? '紧急提醒' : 'Emergency Notice'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                    lineNumber: 194,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mt-1 text-sm text-red-700",
                                                                    children: locale === 'zh' ? '如出现急性溶血症状，请立即就医。本内容仅供参考，不能替代专业医疗建议。' : 'If you experience acute hemolysis symptoms, seek immediate medical attention. This content is for reference only and cannot replace professional medical advice.'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                    lineNumber: 197,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                            lineNumber: 193,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 190,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "prose prose-lg max-w-none",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    dangerouslySetInnerHTML: {
                                                        __html: faq.answer
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                    lineNumber: 210,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 209,
                                                columnNumber: 15
                                            }, this),
                                            faq.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-8 pt-6 border-t border-gray-200",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-medium text-gray-700 mb-3",
                                                        children: locale === 'zh' ? '相关标签' : 'Related Tags'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 216,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex flex-wrap gap-2",
                                                        children: faq.tags.map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$TagIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__TagIcon$3e$__["TagIcon"], {
                                                                        className: "h-3 w-3 mr-1"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                        lineNumber: 222,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    tag
                                                                ]
                                                            }, tag, true, {
                                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                lineNumber: 221,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 219,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 215,
                                                columnNumber: 17
                                            }, this),
                                            faq.sources && faq.sources.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-6 pt-6 border-t border-gray-200",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-medium text-gray-700 mb-3",
                                                        children: locale === 'zh' ? '参考来源' : 'Sources'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 233,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "text-sm text-gray-600 space-y-1",
                                                        children: faq.sources.map((source, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: [
                                                                    "• ",
                                                                    source
                                                                ]
                                                            }, index, true, {
                                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                                lineNumber: 238,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 236,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 232,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                        lineNumber: 187,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                lineNumber: 136,
                                columnNumber: 11
                            }, this),
                            relatedFaqs.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-12",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-2xl font-bold text-gray-900 mb-6",
                                        children: locale === 'zh' ? '相关问题' : 'Related Questions'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                        lineNumber: 249,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                        children: relatedFaqs.map((relatedFaq)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                href: `/${locale}/faq/${relatedFaq.slug}`,
                                                className: "block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-lg font-semibold text-gray-900 mb-2",
                                                        children: relatedFaq.title
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 259,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-600 text-sm",
                                                        children: relatedFaq.shortAnswer || relatedFaq.answer.substring(0, 100) + '...'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                        lineNumber: 262,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, relatedFaq.id, true, {
                                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                                lineNumber: 254,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                        lineNumber: 252,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                                lineNumber: 248,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                        lineNumber: 123,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/[locale]/faq/[slug]/page.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
}}),
"[project]/src/app/[locale]/faq/[slug]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/faq/[slug]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__75e19a08._.js.map