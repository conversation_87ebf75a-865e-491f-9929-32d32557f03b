{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\n\nexport default function RootPage() {\n  // Redirect to Chinese version by default\n  redirect('/zh')\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS;IACtB,yCAAyC;IACzC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX", "debugId": null}}]}