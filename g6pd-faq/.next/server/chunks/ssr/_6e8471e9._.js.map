{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/medications/page.tsx"], "sourcesContent": ["import { getTranslations } from 'next-intl/server'\nimport Link from 'next/link'\nimport { Metadata } from 'next'\nimport {\n  ShieldCheckIcon,\n  ExclamationTriangleIcon,\n  BookOpenIcon,\n  BeakerIcon\n} from '@heroicons/react/24/outline'\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}): Promise<Metadata> {\n  const { locale } = await params\n  const t = await getTranslations({ locale })\n\n  return {\n    title: locale === 'zh' ? '用药指导 - G6PD缺乏症FAQ' : 'Medication Guide - G6PD Deficiency FAQ',\n    description: locale === 'zh' \n      ? '为G6PD缺乏症患者提供详细的用药指导，包括禁用药物清单、中药注意事项、口服液安全指南等。'\n      : 'Comprehensive medication guide for G6PD deficiency patients, including prohibited drugs, Chinese medicine precautions, and oral solution safety guidelines.',\n    keywords: locale === 'zh'\n      ? 'G6PD缺乏症,用药指导,禁用药物,中药,口服液,药物安全'\n      : 'G6PD deficiency,medication guide,prohibited drugs,Chinese medicine,oral solutions,drug safety'\n  }\n}\n\nexport default async function MedicationsPage({ params }: { params: Promise<{ locale: string }> }) {\n  const { locale } = await params\n  const t = await getTranslations({ locale })\n\n  const medicationCategories = [\n    {\n      title: locale === 'zh' ? '中药相关' : 'Chinese Medicine',\n      href: `/${locale}/medications/chinese-medicine`,\n      icon: BookOpenIcon,\n      description: locale === 'zh' ? '中药使用注意事项和禁忌' : 'Chinese medicine precautions and contraindications',\n      count: '50+',\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      borderColor: 'border-green-200'\n    },\n    {\n      title: locale === 'zh' ? '口服液相关' : 'Oral Solutions',\n      href: `/${locale}/medications/oral-solutions`,\n      icon: BeakerIcon,\n      description: locale === 'zh' ? '口服液安全指导和建议' : 'Oral solution safety guidelines and recommendations',\n      count: '30+',\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      borderColor: 'border-blue-200'\n    },\n    {\n      title: locale === 'zh' ? '西药相关' : 'Western Medicine',\n      href: `/${locale}/medications/western-medicine`,\n      icon: ShieldCheckIcon,\n      description: locale === 'zh' ? '西药禁忌和安全用药' : 'Western medicine contraindications and safe usage',\n      count: '40+',\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n      borderColor: 'border-purple-200'\n    }\n  ]\n\n  const commonQuestions = [\n    {\n      question: locale === 'zh' ? 'G6PD缺乏症患者可以服用阿司匹林吗？' : 'Can G6PD deficient patients take aspirin?',\n      href: `/${locale}/faq/aspirin-safety`\n    },\n    {\n      question: locale === 'zh' ? '哪些中药成分需要避免？' : 'Which Chinese medicine ingredients should be avoided?',\n      href: `/${locale}/faq/chinese-medicine-ingredients`\n    },\n    {\n      question: locale === 'zh' ? '儿童口服液选择注意事项' : 'Considerations for choosing oral solutions for children',\n      href: `/${locale}/faq/children-oral-solutions`\n    },\n    {\n      question: locale === 'zh' ? '如何判断药物是否安全？' : 'How to determine if a medication is safe?',\n      href: `/${locale}/faq/medication-safety-check`\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              {locale === 'zh' ? '用药指导' : 'Medication Guide'}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90\">\n              {locale === 'zh' \n                ? '为G6PD缺乏症患者提供专业、安全的用药指导'\n                : 'Professional and safe medication guidance for G6PD deficiency patients'\n              }\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Emergency Alert */}\n      <section className=\"bg-red-50 border-b border-red-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-center\">\n            <ExclamationTriangleIcon className=\"h-6 w-6 text-red-600 mr-3\" />\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-semibold text-red-800\">\n                {locale === 'zh' ? '重要提醒' : 'Important Notice'}\n              </h3>\n              <p className=\"text-red-700 mt-1\">\n                {locale === 'zh' \n                  ? '服用任何新药物前，请务必咨询医生或药师！'\n                  : 'Always consult a doctor or pharmacist before taking any new medication!'\n                }\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Medication Categories */}\n      <section className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              {locale === 'zh' ? '药物分类' : 'Medication Categories'}\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              {locale === 'zh' \n                ? '按类别查看详细的用药指导信息'\n                : 'View detailed medication guidance by category'\n              }\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {medicationCategories.map((category, index) => (\n              <Link\n                key={index}\n                href={category.href}\n                className={`group p-6 bg-white rounded-lg border ${category.borderColor} hover:shadow-lg transition-all`}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <category.icon className={`h-8 w-8 ${category.color}`} />\n                  <span className={`text-sm font-semibold ${category.color} ${category.bgColor} px-2 py-1 rounded`}>\n                    {category.count}\n                  </span>\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-700\">\n                  {category.title}\n                </h3>\n                <p className=\"text-gray-600\">\n                  {category.description}\n                </p>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Common Questions */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              {locale === 'zh' ? '常见用药问题' : 'Common Medication Questions'}\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              {locale === 'zh' \n                ? '患者最关心的用药安全问题'\n                : 'Most concerning medication safety questions for patients'\n              }\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {commonQuestions.map((item, index) => (\n              <Link\n                key={index}\n                href={item.href}\n                className=\"p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <h3 className=\"text-lg font-medium text-gray-900 hover:text-blue-700\">\n                  {item.question}\n                </h3>\n              </Link>\n            ))}\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <Link\n              href={`/${locale}/faq`}\n              className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n            >\n              {locale === 'zh' ? '查看所有问题' : 'View All Questions'}\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;;;;;AAOO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;IAAO;IAEzC,OAAO;QACL,OAAO,WAAW,OAAO,sBAAsB;QAC/C,aAAa,WAAW,OACpB,kDACA;QACJ,UAAU,WAAW,OACjB,kCACA;IACN;AACF;AAEe,eAAe,gBAAgB,EAAE,MAAM,EAA2C;IAC/F,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;IAAO;IAEzC,MAAM,uBAAuB;QAC3B;YACE,OAAO,WAAW,OAAO,SAAS;YAClC,MAAM,CAAC,CAAC,EAAE,OAAO,6BAA6B,CAAC;YAC/C,MAAM,uNAAA,CAAA,eAAY;YAClB,aAAa,WAAW,OAAO,gBAAgB;YAC/C,OAAO;YACP,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO,WAAW,OAAO,UAAU;YACnC,MAAM,CAAC,CAAC,EAAE,OAAO,2BAA2B,CAAC;YAC7C,MAAM,mNAAA,CAAA,aAAU;YAChB,aAAa,WAAW,OAAO,eAAe;YAC9C,OAAO;YACP,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO,WAAW,OAAO,SAAS;YAClC,MAAM,CAAC,CAAC,EAAE,OAAO,6BAA6B,CAAC;YAC/C,MAAM,6NAAA,CAAA,kBAAe;YACrB,aAAa,WAAW,OAAO,cAAc;YAC7C,OAAO;YACP,OAAO;YACP,SAAS;YACT,aAAa;QACf;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,UAAU,WAAW,OAAO,wBAAwB;YACpD,MAAM,CAAC,CAAC,EAAE,OAAO,mBAAmB,CAAC;QACvC;QACA;YACE,UAAU,WAAW,OAAO,gBAAgB;YAC5C,MAAM,CAAC,CAAC,EAAE,OAAO,iCAAiC,CAAC;QACrD;QACA;YACE,UAAU,WAAW,OAAO,gBAAgB;YAC5C,MAAM,CAAC,CAAC,EAAE,OAAO,4BAA4B,CAAC;QAChD;QACA;YACE,UAAU,WAAW,OAAO,gBAAgB;YAC5C,MAAM,CAAC,CAAC,EAAE,OAAO,4BAA4B,CAAC;QAChD;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,WAAW,OAAO,SAAS;;;;;;0CAE9B,8OAAC;gCAAE,WAAU;0CACV,WAAW,OACR,2BACA;;;;;;;;;;;;;;;;;;;;;;0BAQZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6OAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CACnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,SAAS;;;;;;kDAE9B,8OAAC;wCAAE,WAAU;kDACV,WAAW,OACR,yBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,WAAW,OAAO,SAAS;;;;;;8CAE9B,8OAAC;oCAAE,WAAU;8CACV,WAAW,OACR,mBACA;;;;;;;;;;;;sCAKR,8OAAC;4BAAI,WAAU;sCACZ,qBAAqB,GAAG,CAAC,CAAC,UAAU,sBACnC,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,SAAS,IAAI;oCACnB,WAAW,CAAC,qCAAqC,EAAE,SAAS,WAAW,CAAC,+BAA+B,CAAC;;sDAExG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,SAAS,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,KAAK,EAAE;;;;;;8DACrD,8OAAC;oDAAK,WAAW,CAAC,sBAAsB,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,kBAAkB,CAAC;8DAC7F,SAAS,KAAK;;;;;;;;;;;;sDAGnB,8OAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAEjB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;;mCAdlB;;;;;;;;;;;;;;;;;;;;;0BAuBf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,WAAW,OAAO,WAAW;;;;;;8CAEhC,8OAAC;oCAAE,WAAU;8CACV,WAAW,OACR,iBACA;;;;;;;;;;;;sCAKR,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAEV,cAAA,8OAAC;wCAAG,WAAU;kDACX,KAAK,QAAQ;;;;;;mCALX;;;;;;;;;;sCAWX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;gCACtB,WAAU;0CAET,WAAW,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/ShieldCheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/ExclamationTriangleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,wBAAwB,EAC/B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/BookOpenIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction BookOpenIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BookOpenIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/%40heroicons/react/24/outline/esm/BeakerIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction BeakerIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BeakerIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA;wBAEzG,YAAA;4BAAA;4BAAA,CACA,kCAD4D;4BAC5D,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kCAChDY,QAAAA,CAAAA,CAAY;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BAC<PERSON>,MAAMZ,UAAUa,QAAQ;;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA;oBAAAA,CAAU;iBAAA;;iBACV,2CAA2C;sBAC3CC,IAAAA,CAAAA;gBAAAA,CAAY,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;eACZC,UAAU;;SACVC,UAAU,EAAE;UACd,QAAA;YAAA,MAAA;gBACAC,OAAU,QAAA;wBAAA;4BACRC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,OAAYnB,eAAAA,EAAAA,MAAAA,MAAAA,MAAAA,MAAAA,EAAAA,iBAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAAA,EAAAA;4BACd,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACA,MAAA,CAAA,YAAA,CAAA", "ignoreList": [0], "debugId": null}}]}