{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/faq-data.ts"], "sourcesContent": ["export interface FAQItem {\n  id: string\n  question: string\n  answer: string\n  category: string\n  subcategory?: string\n  keywords: string[]\n  searchVolume?: number\n  locale: 'zh' | 'en'\n}\n\nexport interface FAQCategory {\n  id: string\n  name: string\n  description: string\n  icon: string\n  subcategories?: FAQSubcategory[]\n}\n\nexport interface FAQSubcategory {\n  id: string\n  name: string\n  description: string\n}\n\n// FAQ Categories\nexport const faqCategories: Record<'zh' | 'en', FAQCategory[]> = {\n  zh: [\n    {\n      id: 'medications',\n      name: '用药安全',\n      description: '了解G6PD缺乏症患者的用药禁忌和安全指导',\n      icon: '💊',\n      subcategories: [\n        {\n          id: 'chinese-medicine',\n          name: '中药禁忌',\n          description: '不能使用的中药成分和药物'\n        },\n        {\n          id: 'oral-solutions',\n          name: '口服液安全',\n          description: '各种口服液药物的安全性评估'\n        },\n        {\n          id: 'western-medicine',\n          name: '西药指导',\n          description: '西药使用注意事项'\n        }\n      ]\n    },\n    {\n      id: 'symptoms',\n      name: '症状识别',\n      description: '学习识别G6PD缺乏症的症状和紧急情况',\n      icon: '🩺'\n    },\n    {\n      id: 'diet',\n      name: '饮食指导',\n      description: '安全的饮食建议和食物禁忌',\n      icon: '🥗'\n    },\n    {\n      id: 'treatment',\n      name: '治疗方案',\n      description: '治疗选择和医疗管理',\n      icon: '🏥'\n    }\n  ],\n  en: [\n    {\n      id: 'medications',\n      name: 'Medication Safety',\n      description: 'Learn about medication contraindications and safety guidelines for G6PD deficiency patients',\n      icon: '💊',\n      subcategories: [\n        {\n          id: 'chinese-medicine',\n          name: 'Chinese Medicine',\n          description: 'Chinese medicine ingredients and drugs to avoid'\n        },\n        {\n          id: 'oral-solutions',\n          name: 'Oral Solutions',\n          description: 'Safety assessment of various oral solution medications'\n        },\n        {\n          id: 'western-medicine',\n          name: 'Western Medicine',\n          description: 'Western medicine usage precautions'\n        }\n      ]\n    },\n    {\n      id: 'symptoms',\n      name: 'Symptom Recognition',\n      description: 'Learn to recognize G6PD deficiency symptoms and emergency situations',\n      icon: '🩺'\n    },\n    {\n      id: 'diet',\n      name: 'Diet Guide',\n      description: 'Safe dietary recommendations and food restrictions',\n      icon: '🥗'\n    },\n    {\n      id: 'treatment',\n      name: 'Treatment Options',\n      description: 'Treatment choices and medical management',\n      icon: '🏥'\n    }\n  ]\n}\n\n// Sample FAQ data based on long-tail keywords\nexport const faqData: FAQItem[] = [\n  // Chinese Medicine FAQs (based on 蚕豆病中药长尾词.md)\n  {\n    id: 'zh-chinese-medicine-1',\n    question: '蚕豆病不能吃的中药有哪些？',\n    answer: 'G6PD缺乏症（蚕豆病）患者需要避免以下中药成分：薄荷、金银花、黄连、大黄、麻黄、茵陈等。这些中药可能引起溶血性贫血发作。使用任何中药前，请务必咨询医生并告知您的G6PD缺乏症状况。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '中药', '禁忌', '不能吃'],\n    searchVolume: 472000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-chinese-medicine-2',\n    question: '蚕豆病哪些中药不能吃？',\n    answer: '蚕豆病患者应避免使用含有以下成分的中药：1. 薄荷类：薄荷、薄荷脑等；2. 清热解毒类：金银花、黄连、黄芩等；3. 泻下类：大黄、芒硝等；4. 发散风寒类：麻黄、桂枝等。建议在使用任何中药前咨询专业中医师。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '哪些中药', '不能吃'],\n    searchVolume: 235000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-chinese-medicine-3',\n    question: '蚕豆病能吃的中药有哪些？',\n    answer: '蚕豆病患者可以安全使用的中药包括：1. 补益类：人参、党参、黄芪、当归等；2. 健脾类：白术、茯苓、山药等；3. 养血类：熟地黄、白芍、阿胶等。但即使是安全的中药，也建议在医生指导下使用，并密切观察身体反应。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '能吃', '中药'],\n    searchVolume: 169000,\n    locale: 'zh'\n  },\n  \n  // Oral Solutions FAQs (based on 蚕豆病口服液长尾词.md)\n  {\n    id: 'zh-oral-solutions-1',\n    question: '蚕豆病能吃双黄连口服液吗？',\n    answer: '蚕豆病患者不建议使用双黄连口服液。双黄连口服液含有金银花、黄芩、连翘等成分，其中金银花和黄芩可能引起G6PD缺乏症患者发生溶血反应。如需治疗感冒等症状，请咨询医生选择安全的替代药物。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '双黄连口服液'],\n    searchVolume: 24200,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-oral-solutions-2',\n    question: '蚕豆病可以吃抗病毒口服液吗？',\n    answer: '蚕豆病患者使用抗病毒口服液需要谨慎。不同品牌的抗病毒口服液成分不同，有些含有板蓝根、金银花等可能引起溶血的成分。建议：1. 使用前仔细查看成分表；2. 咨询医生或药师；3. 选择不含禁忌成分的产品；4. 使用后密切观察身体反应。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '抗病毒口服液'],\n    searchVolume: 18700,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-oral-solutions-3',\n    question: '蚕豆病能吃保济口服液吗？',\n    answer: '蚕豆病患者可以谨慎使用保济口服液。保济口服液主要含有钩藤、薄荷、菊花等成分，其中薄荷可能对部分G6PD缺乏症患者有影响。建议：1. 首次使用时小剂量试用；2. 密切观察是否出现乏力、面色苍白等症状；3. 如有不适立即停用并就医。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '保济口服液'],\n    searchVolume: 1440,\n    locale: 'zh'\n  },\n\n  // Symptoms FAQs\n  {\n    id: 'zh-symptoms-1',\n    question: '蚕豆病发作有什么症状？',\n    answer: 'G6PD缺乏症急性发作的主要症状包括：1. 急性溶血症状：面色苍白、乏力、头晕；2. 黄疸：皮肤和眼白发黄；3. 尿液改变：尿色加深，呈茶色或酱油色；4. 其他症状：发热、恶心、呕吐、腹痛。如出现这些症状，应立即停用可疑药物并紧急就医。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '发作', '症状'],\n    locale: 'zh'\n  },\n  {\n    id: 'zh-symptoms-2',\n    question: '蚕豆病黄疸多久能好？',\n    answer: '蚕豆病引起的黄疸恢复时间因人而异，通常需要1-2周。恢复时间取决于：1. 溶血程度：轻度溶血3-7天，重度溶血可能需要2-3周；2. 治疗及时性：早期治疗恢复更快；3. 个体差异：年龄、体质等因素影响恢复速度。期间需要充分休息、多饮水，避免再次接触诱发因素。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '黄疸', '多久', '恢复'],\n    locale: 'zh'\n  }\n]\n\n// English FAQ data\nexport const englishFaqData: FAQItem[] = [\n  {\n    id: 'en-chinese-medicine-1',\n    question: 'What Chinese medicines should G6PD deficiency patients avoid?',\n    answer: 'G6PD deficiency patients should avoid Chinese medicines containing: mint, honeysuckle, coptis, rhubarb, ephedra, and artemisia. These ingredients may trigger hemolytic anemia attacks. Always consult a doctor before using any Chinese medicine and inform them of your G6PD deficiency status.',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['G6PD deficiency', 'Chinese medicine', 'contraindications', 'avoid'],\n    locale: 'en'\n  },\n  {\n    id: 'en-oral-solutions-1',\n    question: 'Can G6PD deficiency patients take Shuanghuanglian oral solution?',\n    answer: 'G6PD deficiency patients are not recommended to use Shuanghuanglian oral solution. It contains honeysuckle, scutellaria, and forsythia, where honeysuckle and scutellaria may cause hemolytic reactions in G6PD deficient patients. For cold treatment, please consult a doctor for safe alternative medications.',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['G6PD deficiency', 'Shuanghuanglian', 'oral solution'],\n    locale: 'en'\n  },\n  {\n    id: 'en-symptoms-1',\n    question: 'What are the symptoms of G6PD deficiency crisis?',\n    answer: 'Main symptoms of acute G6PD deficiency crisis include: 1. Acute hemolysis: pallor, fatigue, dizziness; 2. Jaundice: yellowing of skin and eyes; 3. Urine changes: dark urine (tea or cola-colored); 4. Other symptoms: fever, nausea, vomiting, abdominal pain. If these symptoms occur, immediately stop the suspected medication and seek emergency medical care.',\n    category: 'symptoms',\n    keywords: ['G6PD deficiency', 'crisis', 'symptoms'],\n    locale: 'en'\n  }\n]\n\n// Combine all FAQ data\nexport const allFaqData = [...faqData, ...englishFaqData]\n\n// Helper functions\nexport function getFaqsByCategory(category: string, locale: 'zh' | 'en'): FAQItem[] {\n  return allFaqData.filter(faq => faq.category === category && faq.locale === locale)\n}\n\nexport function getFaqsBySubcategory(category: string, subcategory: string, locale: 'zh' | 'en'): FAQItem[] {\n  return allFaqData.filter(faq => \n    faq.category === category && \n    faq.subcategory === subcategory && \n    faq.locale === locale\n  )\n}\n\nexport function searchFaqs(query: string, locale: 'zh' | 'en'): FAQItem[] {\n  const lowercaseQuery = query.toLowerCase()\n  return allFaqData.filter(faq => \n    faq.locale === locale && (\n      faq.question.toLowerCase().includes(lowercaseQuery) ||\n      faq.answer.toLowerCase().includes(lowercaseQuery) ||\n      faq.keywords.some(keyword => keyword.toLowerCase().includes(lowercaseQuery))\n    )\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;AA0BO,MAAM,gBAAoD;IAC/D,IAAI;QACF;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;KACD;IACD,IAAI;QACF;YACE,IAAI;YAC<PERSON>,MAAM;YAC<PERSON>,aAAa;YACb,MAAM;YAC<PERSON>,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;KACD;AACH;AAGO,MAAM,UAAqB;IAChC,+CAA+C;IAC/C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAM;YAAM;SAAM;QACpC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAQ;SAAM;QAChC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAM;SAAK;QAC7B,cAAc;QACd,QAAQ;IACV;IAEA,8CAA8C;IAC9C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAS;QAC3B,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAS;QAC3B,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAQ;QAC1B,cAAc;QACd,QAAQ;IACV;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;SAAK;QAC7B,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAM;SAAK;QACnC,QAAQ;IACV;CACD;AAGM,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAmB;YAAoB;YAAqB;SAAQ;QAC/E,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAmB;YAAmB;SAAgB;QACjE,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAU;SAAW;QACnD,QAAQ;IACV;CACD;AAGM,MAAM,aAAa;OAAI;OAAY;CAAe;AAGlD,SAAS,kBAAkB,QAAgB,EAAE,MAAmB;IACrE,OAAO,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,YAAY,IAAI,MAAM,KAAK;AAC9E;AAEO,SAAS,qBAAqB,QAAgB,EAAE,WAAmB,EAAE,MAAmB;IAC7F,OAAO,WAAW,MAAM,CAAC,CAAA,MACvB,IAAI,QAAQ,KAAK,YACjB,IAAI,WAAW,KAAK,eACpB,IAAI,MAAM,KAAK;AAEnB;AAEO,SAAS,WAAW,KAAa,EAAE,MAAmB;IAC3D,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,WAAW,MAAM,CAAC,CAAA,MACvB,IAAI,MAAM,KAAK,UAAU,CACvB,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACpC,IAAI,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,gBAC9D;AAEJ", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/faq/medications/oral-solutions/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { getFaqsBySubcategory } from '@/lib/faq-data'\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  const title = isZh ? '口服液安全 - G6PD缺乏症（蚕豆病）' : 'Oral Solution Safety - G6PD Deficiency'\n  const description = isZh \n    ? 'G6PD缺乏症患者各种口服液药物的安全性评估，包括儿童常用感冒药、退烧药等'\n    : 'Safety assessment of various oral solution medications for G6PD deficiency patients, including common cold and fever medicines for children'\n\n  return {\n    title,\n    description,\n    openGraph: {\n      title,\n      description,\n      type: 'website',\n    },\n  }\n}\n\nexport default async function OralSolutionsPage({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  const faqs = getFaqsBySubcategory('medications', 'oral-solutions', locale as 'zh' | 'en')\n\n  // Oral solutions safety data\n  const oralSolutions = {\n    zh: [\n      {\n        category: '感冒类口服液',\n        medicines: [\n          { name: '双黄连口服液', safety: '不安全', reason: '含金银花、黄芩等禁忌成分' },\n          { name: '抗病毒口服液', safety: '需谨慎', reason: '部分含板蓝根等成分' },\n          { name: '小儿感冒颗粒', safety: '需谨慎', reason: '查看具体成分' },\n          { name: '999感冒灵', safety: '相对安全', reason: '主要成分相对安全' }\n        ]\n      },\n      {\n        category: '止咳类口服液',\n        medicines: [\n          { name: '川贝枇杷膏', safety: '相对安全', reason: '主要成分川贝、枇杷叶安全' },\n          { name: '急支糖浆', safety: '需谨慎', reason: '含鱼腥草等成分' },\n          { name: '蛇胆川贝液', safety: '相对安全', reason: '主要成分安全' },\n          { name: '复方甘草口服液', safety: '相对安全', reason: '甘草成分相对安全' }\n        ]\n      },\n      {\n        category: '消化类口服液',\n        medicines: [\n          { name: '保济口服液', safety: '需谨慎', reason: '含薄荷等成分' },\n          { name: '藿香正气液', safety: '需谨慎', reason: '含藿香、紫苏等成分' },\n          { name: '健胃消食口服液', safety: '相对安全', reason: '主要成分相对安全' },\n          { name: '妈咪爱', safety: '安全', reason: '益生菌制剂，安全' }\n        ]\n      }\n    ],\n    en: [\n      {\n        category: 'Cold Medicine Oral Solutions',\n        medicines: [\n          { name: 'Shuanghuanglian Oral Solution', safety: 'Unsafe', reason: 'Contains honeysuckle, scutellaria and other contraindicated ingredients' },\n          { name: 'Antiviral Oral Solution', safety: 'Use with Caution', reason: 'Some contain isatis root and other ingredients' },\n          { name: 'Pediatric Cold Granules', safety: 'Use with Caution', reason: 'Check specific ingredients' },\n          { name: '999 Cold Medicine', safety: 'Relatively Safe', reason: 'Main ingredients are relatively safe' }\n        ]\n      },\n      {\n        category: 'Cough Medicine Oral Solutions',\n        medicines: [\n          { name: 'Fritillaria Loquat Syrup', safety: 'Relatively Safe', reason: 'Main ingredients fritillaria and loquat leaf are safe' },\n          { name: 'Jizhitang Syrup', safety: 'Use with Caution', reason: 'Contains houttuynia and other ingredients' },\n          { name: 'Snake Gallbladder Fritillaria Solution', safety: 'Relatively Safe', reason: 'Main ingredients are safe' },\n          { name: 'Compound Licorice Oral Solution', safety: 'Relatively Safe', reason: 'Licorice ingredients are relatively safe' }\n        ]\n      },\n      {\n        category: 'Digestive Oral Solutions',\n        medicines: [\n          { name: 'Baoji Oral Solution', safety: 'Use with Caution', reason: 'Contains mint and other ingredients' },\n          { name: 'Huoxiang Zhengqi Solution', safety: 'Use with Caution', reason: 'Contains agastache, perilla and other ingredients' },\n          { name: 'Stomach-strengthening Digestive Oral Solution', safety: 'Relatively Safe', reason: 'Main ingredients are relatively safe' },\n          { name: 'Mamiai', safety: 'Safe', reason: 'Probiotic preparation, safe' }\n        ]\n      }\n    ]\n  }\n\n  const getSafetyColor = (safety: string) => {\n    if (safety === '安全' || safety === 'Safe') return 'bg-green-100 text-green-800'\n    if (safety === '相对安全' || safety === 'Relatively Safe') return 'bg-blue-100 text-blue-800'\n    if (safety === '需谨慎' || safety === 'Use with Caution') return 'bg-yellow-100 text-yellow-800'\n    if (safety === '不安全' || safety === 'Unsafe') return 'bg-red-100 text-red-800'\n    return 'bg-gray-100 text-gray-800'\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Breadcrumb */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <nav className=\"flex\" aria-label=\"Breadcrumb\">\n            <ol className=\"flex items-center space-x-4\">\n              <li>\n                <Link href={`/${locale}`} className=\"text-gray-500 hover:text-gray-700\">\n                  {isZh ? '首页' : 'Home'}\n                </Link>\n              </li>\n              <li><span className=\"text-gray-400\">/</span></li>\n              <li>\n                <Link href={`/${locale}/faq`} className=\"text-gray-500 hover:text-gray-700\">\n                  {isZh ? '常见问题' : 'FAQ'}\n                </Link>\n              </li>\n              <li><span className=\"text-gray-400\">/</span></li>\n              <li>\n                <Link href={`/${locale}/faq/medications`} className=\"text-gray-500 hover:text-gray-700\">\n                  {isZh ? '用药安全' : 'Medication Safety'}\n                </Link>\n              </li>\n              <li><span className=\"text-gray-400\">/</span></li>\n              <li>\n                <span className=\"text-gray-900 font-medium\">\n                  {isZh ? '口服液安全' : 'Oral Solutions'}\n                </span>\n              </li>\n            </ol>\n          </nav>\n        </div>\n      </div>\n\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-orange-500 to-red-600 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <div className=\"text-6xl mb-4\">🧪</div>\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n              {isZh ? '口服液安全指南' : 'Oral Solution Safety Guide'}\n            </h1>\n            <p className=\"text-xl text-orange-100 max-w-3xl mx-auto\">\n              {isZh \n                ? 'G6PD缺乏症患者各种口服液药物的安全性评估和使用指导'\n                : 'Safety assessment and usage guidance for various oral solution medications for G6PD deficiency patients'\n              }\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        {/* Safety Categories */}\n        <div className=\"mb-12\">\n          <h2 className=\"text-2xl font-bold mb-6 text-gray-900\">\n            {isZh ? '口服液安全性分类' : 'Oral Solution Safety Categories'}\n          </h2>\n          <div className=\"grid md:grid-cols-4 gap-4 mb-8\">\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 text-center\">\n              <div className=\"text-green-600 font-semibold mb-2\">{isZh ? '安全' : 'Safe'}</div>\n              <div className=\"text-sm text-green-700\">{isZh ? '可以正常使用' : 'Can be used normally'}</div>\n            </div>\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 text-center\">\n              <div className=\"text-blue-600 font-semibold mb-2\">{isZh ? '相对安全' : 'Relatively Safe'}</div>\n              <div className=\"text-sm text-blue-700\">{isZh ? '建议医生指导下使用' : 'Recommended under doctor guidance'}</div>\n            </div>\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center\">\n              <div className=\"text-yellow-600 font-semibold mb-2\">{isZh ? '需谨慎' : 'Use with Caution'}</div>\n              <div className=\"text-sm text-yellow-700\">{isZh ? '仔细查看成分，小心使用' : 'Check ingredients carefully, use with care'}</div>\n            </div>\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 text-center\">\n              <div className=\"text-red-600 font-semibold mb-2\">{isZh ? '不安全' : 'Unsafe'}</div>\n              <div className=\"text-sm text-red-700\">{isZh ? '避免使用' : 'Avoid use'}</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Oral Solutions List */}\n        <div className=\"space-y-8 mb-12\">\n          {oralSolutions[locale as 'zh' | 'en'].map((category, index) => (\n            <div key={index} className=\"bg-white rounded-lg shadow-md p-8\">\n              <h3 className=\"text-xl font-bold mb-6 text-gray-900\">\n                {category.category}\n              </h3>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                {category.medicines.map((medicine, medIndex) => (\n                  <div key={medIndex} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h4 className=\"font-semibold text-gray-900\">{medicine.name}</h4>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSafetyColor(medicine.safety)}`}>\n                        {medicine.safety}\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600\">{medicine.reason}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* FAQ Section */}\n        <div className=\"bg-white rounded-lg shadow-md p-8 mb-12\">\n          <h2 className=\"text-2xl font-bold mb-6 text-gray-900\">\n            {isZh ? '口服液使用常见问题' : 'Common Questions About Oral Solution Use'}\n          </h2>\n          <div className=\"space-y-6\">\n            {faqs.map((faq) => (\n              <div key={faq.id} className=\"border-b border-gray-200 pb-6 last:border-b-0\">\n                <h3 className=\"text-lg font-semibold mb-3 text-gray-900\">\n                  {faq.question}\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed mb-3\">\n                  {faq.answer}\n                </p>\n                <div className=\"flex flex-wrap gap-2\">\n                  {faq.keywords.map((keyword) => (\n                    <span\n                      key={keyword}\n                      className=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full\"\n                    >\n                      {keyword}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Usage Guidelines */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-blue-800 mb-4\">\n            {isZh ? '使用指导原则' : 'Usage Guidelines'}\n          </h3>\n          <ul className=\"space-y-2 text-blue-700\">\n            <li className=\"flex items-start\">\n              <span className=\"text-blue-600 mr-2\">•</span>\n              {isZh \n                ? '使用任何口服液前，仔细阅读成分表，避免含有禁忌成分的产品'\n                : 'Before using any oral solution, carefully read the ingredient list and avoid products containing contraindicated ingredients'\n              }\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"text-blue-600 mr-2\">•</span>\n              {isZh \n                ? '首次使用新的口服液时，建议小剂量试用，观察身体反应'\n                : 'When using a new oral solution for the first time, it is recommended to try a small dose and observe body reactions'\n              }\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"text-blue-600 mr-2\">•</span>\n              {isZh \n                ? '如出现面色苍白、乏力、尿色加深等症状，立即停用并就医'\n                : 'If symptoms such as pallor, fatigue, or dark urine occur, stop use immediately and seek medical attention'\n              }\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"text-blue-600 mr-2\">•</span>\n              {isZh \n                ? '购买药物时告知药师您的G6PD缺乏症状况，寻求专业建议'\n                : 'When purchasing medications, inform the pharmacist of your G6PD deficiency status and seek professional advice'\n              }\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,MAAM,QAAQ,OAAO,yBAAyB;IAC9C,MAAM,cAAc,OAChB,0CACA;IAEJ,OAAO;QACL;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;QACR;IACF;AACF;AAEe,eAAe,kBAAkB,EAC9C,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,MAAM,OAAO,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe,kBAAkB;IAEnE,6BAA6B;IAC7B,MAAM,gBAAgB;QACpB,IAAI;YACF;gBACE,UAAU;gBACV,WAAW;oBACT;wBAAE,MAAM;wBAAU,QAAQ;wBAAO,QAAQ;oBAAe;oBACxD;wBAAE,MAAM;wBAAU,QAAQ;wBAAO,QAAQ;oBAAY;oBACrD;wBAAE,MAAM;wBAAU,QAAQ;wBAAO,QAAQ;oBAAS;oBAClD;wBAAE,MAAM;wBAAU,QAAQ;wBAAQ,QAAQ;oBAAW;iBACtD;YACH;YACA;gBACE,UAAU;gBACV,WAAW;oBACT;wBAAE,MAAM;wBAAS,QAAQ;wBAAQ,QAAQ;oBAAe;oBACxD;wBAAE,MAAM;wBAAQ,QAAQ;wBAAO,QAAQ;oBAAU;oBACjD;wBAAE,MAAM;wBAAS,QAAQ;wBAAQ,QAAQ;oBAAS;oBAClD;wBAAE,MAAM;wBAAW,QAAQ;wBAAQ,QAAQ;oBAAW;iBACvD;YACH;YACA;gBACE,UAAU;gBACV,WAAW;oBACT;wBAAE,MAAM;wBAAS,QAAQ;wBAAO,QAAQ;oBAAS;oBACjD;wBAAE,MAAM;wBAAS,QAAQ;wBAAO,QAAQ;oBAAY;oBACpD;wBAAE,MAAM;wBAAW,QAAQ;wBAAQ,QAAQ;oBAAW;oBACtD;wBAAE,MAAM;wBAAO,QAAQ;wBAAM,QAAQ;oBAAW;iBACjD;YACH;SACD;QACD,IAAI;YACF;gBACE,UAAU;gBACV,WAAW;oBACT;wBAAE,MAAM;wBAAiC,QAAQ;wBAAU,QAAQ;oBAA0E;oBAC7I;wBAAE,MAAM;wBAA2B,QAAQ;wBAAoB,QAAQ;oBAAiD;oBACxH;wBAAE,MAAM;wBAA2B,QAAQ;wBAAoB,QAAQ;oBAA6B;oBACpG;wBAAE,MAAM;wBAAqB,QAAQ;wBAAmB,QAAQ;oBAAuC;iBACxG;YACH;YACA;gBACE,UAAU;gBACV,WAAW;oBACT;wBAAE,MAAM;wBAA4B,QAAQ;wBAAmB,QAAQ;oBAAwD;oBAC/H;wBAAE,MAAM;wBAAmB,QAAQ;wBAAoB,QAAQ;oBAA4C;oBAC3G;wBAAE,MAAM;wBAA0C,QAAQ;wBAAmB,QAAQ;oBAA4B;oBACjH;wBAAE,MAAM;wBAAmC,QAAQ;wBAAmB,QAAQ;oBAA2C;iBAC1H;YACH;YACA;gBACE,UAAU;gBACV,WAAW;oBACT;wBAAE,MAAM;wBAAuB,QAAQ;wBAAoB,QAAQ;oBAAsC;oBACzG;wBAAE,MAAM;wBAA6B,QAAQ;wBAAoB,QAAQ;oBAAoD;oBAC7H;wBAAE,MAAM;wBAAiD,QAAQ;wBAAmB,QAAQ;oBAAuC;oBACnI;wBAAE,MAAM;wBAAU,QAAQ;wBAAQ,QAAQ;oBAA8B;iBACzE;YACH;SACD;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,QAAQ,WAAW,QAAQ,OAAO;QACjD,IAAI,WAAW,UAAU,WAAW,mBAAmB,OAAO;QAC9D,IAAI,WAAW,SAAS,WAAW,oBAAoB,OAAO;QAC9D,IAAI,WAAW,SAAS,WAAW,UAAU,OAAO;QACpD,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAO,cAAW;kCAC/B,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;wCAAE,WAAU;kDACjC,OAAO,OAAO;;;;;;;;;;;8CAGnB,8OAAC;8CAAG,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CACpC,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;wCAAE,WAAU;kDACrC,OAAO,SAAS;;;;;;;;;;;8CAGrB,8OAAC;8CAAG,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CACpC,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,OAAO,gBAAgB,CAAC;wCAAE,WAAU;kDACjD,OAAO,SAAS;;;;;;;;;;;8CAGrB,8OAAC;8CAAG,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CACpC,8OAAC;8CACC,cAAA,8OAAC;wCAAK,WAAU;kDACb,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CACX,OAAO,YAAY;;;;;;0CAEtB,8OAAC;gCAAE,WAAU;0CACV,OACG,gCACA;;;;;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,OAAO,aAAa;;;;;;0CAEvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAqC,OAAO,OAAO;;;;;;0DAClE,8OAAC;gDAAI,WAAU;0DAA0B,OAAO,WAAW;;;;;;;;;;;;kDAE7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAoC,OAAO,SAAS;;;;;;0DACnE,8OAAC;gDAAI,WAAU;0DAAyB,OAAO,cAAc;;;;;;;;;;;;kDAE/D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC,OAAO,QAAQ;;;;;;0DACpE,8OAAC;gDAAI,WAAU;0DAA2B,OAAO,gBAAgB;;;;;;;;;;;;kDAEnE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmC,OAAO,QAAQ;;;;;;0DACjE,8OAAC;gDAAI,WAAU;0DAAwB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;kCAM7D,8OAAC;wBAAI,WAAU;kCACZ,aAAa,CAAC,OAAsB,CAAC,GAAG,CAAC,CAAC,UAAU,sBACnD,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAG,WAAU;kDACX,SAAS,QAAQ;;;;;;kDAEpB,8OAAC;wCAAI,WAAU;kDACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,yBACjC,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA+B,SAAS,IAAI;;;;;;0EAC1D,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,SAAS,MAAM,GAAG;0EAC7F,SAAS,MAAM;;;;;;;;;;;;kEAGpB,8OAAC;wDAAE,WAAU;kEAAyB,SAAS,MAAM;;;;;;;+CAP7C;;;;;;;;;;;+BANN;;;;;;;;;;kCAsBd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,OAAO,cAAc;;;;;;0CAExB,8OAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;uCAVH,IAAI,EAAE;;;;;;;;;;;;;;;;kCAuBtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,OAAO,WAAW;;;;;;0CAErB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;4CACpC,OACG,iCACA;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;4CACpC,OACG,8BACA;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;4CACpC,OACG,+BACA;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;4CACpC,OACG,gCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,OAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;wCACVC,IAAAA;oCAAAA,CAAMZ,UAAUa;oCAAAA,CAAQ;gDACxBC,IAAAA;4CAAAA,CAAM;4CAAA;iDACNC,UAAU;sDACV,IAAA,CAAA;gDAAA,QAAA;oDAAA,IAAA,iBAA2C;oDAAA;iDAAA;;+CAC3CC,YAAY;;yCACZC,UAAU;8CACVC,IAAAA,CAAAA;oCAAAA,CAAU;iCAAA,CAAE;;6BACd;kCACAC,QAAAA,CAAU,CAAA;4BAAA;yBAAA;;yBACRC,YAAYnB;0BACd,QAAA,CAAA;oBAAA;iBAAA;YACF;YAAE", "ignoreList": [0], "debugId": null}}]}