{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\ninterface HomePageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: HomePageProps): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  return {\n    title: isZh ? 'G6PD缺乏症（蚕豆病）FAQ - 专业医疗指导和用药安全' : 'G6PD Deficiency (Favism) FAQ - Professional Medical Guidance and Medication Safety',\n    description: isZh\n      ? '专业的G6PD缺乏症（蚕豆病）医疗指导网站。提供用药禁忌、症状识别、饮食指导、治疗方案等全面信息。基于长尾关键词优化，帮助患者安全用药，避免溶血危机。'\n      : 'Professional G6PD deficiency (favism) medical guidance website. Comprehensive information on medication contraindications, symptom recognition, dietary guidance, and treatment options. Optimized for long-tail keywords to help patients use medications safely and avoid hemolytic crises.',\n    keywords: isZh\n      ? ['G6PD缺乏症', '蚕豆病', '用药禁忌', '中药安全', '口服液', '症状识别', '饮食指导', '治疗方案', '溶血性贫血', '遗传病']\n      : ['G6PD deficiency', 'favism', 'medication contraindications', 'Chinese medicine safety', 'oral solutions', 'symptom recognition', 'dietary guidance', 'treatment options', 'hemolytic anemia', 'genetic disorder'],\n    openGraph: {\n      title: isZh ? 'G6PD缺乏症（蚕豆病）FAQ - 专业医疗指导' : 'G6PD Deficiency FAQ - Professional Medical Guidance',\n      description: isZh\n        ? '专业的G6PD缺乏症医疗指导，包含用药安全、症状识别、饮食建议等全面信息'\n        : 'Professional G6PD deficiency medical guidance with comprehensive medication safety, symptom recognition, and dietary advice',\n      type: 'website',\n      locale: locale,\n      alternateLocale: locale === 'zh' ? 'en' : 'zh',\n    },\n    alternates: {\n      canonical: `/${locale}`,\n      languages: {\n        'zh': '/zh',\n        'en': '/en',\n      },\n    },\n  }\n}\n\nexport default async function HomePage({ params }: HomePageProps) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6 text-gray-900\">\n              {isZh ? 'G6PD缺乏症（蚕豆病）' : 'G6PD Deficiency (Favism)'}\n              <span className=\"block text-blue-600\">\n                {isZh ? '专业指导中心' : 'Professional Guide Center'}\n              </span>\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-gray-600\">\n              {isZh\n                ? '为患者及家属提供权威、全面的医疗信息和生活指导，包括用药安全、饮食建议、症状识别等专业内容'\n                : 'Providing authoritative and comprehensive medical information and lifestyle guidance for patients and families, including medication safety, dietary advice, symptom recognition and more'\n              }\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href={`/${locale}/faq`}\n                className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n              >\n                {isZh ? '浏览常见问题' : 'Browse FAQ'}\n              </a>\n              <a\n                href={`/${locale}/medications`}\n                className=\"bg-white text-blue-600 border-2 border-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\"\n              >\n                {isZh ? '用药指导' : 'Medication Guide'}\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Access Cards */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          <div className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\">\n            <div className=\"text-red-600 mb-4\">\n              <svg className=\"w-12 h-12\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold mb-3 text-gray-900\">\n              {isZh ? '中药禁忌' : 'Chinese Medicine Contraindications'}\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              {isZh\n                ? '了解G6PD缺乏症患者不能使用的中药成分和药物，避免溶血性贫血发作'\n                : 'Learn about Chinese medicine ingredients and drugs that G6PD deficient patients cannot use to avoid hemolytic anemia attacks'\n              }\n            </p>\n            <a\n              href={`/${locale}/faq/medications/chinese-medicine`}\n              className=\"text-blue-600 font-medium hover:text-blue-800\"\n            >\n              {isZh ? '查看详情 →' : 'View Details →'}\n            </a>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\">\n            <div className=\"text-orange-600 mb-4\">\n              <svg className=\"w-12 h-12\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold mb-3 text-gray-900\">\n              {isZh ? '口服液安全' : 'Oral Solution Safety'}\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              {isZh\n                ? '查询各种口服液药物的安全性，包括儿童常用的感冒药、退烧药等'\n                : 'Check the safety of various oral solution medications, including common cold and fever medicines for children'\n              }\n            </p>\n            <a\n              href={`/${locale}/faq/medications/oral-solutions`}\n              className=\"text-blue-600 font-medium hover:text-blue-800\"\n            >\n              {isZh ? '查看详情 →' : 'View Details →'}\n            </a>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\">\n            <div className=\"text-green-600 mb-4\">\n              <svg className=\"w-12 h-12\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold mb-3 text-gray-900\">\n              {isZh ? '症状识别' : 'Symptom Recognition'}\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              {isZh\n                ? '学习识别G6PD缺乏症的症状表现，了解何时需要紧急就医'\n                : 'Learn to recognize symptoms of G6PD deficiency and understand when emergency medical care is needed'\n              }\n            </p>\n            <a\n              href={`/${locale}/faq/symptoms`}\n              className=\"text-blue-600 font-medium hover:text-blue-800\"\n            >\n              {isZh ? '查看详情 →' : 'View Details →'}\n            </a>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Section */}\n      <div className=\"bg-blue-600 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold mb-4\">\n              {isZh ? '关于G6PD缺乏症' : 'About G6PD Deficiency'}\n            </h2>\n            <p className=\"text-xl text-blue-100\">\n              {isZh\n                ? '了解这种常见的遗传性疾病的基本信息'\n                : 'Learn basic information about this common genetic disorder'\n              }\n            </p>\n          </div>\n          <div className=\"grid md:grid-cols-3 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">4-5%</div>\n              <div className=\"text-blue-100\">\n                {isZh ? '全球患病率' : 'Global Prevalence'}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">400M+</div>\n              <div className=\"text-blue-100\">\n                {isZh ? '全球患者数量' : 'Global Patient Count'}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">X-linked</div>\n              <div className=\"text-blue-100\">\n                {isZh ? '遗传方式' : 'Inheritance Pattern'}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAiB;IAC9D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,OAAO;QACL,OAAO,OAAO,kCAAkC;QAChD,aAAa,OACT,gFACA;QACJ,UAAU,OACN;YAAC;YAAW;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAQ;YAAS;SAAM,GACjF;YAAC;YAAmB;YAAU;YAAgC;YAA2B;YAAkB;YAAuB;YAAoB;YAAqB;YAAoB;SAAmB;QACtN,WAAW;YACT,OAAO,OAAO,6BAA6B;YAC3C,aAAa,OACT,yCACA;YACJ,MAAM;YACN,QAAQ;YACR,iBAAiB,WAAW,OAAO,OAAO;QAC5C;QACA,YAAY;YACV,WAAW,CAAC,CAAC,EAAE,QAAQ;YACvB,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF;AAEe,eAAe,SAAS,EAAE,MAAM,EAAiB;IAC9D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCACX,OAAO,iBAAiB;kDACzB,8OAAC;wCAAK,WAAU;kDACb,OAAO,WAAW;;;;;;;;;;;;0CAGvB,8OAAC;gCAAE,WAAU;0CACV,OACG,kDACA;;;;;;0CAGN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;wCACtB,WAAU;kDAET,OAAO,WAAW;;;;;;kDAErB,8OAAC;wCACC,MAAM,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC;wCAC9B,WAAU;kDAET,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAY,MAAK;wCAAe,SAAQ;kDACrD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoN,UAAS;;;;;;;;;;;;;;;;8CAG5P,8OAAC;oCAAG,WAAU;8CACX,OAAO,SAAS;;;;;;8CAEnB,8OAAC;oCAAE,WAAU;8CACV,OACG,sCACA;;;;;;8CAGN,8OAAC;oCACC,MAAM,CAAC,CAAC,EAAE,OAAO,iCAAiC,CAAC;oCACnD,WAAU;8CAET,OAAO,WAAW;;;;;;;;;;;;sCAIvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAY,MAAK;wCAAe,SAAQ;kDACrD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAG,WAAU;8CACX,OAAO,UAAU;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CACV,OACG,kCACA;;;;;;8CAGN,8OAAC;oCACC,MAAM,CAAC,CAAC,EAAE,OAAO,+BAA+B,CAAC;oCACjD,WAAU;8CAET,OAAO,WAAW;;;;;;;;;;;;sCAIvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAY,MAAK;wCAAe,SAAQ;kDACrD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAmI,UAAS;;;;;;;;;;;;;;;;8CAG3K,8OAAC;oCAAG,WAAU;8CACX,OAAO,SAAS;;;;;;8CAEnB,8OAAC;oCAAE,WAAU;8CACV,OACG,gCACA;;;;;;8CAGN,8OAAC;oCACC,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC;oCAC/B,WAAU;8CAET,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,OAAO,cAAc;;;;;;8CAExB,8OAAC;oCAAE,WAAU;8CACV,OACG,sBACA;;;;;;;;;;;;sCAIR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,UAAU;;;;;;;;;;;;8CAGtB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,WAAW;;;;;;;;;;;;8CAGvB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,EAAc,IAAIX,mBAAmB;qBAChDY,YAAY;8BACVC,IAAAA,CAAMZ,CAAAA;wBAAAA,KAAUa,GAAAA;4BAAAA,IAAQ;4BAAA;yBAAA;;uBACxBC,MAAM;;iBACNC,UAAU;sBACV,IAAA,CAAA;gBAAA,UAAA;oBAAA,IAAA,eAA2C;oBAAA;iBAAA;;eAC3CC,YAAY;;SACZC,UAAU;cACVC,IAAAA;YAAAA,CAAU,EAAE,GAAA;gBACd,OAAA,QAAA;wBAAA;4BACAC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,CAAU,qBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACRC,OAAAA,GAAAA,6SAAAA,CAAAA,EAAYnB,QAAAA,CAAAA,KAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,MAAAA,EAAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACA;qBAAA", "ignoreList": [0], "debugId": null}}]}