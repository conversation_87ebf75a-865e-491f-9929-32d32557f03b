{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/lib/faq-data.ts"], "sourcesContent": ["export interface FAQItem {\n  id: string\n  question: string\n  answer: string\n  category: string\n  subcategory?: string\n  keywords: string[]\n  searchVolume?: number\n  locale: 'zh' | 'en'\n}\n\nexport interface FAQCategory {\n  id: string\n  name: string\n  description: string\n  icon: string\n  subcategories?: FAQSubcategory[]\n}\n\nexport interface FAQSubcategory {\n  id: string\n  name: string\n  description: string\n}\n\n// FAQ Categories\nexport const faqCategories: Record<'zh' | 'en', FAQCategory[]> = {\n  zh: [\n    {\n      id: 'medications',\n      name: '用药安全',\n      description: '了解G6PD缺乏症患者的用药禁忌和安全指导',\n      icon: '💊',\n      subcategories: [\n        {\n          id: 'chinese-medicine',\n          name: '中药禁忌',\n          description: '不能使用的中药成分和药物'\n        },\n        {\n          id: 'oral-solutions',\n          name: '口服液安全',\n          description: '各种口服液药物的安全性评估'\n        },\n        {\n          id: 'western-medicine',\n          name: '西药指导',\n          description: '西药使用注意事项'\n        }\n      ]\n    },\n    {\n      id: 'symptoms',\n      name: '症状识别',\n      description: '学习识别G6PD缺乏症的症状和紧急情况',\n      icon: '🩺'\n    },\n    {\n      id: 'diet',\n      name: '饮食指导',\n      description: '安全的饮食建议和食物禁忌',\n      icon: '🥗'\n    },\n    {\n      id: 'treatment',\n      name: '治疗方案',\n      description: '治疗选择和医疗管理',\n      icon: '🏥'\n    }\n  ],\n  en: [\n    {\n      id: 'medications',\n      name: 'Medication Safety',\n      description: 'Learn about medication contraindications and safety guidelines for G6PD deficiency patients',\n      icon: '💊',\n      subcategories: [\n        {\n          id: 'chinese-medicine',\n          name: 'Chinese Medicine',\n          description: 'Chinese medicine ingredients and drugs to avoid'\n        },\n        {\n          id: 'oral-solutions',\n          name: 'Oral Solutions',\n          description: 'Safety assessment of various oral solution medications'\n        },\n        {\n          id: 'western-medicine',\n          name: 'Western Medicine',\n          description: 'Western medicine usage precautions'\n        }\n      ]\n    },\n    {\n      id: 'symptoms',\n      name: 'Symptom Recognition',\n      description: 'Learn to recognize G6PD deficiency symptoms and emergency situations',\n      icon: '🩺'\n    },\n    {\n      id: 'diet',\n      name: 'Diet Guide',\n      description: 'Safe dietary recommendations and food restrictions',\n      icon: '🥗'\n    },\n    {\n      id: 'treatment',\n      name: 'Treatment Options',\n      description: 'Treatment choices and medical management',\n      icon: '🏥'\n    }\n  ]\n}\n\n// Sample FAQ data based on long-tail keywords\nexport const faqData: FAQItem[] = [\n  // Chinese Medicine FAQs (based on 蚕豆病中药长尾词.md)\n  {\n    id: 'zh-chinese-medicine-1',\n    question: '蚕豆病不能吃的中药有哪些？',\n    answer: 'G6PD缺乏症（蚕豆病）患者需要避免以下中药成分：薄荷、金银花、黄连、大黄、麻黄、茵陈等。这些中药可能引起溶血性贫血发作。使用任何中药前，请务必咨询医生并告知您的G6PD缺乏症状况。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '中药', '禁忌', '不能吃'],\n    searchVolume: 472000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-chinese-medicine-2',\n    question: '蚕豆病哪些中药不能吃？',\n    answer: '蚕豆病患者应避免使用含有以下成分的中药：1. 薄荷类：薄荷、薄荷脑等；2. 清热解毒类：金银花、黄连、黄芩等；3. 泻下类：大黄、芒硝等；4. 发散风寒类：麻黄、桂枝等。建议在使用任何中药前咨询专业中医师。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '哪些中药', '不能吃'],\n    searchVolume: 235000,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-chinese-medicine-3',\n    question: '蚕豆病能吃的中药有哪些？',\n    answer: '蚕豆病患者可以安全使用的中药包括：1. 补益类：人参、党参、黄芪、当归等；2. 健脾类：白术、茯苓、山药等；3. 养血类：熟地黄、白芍、阿胶等。但即使是安全的中药，也建议在医生指导下使用，并密切观察身体反应。',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['蚕豆病', '能吃', '中药'],\n    searchVolume: 169000,\n    locale: 'zh'\n  },\n  \n  // Oral Solutions FAQs (based on 蚕豆病口服液长尾词.md)\n  {\n    id: 'zh-oral-solutions-1',\n    question: '蚕豆病能吃双黄连口服液吗？',\n    answer: '蚕豆病患者不建议使用双黄连口服液。双黄连口服液含有金银花、黄芩、连翘等成分，其中金银花和黄芩可能引起G6PD缺乏症患者发生溶血反应。如需治疗感冒等症状，请咨询医生选择安全的替代药物。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '双黄连口服液'],\n    searchVolume: 24200,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-oral-solutions-2',\n    question: '蚕豆病可以吃抗病毒口服液吗？',\n    answer: '蚕豆病患者使用抗病毒口服液需要谨慎。不同品牌的抗病毒口服液成分不同，有些含有板蓝根、金银花等可能引起溶血的成分。建议：1. 使用前仔细查看成分表；2. 咨询医生或药师；3. 选择不含禁忌成分的产品；4. 使用后密切观察身体反应。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '抗病毒口服液'],\n    searchVolume: 18700,\n    locale: 'zh'\n  },\n  {\n    id: 'zh-oral-solutions-3',\n    question: '蚕豆病能吃保济口服液吗？',\n    answer: '蚕豆病患者可以谨慎使用保济口服液。保济口服液主要含有钩藤、薄荷、菊花等成分，其中薄荷可能对部分G6PD缺乏症患者有影响。建议：1. 首次使用时小剂量试用；2. 密切观察是否出现乏力、面色苍白等症状；3. 如有不适立即停用并就医。',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['蚕豆病', '保济口服液'],\n    searchVolume: 1440,\n    locale: 'zh'\n  },\n\n  // Symptoms FAQs\n  {\n    id: 'zh-symptoms-1',\n    question: '蚕豆病发作有什么症状？',\n    answer: 'G6PD缺乏症急性发作的主要症状包括：1. 急性溶血症状：面色苍白、乏力、头晕；2. 黄疸：皮肤和眼白发黄；3. 尿液改变：尿色加深，呈茶色或酱油色；4. 其他症状：发热、恶心、呕吐、腹痛。如出现这些症状，应立即停用可疑药物并紧急就医。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '发作', '症状'],\n    locale: 'zh'\n  },\n  {\n    id: 'zh-symptoms-2',\n    question: '蚕豆病黄疸多久能好？',\n    answer: '蚕豆病引起的黄疸恢复时间因人而异，通常需要1-2周。恢复时间取决于：1. 溶血程度：轻度溶血3-7天，重度溶血可能需要2-3周；2. 治疗及时性：早期治疗恢复更快；3. 个体差异：年龄、体质等因素影响恢复速度。期间需要充分休息、多饮水，避免再次接触诱发因素。',\n    category: 'symptoms',\n    keywords: ['蚕豆病', '黄疸', '多久', '恢复'],\n    locale: 'zh'\n  }\n]\n\n// English FAQ data\nexport const englishFaqData: FAQItem[] = [\n  {\n    id: 'en-chinese-medicine-1',\n    question: 'What Chinese medicines should G6PD deficiency patients avoid?',\n    answer: 'G6PD deficiency patients should avoid Chinese medicines containing: mint, honeysuckle, coptis, rhubarb, ephedra, and artemisia. These ingredients may trigger hemolytic anemia attacks. Always consult a doctor before using any Chinese medicine and inform them of your G6PD deficiency status.',\n    category: 'medications',\n    subcategory: 'chinese-medicine',\n    keywords: ['G6PD deficiency', 'Chinese medicine', 'contraindications', 'avoid'],\n    locale: 'en'\n  },\n  {\n    id: 'en-oral-solutions-1',\n    question: 'Can G6PD deficiency patients take Shuanghuanglian oral solution?',\n    answer: 'G6PD deficiency patients are not recommended to use Shuanghuanglian oral solution. It contains honeysuckle, scutellaria, and forsythia, where honeysuckle and scutellaria may cause hemolytic reactions in G6PD deficient patients. For cold treatment, please consult a doctor for safe alternative medications.',\n    category: 'medications',\n    subcategory: 'oral-solutions',\n    keywords: ['G6PD deficiency', 'Shuanghuanglian', 'oral solution'],\n    locale: 'en'\n  },\n  {\n    id: 'en-symptoms-1',\n    question: 'What are the symptoms of G6PD deficiency crisis?',\n    answer: 'Main symptoms of acute G6PD deficiency crisis include: 1. Acute hemolysis: pallor, fatigue, dizziness; 2. Jaundice: yellowing of skin and eyes; 3. Urine changes: dark urine (tea or cola-colored); 4. Other symptoms: fever, nausea, vomiting, abdominal pain. If these symptoms occur, immediately stop the suspected medication and seek emergency medical care.',\n    category: 'symptoms',\n    keywords: ['G6PD deficiency', 'crisis', 'symptoms'],\n    locale: 'en'\n  }\n]\n\n// Combine all FAQ data\nexport const allFaqData = [...faqData, ...englishFaqData]\n\n// Helper functions\nexport function getFaqsByCategory(category: string, locale: 'zh' | 'en'): FAQItem[] {\n  return allFaqData.filter(faq => faq.category === category && faq.locale === locale)\n}\n\nexport function getFaqsBySubcategory(category: string, subcategory: string, locale: 'zh' | 'en'): FAQItem[] {\n  return allFaqData.filter(faq => \n    faq.category === category && \n    faq.subcategory === subcategory && \n    faq.locale === locale\n  )\n}\n\nexport function searchFaqs(query: string, locale: 'zh' | 'en'): FAQItem[] {\n  const lowercaseQuery = query.toLowerCase()\n  return allFaqData.filter(faq => \n    faq.locale === locale && (\n      faq.question.toLowerCase().includes(lowercaseQuery) ||\n      faq.answer.toLowerCase().includes(lowercaseQuery) ||\n      faq.keywords.some(keyword => keyword.toLowerCase().includes(lowercaseQuery))\n    )\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;AA0BO,MAAM,gBAAoD;IAC/D,IAAI;QACF;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;KACD;IACD,IAAI;QACF;YACE,IAAI;YAC<PERSON>,MAAM;YAC<PERSON>,aAAa;YACb,MAAM;YAC<PERSON>,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;QACR;KACD;AACH;AAGO,MAAM,UAAqB;IAChC,+CAA+C;IAC/C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAM;YAAM;SAAM;QACpC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAQ;SAAM;QAChC,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;YAAM;SAAK;QAC7B,cAAc;QACd,QAAQ;IACV;IAEA,8CAA8C;IAC9C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAS;QAC3B,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAS;QAC3B,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAO;SAAQ;QAC1B,cAAc;QACd,QAAQ;IACV;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;SAAK;QAC7B,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAO;YAAM;YAAM;SAAK;QACnC,QAAQ;IACV;CACD;AAGM,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAmB;YAAoB;YAAqB;SAAQ;QAC/E,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAmB;YAAmB;SAAgB;QACjE,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;YAAC;YAAmB;YAAU;SAAW;QACnD,QAAQ;IACV;CACD;AAGM,MAAM,aAAa;OAAI;OAAY;CAAe;AAGlD,SAAS,kBAAkB,QAAgB,EAAE,MAAmB;IACrE,OAAO,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,YAAY,IAAI,MAAM,KAAK;AAC9E;AAEO,SAAS,qBAAqB,QAAgB,EAAE,WAAmB,EAAE,MAAmB;IAC7F,OAAO,WAAW,MAAM,CAAC,CAAA,MACvB,IAAI,QAAQ,KAAK,YACjB,IAAI,WAAW,KAAK,eACpB,IAAI,MAAM,KAAK;AAEnB;AAEO,SAAS,WAAW,KAAa,EAAE,MAAmB;IAC3D,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,WAAW,MAAM,CAAC,CAAA,MACvB,IAAI,MAAM,KAAK,UAAU,CACvB,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACpC,IAAI,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,gBAC9D;AAEJ", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/faq/medications/chinese-medicine/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { getFaqsBySubcategory } from '@/lib/faq-data'\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  const title = isZh ? '中药禁忌 - G6PD缺乏症（蚕豆病）' : 'Chinese Medicine Contraindications - G6PD Deficiency'\n  const description = isZh \n    ? 'G6PD缺乏症患者不能使用的中药成分和药物详细列表，避免溶血性贫血发作'\n    : 'Detailed list of Chinese medicine ingredients and drugs that G6PD deficiency patients cannot use to avoid hemolytic anemia attacks'\n\n  return {\n    title,\n    description,\n    openGraph: {\n      title,\n      description,\n      type: 'website',\n    },\n  }\n}\n\nexport default async function ChineseMedicinePage({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  const faqs = getFaqsBySubcategory('medications', 'chinese-medicine', locale as 'zh' | 'en')\n\n  // Prohibited Chinese medicines data\n  const prohibitedMedicines = {\n    zh: [\n      {\n        category: '清热解毒类',\n        medicines: ['薄荷', '金银花', '黄连', '黄芩', '板蓝根', '大青叶', '蒲公英'],\n        risk: '高风险'\n      },\n      {\n        category: '泻下类',\n        medicines: ['大黄', '芒硝', '番泻叶'],\n        risk: '高风险'\n      },\n      {\n        category: '发散风寒类',\n        medicines: ['麻黄', '桂枝', '紫苏叶'],\n        risk: '中等风险'\n      },\n      {\n        category: '利水渗湿类',\n        medicines: ['茵陈', '车前子', '泽泻'],\n        risk: '中等风险'\n      }\n    ],\n    en: [\n      {\n        category: 'Heat-clearing and Detoxifying',\n        medicines: ['Mint', 'Honeysuckle', 'Coptis', 'Scutellaria', 'Isatis Root', 'Isatis Leaf', 'Dandelion'],\n        risk: 'High Risk'\n      },\n      {\n        category: 'Purgative',\n        medicines: ['Rhubarb', 'Mirabilite', 'Senna Leaf'],\n        risk: 'High Risk'\n      },\n      {\n        category: 'Wind-Cold Dispersing',\n        medicines: ['Ephedra', 'Cinnamon Twig', 'Perilla Leaf'],\n        risk: 'Moderate Risk'\n      },\n      {\n        category: 'Water-promoting and Dampness-permeating',\n        medicines: ['Artemisia', 'Plantain Seed', 'Alisma'],\n        risk: 'Moderate Risk'\n      }\n    ]\n  }\n\n  const safeMedicines = {\n    zh: [\n      {\n        category: '补益类',\n        medicines: ['人参', '党参', '黄芪', '当归', '熟地黄', '白芍', '阿胶']\n      },\n      {\n        category: '健脾类',\n        medicines: ['白术', '茯苓', '山药', '陈皮', '半夏']\n      },\n      {\n        category: '安神类',\n        medicines: ['酸枣仁', '龙骨', '牡蛎', '远志']\n      }\n    ],\n    en: [\n      {\n        category: 'Tonifying',\n        medicines: ['Ginseng', 'Codonopsis', 'Astragalus', 'Angelica', 'Prepared Rehmannia', 'White Peony', 'Donkey-hide Gelatin']\n      },\n      {\n        category: 'Spleen-strengthening',\n        medicines: ['Atractylodes', 'Poria', 'Chinese Yam', 'Tangerine Peel', 'Pinellia']\n      },\n      {\n        category: 'Spirit-calming',\n        medicines: ['Jujube Seed', 'Dragon Bone', 'Oyster Shell', 'Polygala']\n      }\n    ]\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Breadcrumb */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <nav className=\"flex\" aria-label=\"Breadcrumb\">\n            <ol className=\"flex items-center space-x-4\">\n              <li>\n                <Link href={`/${locale}`} className=\"text-gray-500 hover:text-gray-700\">\n                  {isZh ? '首页' : 'Home'}\n                </Link>\n              </li>\n              <li><span className=\"text-gray-400\">/</span></li>\n              <li>\n                <Link href={`/${locale}/faq`} className=\"text-gray-500 hover:text-gray-700\">\n                  {isZh ? '常见问题' : 'FAQ'}\n                </Link>\n              </li>\n              <li><span className=\"text-gray-400\">/</span></li>\n              <li>\n                <Link href={`/${locale}/faq/medications`} className=\"text-gray-500 hover:text-gray-700\">\n                  {isZh ? '用药安全' : 'Medication Safety'}\n                </Link>\n              </li>\n              <li><span className=\"text-gray-400\">/</span></li>\n              <li>\n                <span className=\"text-gray-900 font-medium\">\n                  {isZh ? '中药禁忌' : 'Chinese Medicine'}\n                </span>\n              </li>\n            </ol>\n          </nav>\n        </div>\n      </div>\n\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-red-600 to-red-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <div className=\"text-6xl mb-4\">🚫</div>\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n              {isZh ? '中药禁忌指南' : 'Chinese Medicine Contraindications Guide'}\n            </h1>\n            <p className=\"text-xl text-red-100 max-w-3xl mx-auto\">\n              {isZh \n                ? 'G6PD缺乏症患者必须了解的中药使用禁忌，避免溶血性贫血发作'\n                : 'Essential Chinese medicine contraindications for G6PD deficiency patients to prevent hemolytic anemia attacks'\n              }\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        {/* Prohibited Medicines */}\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-8 mb-12\">\n          <h2 className=\"text-2xl font-bold mb-6 text-red-800 flex items-center\">\n            <svg className=\"w-8 h-8 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z\" clipRule=\"evenodd\" />\n            </svg>\n            {isZh ? '禁用中药列表' : 'Prohibited Chinese Medicines'}\n          </h2>\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {prohibitedMedicines[locale as 'zh' | 'en'].map((category, index) => (\n              <div key={index} className=\"bg-white rounded-lg p-6 border border-red-200\">\n                <h3 className=\"text-lg font-semibold mb-3 text-red-800\">\n                  {category.category}\n                </h3>\n                <div className=\"mb-3\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    category.risk === '高风险' || category.risk === 'High Risk' \n                      ? 'bg-red-100 text-red-800' \n                      : 'bg-orange-100 text-orange-800'\n                  }`}>\n                    {category.risk}\n                  </span>\n                </div>\n                <div className=\"flex flex-wrap gap-2\">\n                  {category.medicines.map((medicine) => (\n                    <span\n                      key={medicine}\n                      className=\"px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full\"\n                    >\n                      {medicine}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Safe Medicines */}\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-8 mb-12\">\n          <h2 className=\"text-2xl font-bold mb-6 text-green-800 flex items-center\">\n            <svg className=\"w-8 h-8 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            {isZh ? '相对安全的中药' : 'Relatively Safe Chinese Medicines'}\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            {safeMedicines[locale as 'zh' | 'en'].map((category, index) => (\n              <div key={index} className=\"bg-white rounded-lg p-6 border border-green-200\">\n                <h3 className=\"text-lg font-semibold mb-3 text-green-800\">\n                  {category.category}\n                </h3>\n                <div className=\"flex flex-wrap gap-2\">\n                  {category.medicines.map((medicine) => (\n                    <span\n                      key={medicine}\n                      className=\"px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full\"\n                    >\n                      {medicine}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* FAQ Section */}\n        <div className=\"bg-white rounded-lg shadow-md p-8 mb-12\">\n          <h2 className=\"text-2xl font-bold mb-6 text-gray-900\">\n            {isZh ? '中药使用常见问题' : 'Common Questions About Chinese Medicine Use'}\n          </h2>\n          <div className=\"space-y-6\">\n            {faqs.map((faq) => (\n              <div key={faq.id} className=\"border-b border-gray-200 pb-6 last:border-b-0\">\n                <h3 className=\"text-lg font-semibold mb-3 text-gray-900\">\n                  {faq.question}\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed mb-3\">\n                  {faq.answer}\n                </p>\n                <div className=\"flex flex-wrap gap-2\">\n                  {faq.keywords.map((keyword) => (\n                    <span\n                      key={keyword}\n                      className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\"\n                    >\n                      {keyword}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Emergency Warning */}\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n          <div className=\"flex items-start\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"w-6 h-6 text-yellow-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-lg font-semibold text-yellow-800 mb-2\">\n                {isZh ? '紧急情况处理' : 'Emergency Situation Management'}\n              </h3>\n              <p className=\"text-yellow-700\">\n                {isZh \n                  ? '如果误用禁忌中药后出现面色苍白、乏力、尿色加深、黄疸等症状，请立即停用药物并紧急就医。告知医生您的G6PD缺乏症状况和所使用的中药。'\n                  : 'If you experience pallor, fatigue, dark urine, jaundice or other symptoms after mistakenly using contraindicated Chinese medicine, immediately stop the medication and seek emergency medical care. Inform the doctor of your G6PD deficiency status and the Chinese medicine used.'\n                }\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,MAAM,QAAQ,OAAO,wBAAwB;IAC7C,MAAM,cAAc,OAChB,wCACA;IAEJ,OAAO;QACL;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;QACR;IACF;AACF;AAEe,eAAe,oBAAoB,EAChD,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,MAAM,OAAO,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe,oBAAoB;IAErE,oCAAoC;IACpC,MAAM,sBAAsB;QAC1B,IAAI;YACF;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAM;oBAAO;oBAAM;oBAAM;oBAAO;oBAAO;iBAAM;gBACzD,MAAM;YACR;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAM;oBAAM;iBAAM;gBAC9B,MAAM;YACR;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAM;oBAAM;iBAAM;gBAC9B,MAAM;YACR;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAM;oBAAO;iBAAK;gBAC9B,MAAM;YACR;SACD;QACD,IAAI;YACF;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAQ;oBAAe;oBAAU;oBAAe;oBAAe;oBAAe;iBAAY;gBACtG,MAAM;YACR;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAW;oBAAc;iBAAa;gBAClD,MAAM;YACR;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAW;oBAAiB;iBAAe;gBACvD,MAAM;YACR;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAa;oBAAiB;iBAAS;gBACnD,MAAM;YACR;SACD;IACH;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAO;oBAAM;iBAAK;YACxD;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;YAC3C;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAO;oBAAM;oBAAM;iBAAK;YACtC;SACD;QACD,IAAI;YACF;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAW;oBAAc;oBAAc;oBAAY;oBAAsB;oBAAe;iBAAsB;YAC5H;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAgB;oBAAS;oBAAe;oBAAkB;iBAAW;YACnF;YACA;gBACE,UAAU;gBACV,WAAW;oBAAC;oBAAe;oBAAe;oBAAgB;iBAAW;YACvE;SACD;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAO,cAAW;kCAC/B,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;wCAAE,WAAU;kDACjC,OAAO,OAAO;;;;;;;;;;;8CAGnB,8OAAC;8CAAG,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CACpC,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;wCAAE,WAAU;kDACrC,OAAO,SAAS;;;;;;;;;;;8CAGrB,8OAAC;8CAAG,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CACpC,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,OAAO,gBAAgB,CAAC;wCAAE,WAAU;kDACjD,OAAO,SAAS;;;;;;;;;;;8CAGrB,8OAAC;8CAAG,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CACpC,8OAAC;8CACC,cAAA,8OAAC;wCAAK,WAAU;kDACb,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CACX,OAAO,WAAW;;;;;;0CAErB,8OAAC;gCAAE,WAAU;0CACV,OACG,mCACA;;;;;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA8H,UAAS;;;;;;;;;;;oCAEnK,OAAO,WAAW;;;;;;;0CAErB,8OAAC;gCAAI,WAAU;0CACZ,mBAAmB,CAAC,OAAsB,CAAC,GAAG,CAAC,CAAC,UAAU,sBACzD,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAG,WAAU;0DACX,SAAS,QAAQ;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,IAAI,KAAK,SAAS,SAAS,IAAI,KAAK,cACzC,4BACA,iCACJ;8DACC,SAAS,IAAI;;;;;;;;;;;0DAGlB,8OAAC;gDAAI,WAAU;0DACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,yBACvB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;uCAhBH;;;;;;;;;;;;;;;;kCA6BhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwI,UAAS;;;;;;;;;;;oCAE7K,OAAO,YAAY;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;0CACZ,aAAa,CAAC,OAAsB,CAAC,GAAG,CAAC,CAAC,UAAU,sBACnD,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAG,WAAU;0DACX,SAAS,QAAQ;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,yBACvB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;uCAPH;;;;;;;;;;;;;;;;kCAoBhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,OAAO,aAAa;;;;;;0CAEvB,8OAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;uCAVH,IAAI,EAAE;;;;;;;;;;;;;;;;kCAuBtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAe,SAAQ;kDACnE,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoN,UAAS;;;;;;;;;;;;;;;;8CAG5P,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,OAAO,WAAW;;;;;;sDAErB,8OAAC;4CAAE,WAAU;sDACV,OACG,uEACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB", "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,OAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;wCACVC,IAAAA;oCAAAA,CAAMZ,UAAUa,QAAQ;oCAAA;gDACxBC,IAAAA;4CAAAA,CAAM;4CAAA;iDACNC,UAAU;sDACV,IAAA,CAAA;gDAAA,QAAA;oDAAA,IAAA,iBAA2C;oDAAA;iDAAA;;+CAC3CC,YAAY;;yCACZC,UAAU;8CACVC,IAAAA,CAAAA;oCAAAA,CAAU;iCAAA,CAAE;;6BACd;kCACAC,QAAAA,CAAU,CAAA;4BAAA;yBAAA;;yBACRC,YAAYnB;0BACd,QAAA,CAAA;oBAAA;iBAAA;YACF;YAAE", "ignoreList": [0], "debugId": null}}]}