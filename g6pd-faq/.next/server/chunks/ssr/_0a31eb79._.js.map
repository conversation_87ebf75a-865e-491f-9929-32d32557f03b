{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/privacy/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\ninterface PrivacyPageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: PrivacyPageProps): Promise<Metadata> {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n  \n  return {\n    title: isZh ? '隐私政策 - G6PD缺乏症FAQ' : 'Privacy Policy - G6PD Deficiency FAQ',\n    description: isZh \n      ? '了解我们如何收集、使用和保护您的个人信息。我们致力于保护用户隐私和数据安全。'\n      : 'Learn how we collect, use, and protect your personal information. We are committed to protecting user privacy and data security.',\n  }\n}\n\nexport default async function PrivacyPage({ params }: PrivacyPageProps) {\n  const { locale } = await params\n  const isZh = locale === 'zh'\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">\n            {isZh ? '隐私政策' : 'Privacy Policy'}\n          </h1>\n          \n          <div className=\"prose prose-lg max-w-none\">\n            {isZh ? (\n              <>\n                <p className=\"text-gray-600 mb-6\">\n                  最后更新：2024年6月28日\n                </p>\n                \n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">1. 信息收集</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  我们收集的信息类型包括：\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>访问日志：包括IP地址、浏览器类型、访问时间等技术信息</li>\n                  <li>使用数据：页面浏览记录、搜索查询、点击行为等</li>\n                  <li>设备信息：设备类型、操作系统、屏幕分辨率等</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">2. 信息使用</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  我们使用收集的信息用于：\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>改善网站功能和用户体验</li>\n                  <li>分析网站使用情况和趋势</li>\n                  <li>提供个性化内容推荐</li>\n                  <li>确保网站安全和防止滥用</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">3. 信息保护</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  我们采取适当的技术和组织措施来保护您的个人信息，包括加密传输、访问控制和定期安全审查。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">4. Cookie使用</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  我们使用Cookie来改善用户体验，包括记住语言偏好、分析网站使用情况等。您可以通过浏览器设置管理Cookie。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">5. 第三方服务</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  我们可能使用第三方分析服务（如Google Analytics）来了解网站使用情况。这些服务有自己的隐私政策。\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">6. 联系我们</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  如果您对本隐私政策有任何疑问，请通过以下方式联系我们：\n                </p>\n                <p className=\"text-gray-700\">\n                  邮箱：<EMAIL>\n                </p>\n              </>\n            ) : (\n              <>\n                <p className=\"text-gray-600 mb-6\">\n                  Last updated: June 28, 2024\n                </p>\n                \n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">1. Information Collection</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  We collect the following types of information:\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>Access logs: including IP addresses, browser types, access times, and other technical information</li>\n                  <li>Usage data: page views, search queries, click behavior, etc.</li>\n                  <li>Device information: device type, operating system, screen resolution, etc.</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">2. Information Use</h2>\n                <p className=\"text-gray-700 mb-4\">\n                  We use collected information to:\n                </p>\n                <ul className=\"list-disc pl-6 mb-6 text-gray-700\">\n                  <li>Improve website functionality and user experience</li>\n                  <li>Analyze website usage patterns and trends</li>\n                  <li>Provide personalized content recommendations</li>\n                  <li>Ensure website security and prevent abuse</li>\n                </ul>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">3. Information Protection</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  We implement appropriate technical and organizational measures to protect your personal information, including encrypted transmission, access controls, and regular security reviews.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">4. Cookie Usage</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  We use cookies to improve user experience, including remembering language preferences and analyzing website usage. You can manage cookies through your browser settings.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">5. Third-Party Services</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  We may use third-party analytics services (such as Google Analytics) to understand website usage. These services have their own privacy policies.\n                </p>\n\n                <h2 className=\"text-2xl font-semibold text-gray-900 mt-8 mb-4\">6. Contact Us</h2>\n                <p className=\"text-gray-700 mb-6\">\n                  If you have any questions about this privacy policy, please contact us:\n                </p>\n                <p className=\"text-gray-700\">\n                  Email: <EMAIL>\n                </p>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAoB;IACjE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,OAAO;QACL,OAAO,OAAO,sBAAsB;QACpC,aAAa,OACT,2CACA;IACN;AACF;AAEe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,OAAO,WAAW;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,OAAO,SAAS;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;kCACZ,qBACC;;8CACE,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;yDAK/B;;8CACE,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,GAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BAC<PERSON>,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}