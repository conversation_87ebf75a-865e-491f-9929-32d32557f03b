{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/i18n.ts"], "sourcesContent": ["import { notFound } from 'next/navigation';\nimport { getRequestConfig } from 'next-intl/server';\n\n// Can be imported from a shared config\nexport const locales = ['zh', 'en'] as const;\nexport type Locale = typeof locales[number];\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) notFound();\n\n  return {\n    locale: locale as Locale,\n    messages: (await import(`../messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;uCAGpB,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAmB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAEhD,OAAO;QACL,QAAQ;QACR,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAChE;AACF", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx\",\n    \"Navigation\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/components/footer.tsx"], "sourcesContent": ["import { useTranslations } from 'next-intl'\nimport Link from 'next/link'\nimport { useLocale } from 'next-intl'\n\nexport function Footer() {\n  const t = useTranslations('footer')\n  const locale = useLocale()\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-gray-50 border-t\">\n      <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Logo and description */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                G6PD FAQ\n              </h3>\n            </div>\n            <p className=\"mt-4 text-sm text-gray-600 max-w-md\">\n              {locale === 'zh' \n                ? '为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导。'\n                : 'Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families.'\n              }\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase\">\n              {locale === 'zh' ? '快速链接' : 'Quick Links'}\n            </h4>\n            <ul className=\"mt-4 space-y-2\">\n              <li>\n                <Link \n                  href={`/${locale}/faq`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '常见问题' : 'FAQ'}\n                </Link>\n              </li>\n              <li>\n                <Link \n                  href={`/${locale}/medications`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '用药指导' : 'Medications'}\n                </Link>\n              </li>\n              <li>\n                <Link \n                  href={`/${locale}/search`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '搜索' : 'Search'}\n                </Link>\n              </li>\n              <li>\n                <Link \n                  href={`/${locale}/about`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '关于我们' : 'About'}\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Categories */}\n          <div>\n            <h4 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase\">\n              {locale === 'zh' ? '分类' : 'Categories'}\n            </h4>\n            <ul className=\"mt-4 space-y-2\">\n              <li>\n                <Link \n                  href={`/${locale}/faq/medications`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '用药相关' : 'Medications'}\n                </Link>\n              </li>\n              <li>\n                <Link \n                  href={`/${locale}/faq/diet`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '饮食相关' : 'Diet'}\n                </Link>\n              </li>\n              <li>\n                <Link \n                  href={`/${locale}/faq/symptoms`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '症状相关' : 'Symptoms'}\n                </Link>\n              </li>\n              <li>\n                <Link \n                  href={`/${locale}/faq/treatment`}\n                  className=\"text-sm text-gray-600 hover:text-gray-900\"\n                >\n                  {locale === 'zh' ? '治疗相关' : 'Treatment'}\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Emergency notice */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-800\">\n                  {locale === 'zh' \n                    ? '紧急情况：如出现急性溶血症状（黄疸、深色尿液、疲劳等），请立即就医！'\n                    : 'Emergency: If you experience acute hemolysis symptoms (jaundice, dark urine, fatigue), seek immediate medical attention!'\n                  }\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom section */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex space-x-6\">\n              <Link \n                href={`/${locale}/privacy`}\n                className=\"text-sm text-gray-600 hover:text-gray-900\"\n              >\n                {t('links.privacy')}\n              </Link>\n              <Link \n                href={`/${locale}/terms`}\n                className=\"text-sm text-gray-600 hover:text-gray-900\"\n              >\n                {t('links.terms')}\n              </Link>\n              <Link \n                href={`/${locale}/contact`}\n                className=\"text-sm text-gray-600 hover:text-gray-900\"\n              >\n                {t('links.contact')}\n              </Link>\n            </div>\n            <div className=\"mt-4 md:mt-0\">\n              <p className=\"text-sm text-gray-600\">\n                {t('copyright')} © {currentYear}\n              </p>\n            </div>\n          </div>\n          \n          {/* Disclaimer */}\n          <div className=\"mt-4\">\n            <p className=\"text-xs text-gray-500 text-center\">\n              {t('disclaimer')}\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kOAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAItD,8OAAC;oCAAE,WAAU;8CACV,WAAW,OACR,oCACA;;;;;;;;;;;;sCAMR,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,WAAW,OAAO,SAAS;;;;;;8CAE9B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;gDACtB,WAAU;0DAET,WAAW,OAAO,SAAS;;;;;;;;;;;sDAGhC,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC;gDAC9B,WAAU;0DAET,WAAW,OAAO,SAAS;;;;;;;;;;;sDAGhC,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC;gDACzB,WAAU;0DAET,WAAW,OAAO,OAAO;;;;;;;;;;;sDAG9B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;gDACxB,WAAU;0DAET,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;sCAOpC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,WAAW,OAAO,OAAO;;;;;;8CAE5B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,gBAAgB,CAAC;gDAClC,WAAU;0DAET,WAAW,OAAO,SAAS;;;;;;;;;;;sDAGhC,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC;gDAC3B,WAAU;0DAET,WAAW,OAAO,SAAS;;;;;;;;;;;sDAGhC,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC;gDAC/B,WAAU;0DAET,WAAW,OAAO,SAAS;;;;;;;;;;;sDAGhC,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,cAAc,CAAC;gDAChC,WAAU;0DAET,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,SAAQ;wCAAY,MAAK;kDAC7D,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoN,UAAS;;;;;;;;;;;;;;;;8CAG5P,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDACV,WAAW,OACR,uCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;4CAC1B,WAAU;sDAET,EAAE;;;;;;sDAEL,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;4CACxB,WAAU;sDAET,EAAE;;;;;;sDAEL,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;4CAC1B,WAAU;sDAET,EAAE;;;;;;;;;;;;8CAGP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CACV,EAAE;4CAAa;4CAAI;;;;;;;;;;;;;;;;;;sCAM1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;uCAEe", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/code/soho/2024/g6pd-faq/g6pd-faq/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import { NextIntlClientProvider } from 'next-intl'\nimport { getMessages } from 'next-intl/server'\nimport { notFound } from 'next/navigation'\nimport Navigation from '@/components/navigation'\nimport Footer from '@/components/footer'\n\nconst locales = ['zh', 'en']\n\nexport function generateStaticParams() {\n  return locales.map((locale) => ({ locale }))\n}\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}) {\n  const { locale } = await params\n  const messages = await getMessages({ locale })\n  const siteTitle = (messages as Record<string, Record<string, unknown>>)?.site?.title || 'G6PD Deficiency FAQ'\n  const siteDescription = (messages as Record<string, Record<string, unknown>>)?.site?.description || 'Comprehensive FAQ for G6PD deficiency patients and families'\n\n  return {\n    title: {\n      template: `%s | ${siteTitle}`,\n      default: siteTitle\n    },\n    description: siteDescription,\n    keywords: (messages as Record<string, Record<string, unknown>>)?.site?.keywords as string,\n    openGraph: {\n      title: siteTitle,\n      description: siteDescription,\n      type: 'website',\n      locale: locale,\n      alternateLocale: locales.filter(l => l !== locale)\n    },\n    alternates: {\n      languages: {\n        'zh': '/zh',\n        'en': '/en'\n      }\n    }\n  }\n}\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: {\n  children: React.ReactNode\n  params: Promise<{ locale: string }>\n}) {\n  const { locale } = await params\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as string)) {\n    notFound()\n  }\n\n  // Providing all messages to the client side is the easiest way to get started\n  const messages = await getMessages({ locale })\n\n  return (\n    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'}>\n      <body>\n        <NextIntlClientProvider messages={messages}>\n          <div className=\"min-h-screen flex flex-col\">\n            <Navigation />\n            <main className=\"flex-1\">\n              {children}\n            </main>\n            <Footer />\n          </div>\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;;AAEA,MAAM,UAAU;IAAC;IAAM;CAAK;AAErB,SAAS;IACd,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;YAAE;QAAO,CAAC;AAC5C;AAEO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAO;IAC5C,MAAM,YAAY,AAAC,UAAsD,MAAM,SAAS;IACxF,MAAM,kBAAkB,AAAC,UAAsD,MAAM,eAAe;IAEpG,OAAO;QACL,OAAO;YACL,UAAU,CAAC,KAAK,EAAE,WAAW;YAC7B,SAAS;QACX;QACA,aAAa;QACb,UAAW,UAAsD,MAAM;QACvE,WAAW;YACT,OAAO;YACP,aAAa;YACb,MAAM;YACN,QAAQ;YACR,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,MAAM;QAC7C;QACA,YAAY;YACV,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAmB;QACvC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,8EAA8E;IAC9E,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAO;IAE5C,qBACE,8OAAC;QAAK,MAAM;QAAQ,KAAK,WAAW,OAAO,QAAQ;kBACjD,cAAA,8OAAC;sBACC,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;gBAAC,UAAU;0BAChC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,UAAU;;;;;sCACX,8OAAC;4BAAK,WAAU;sCACb;;;;;;sCAEH,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}]}