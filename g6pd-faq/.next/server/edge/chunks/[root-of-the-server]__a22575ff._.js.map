{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n.ts"], "sourcesContent": ["import { notFound } from 'next/navigation';\nimport { getRequestConfig } from 'next-intl/server';\n\n// Can be imported from a shared config\nexport const locales = ['zh', 'en'] as const;\nexport type Locale = typeof locales[number];\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) notFound();\n\n  return {\n    locale: locale as Locale,\n    messages: (await import(`../messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;uCAGpB,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAmB,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD;IAEhD,OAAO;QACL,QAAQ;QACR,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAChE;AACF"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport { locales } from './src/i18n';\n\nexport default createMiddleware({\n  // A list of all locales that are supported\n  locales,\n\n  // Used when no locale matches\n  defaultLocale: 'zh',\n\n  // Always use locale prefix\n  localePrefix: 'always'\n});\n\nexport const config = {\n  // Match only internationalized pathnames\n  matcher: ['/', '/(zh|en)/:path*']\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE;IAC9B,2CAA2C;IAC3C,SAAA,mHAAA,CAAA,UAAO;IAEP,8BAA8B;IAC9B,eAAe;IAEf,2BAA2B;IAC3B,cAAc;AAChB;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;QAAK;KAAkB;AACnC"}}]}