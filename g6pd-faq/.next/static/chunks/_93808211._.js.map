{"version": 3, "sources": [], "sections": [{"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared/runtime-utils.ts"], "sourcesContent": ["/**\n * This file contains runtime types and functions that are shared between all\n * TurboPack ECMAScript runtimes.\n *\n * It will be prepended to the runtime code of each runtime.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"./runtime-types.d.ts\" />\n\ntype EsmNamespaceObject = Record<string, any>;\n\n// @ts-ignore Defined in `dev-base.ts`\ndeclare function getOrInstantiateModuleFromParent<M>(\n  id: ModuleId,\n  sourceModule: M\n): M;\n\nconst REEXPORTED_OBJECTS = Symbol(\"reexported objects\");\n\ntype ModuleContextMap = Record<ModuleId, ModuleContextEntry>;\n\ninterface ModuleContextEntry {\n  id: () => ModuleId;\n  module: () => any;\n}\n\ninterface ModuleContext {\n  // require call\n  (moduleId: ModuleId): Exports | EsmNamespaceObject;\n\n  // async import call\n  import(moduleId: ModuleId): Promise<Exports | EsmNamespaceObject>;\n\n  keys(): ModuleId[];\n\n  resolve(moduleId: ModuleId): ModuleId;\n}\n\ntype GetOrInstantiateModuleFromParent<M> = (\n  moduleId: ModuleId,\n  parentModule: M\n) => M;\n\ndeclare function getOrInstantiateRuntimeModule(moduleId: ModuleId, chunkPath: ChunkPath): Module;\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst toStringTag = typeof Symbol !== \"undefined\" && Symbol.toStringTag;\n\nfunction defineProp(\n  obj: any,\n  name: PropertyKey,\n  options: PropertyDescriptor & ThisType<any>\n) {\n  if (!hasOwnProperty.call(obj, name))\n    Object.defineProperty(obj, name, options);\n}\n\n/**\n * Adds the getters to the exports object.\n */\nfunction esm(\n  exports: Exports,\n  getters: Record<string, (() => any) | [() => any, (v: any) => void]>\n) {\n  defineProp(exports, \"__esModule\", { value: true });\n  if (toStringTag) defineProp(exports, toStringTag, { value: \"Module\" });\n  for (const key in getters) {\n    const item = getters[key];\n    if (Array.isArray(item)) {\n      defineProp(exports, key, {\n        get: item[0],\n        set: item[1],\n        enumerable: true,\n      });\n    } else {\n      defineProp(exports, key, { get: item, enumerable: true });\n    }\n  }\n  Object.seal(exports);\n}\n\n/**\n * Makes the module an ESM with exports\n */\nfunction esmExport(\n  module: Module,\n  exports: Exports,\n  getters: Record<string, () => any>\n) {\n  module.namespaceObject = module.exports;\n  esm(exports, getters);\n}\n\nfunction ensureDynamicExports(module: Module, exports: Exports) {\n  let reexportedObjects = module[REEXPORTED_OBJECTS];\n\n  if (!reexportedObjects) {\n    reexportedObjects = module[REEXPORTED_OBJECTS] = [];\n    module.exports = module.namespaceObject = new Proxy(exports, {\n      get(target, prop) {\n        if (\n          hasOwnProperty.call(target, prop) ||\n          prop === \"default\" ||\n          prop === \"__esModule\"\n        ) {\n          return Reflect.get(target, prop);\n        }\n        for (const obj of reexportedObjects!) {\n          const value = Reflect.get(obj, prop);\n          if (value !== undefined) return value;\n        }\n        return undefined;\n      },\n      ownKeys(target) {\n        const keys = Reflect.ownKeys(target);\n        for (const obj of reexportedObjects!) {\n          for (const key of Reflect.ownKeys(obj)) {\n            if (key !== \"default\" && !keys.includes(key)) keys.push(key);\n          }\n        }\n        return keys;\n      },\n    });\n  }\n}\n\n/**\n * Dynamically exports properties from an object\n */\nfunction dynamicExport(\n  module: Module,\n  exports: Exports,\n  object: Record<string, any>\n) {\n  ensureDynamicExports(module, exports);\n\n  if (typeof object === \"object\" && object !== null) {\n    module[REEXPORTED_OBJECTS]!.push(object);\n  }\n}\n\nfunction exportValue(module: Module, value: any) {\n  module.exports = value;\n}\n\nfunction exportNamespace(module: Module, namespace: any) {\n  module.exports = module.namespaceObject = namespace;\n}\n\nfunction createGetter(obj: Record<string | symbol, any>, key: string | symbol) {\n  return () => obj[key];\n}\n\n/**\n * @returns prototype of the object\n */\nconst getProto: (obj: any) => any = Object.getPrototypeOf\n  ? (obj) => Object.getPrototypeOf(obj)\n  : (obj) => obj.__proto__;\n\n/** Prototypes that are not expanded for exports */\nconst LEAF_PROTOTYPES = [null, getProto({}), getProto([]), getProto(getProto)];\n\n/**\n * @param raw\n * @param ns\n * @param allowExportDefault\n *   * `false`: will have the raw module as default export\n *   * `true`: will have the default property as default export\n */\nfunction interopEsm(\n  raw: Exports,\n  ns: EsmNamespaceObject,\n  allowExportDefault?: boolean\n) {\n  const getters: { [s: string]: () => any } = Object.create(null);\n  for (\n    let current = raw;\n    (typeof current === \"object\" || typeof current === \"function\") &&\n    !LEAF_PROTOTYPES.includes(current);\n    current = getProto(current)\n  ) {\n    for (const key of Object.getOwnPropertyNames(current)) {\n      getters[key] = createGetter(raw, key);\n    }\n  }\n\n  // this is not really correct\n  // we should set the `default` getter if the imported module is a `.cjs file`\n  if (!(allowExportDefault && \"default\" in getters)) {\n    getters[\"default\"] = () => raw;\n  }\n\n  esm(ns, getters);\n  return ns;\n}\n\nfunction createNS(raw: Module[\"exports\"]): EsmNamespaceObject {\n  if (typeof raw === \"function\") {\n    return function (this: any, ...args: any[]) {\n      return raw.apply(this, args);\n    };\n  } else {\n    return Object.create(null);\n  }\n}\n\nfunction esmImport(\n  sourceModule: Module,\n  id: ModuleId\n): Exclude<Module[\"namespaceObject\"], undefined> {\n  const module = getOrInstantiateModuleFromParent(id, sourceModule);\n  if (module.error) throw module.error;\n\n  // any ES module has to have `module.namespaceObject` defined.\n  if (module.namespaceObject) return module.namespaceObject;\n\n  // only ESM can be an async module, so we don't need to worry about exports being a promise here.\n  const raw = module.exports;\n  return (module.namespaceObject = interopEsm(\n    raw,\n    createNS(raw),\n    raw && (raw as any).__esModule\n  ));\n}\n\n// Add a simple runtime require so that environments without one can still pass\n// `typeof require` CommonJS checks so that exports are correctly registered.\nconst runtimeRequire =\n  // @ts-ignore\n  typeof require === \"function\"\n    // @ts-ignore\n    ? require\n    : function require() {\n        throw new Error(\"Unexpected use of runtime require\");\n      };\n\nfunction commonJsRequire(sourceModule: Module, id: ModuleId): Exports {\n  const module = getOrInstantiateModuleFromParent(id, sourceModule);\n  if (module.error) throw module.error;\n  return module.exports;\n}\n\n/**\n * `require.context` and require/import expression runtime.\n */\nfunction moduleContext(map: ModuleContextMap): ModuleContext {\n  function moduleContext(id: ModuleId): Exports {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].module();\n    }\n\n    const e = new Error(`Cannot find module '${id}'`);\n    (e as any).code = \"MODULE_NOT_FOUND\";\n    throw e;\n  }\n\n  moduleContext.keys = (): ModuleId[] => {\n    return Object.keys(map);\n  };\n\n  moduleContext.resolve = (id: ModuleId): ModuleId => {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].id();\n    }\n\n    const e = new Error(`Cannot find module '${id}'`);\n    (e as any).code = \"MODULE_NOT_FOUND\";\n    throw e;\n  };\n\n  moduleContext.import = async (id: ModuleId) => {\n    return await (moduleContext(id) as Promise<Exports>);\n  };\n\n  return moduleContext;\n}\n\n/**\n * Returns the path of a chunk defined by its data.\n */\nfunction getChunkPath(chunkData: ChunkData): ChunkPath {\n  return typeof chunkData === \"string\" ? chunkData : chunkData.path;\n}\n\nfunction isPromise<T = any>(maybePromise: any): maybePromise is Promise<T> {\n  return (\n    maybePromise != null &&\n    typeof maybePromise === \"object\" &&\n    \"then\" in maybePromise &&\n    typeof maybePromise.then === \"function\"\n  );\n}\n\nfunction isAsyncModuleExt<T extends {}>(obj: T): obj is AsyncModuleExt & T {\n  return turbopackQueues in obj;\n}\n\nfunction createPromise<T>() {\n  let resolve: (value: T | PromiseLike<T>) => void;\n  let reject: (reason?: any) => void;\n\n  const promise = new Promise<T>((res, rej) => {\n    reject = rej;\n    resolve = res;\n  });\n\n  return {\n    promise,\n    resolve: resolve!,\n    reject: reject!,\n  };\n}\n\n// everything below is adapted from webpack\n// https://github.com/webpack/webpack/blob/6be4065ade1e252c1d8dcba4af0f43e32af1bdc1/lib/runtime/AsyncModuleRuntimeModule.js#L13\n\nconst turbopackQueues = Symbol(\"turbopack queues\");\nconst turbopackExports = Symbol(\"turbopack exports\");\nconst turbopackError = Symbol(\"turbopack error\");\n\nconst enum QueueStatus {\n  Unknown = -1,\n  Unresolved = 0,\n  Resolved = 1,\n}\n\ntype AsyncQueueFn = (() => void) & { queueCount: number };\ntype AsyncQueue = AsyncQueueFn[] & {\n  status: QueueStatus;\n};\n\nfunction resolveQueue(queue?: AsyncQueue) {\n  if (queue && queue.status !== QueueStatus.Resolved) {\n    queue.status = QueueStatus.Resolved;\n    queue.forEach((fn) => fn.queueCount--);\n    queue.forEach((fn) => (fn.queueCount-- ? fn.queueCount++ : fn()));\n  }\n}\n\ntype Dep = Exports | AsyncModulePromise | Promise<Exports>;\n\ntype AsyncModuleExt = {\n  [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => void;\n  [turbopackExports]: Exports;\n  [turbopackError]?: any;\n};\n\ntype AsyncModulePromise<T = Exports> = Promise<T> & AsyncModuleExt;\n\nfunction wrapDeps(deps: Dep[]): AsyncModuleExt[] {\n  return deps.map((dep): AsyncModuleExt => {\n    if (dep !== null && typeof dep === \"object\") {\n      if (isAsyncModuleExt(dep)) return dep;\n      if (isPromise(dep)) {\n        const queue: AsyncQueue = Object.assign([], {\n          status: QueueStatus.Unresolved,\n        });\n\n        const obj: AsyncModuleExt = {\n          [turbopackExports]: {},\n          [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => fn(queue),\n        };\n\n        dep.then(\n          (res) => {\n            obj[turbopackExports] = res;\n            resolveQueue(queue);\n          },\n          (err) => {\n            obj[turbopackError] = err;\n            resolveQueue(queue);\n          }\n        );\n\n        return obj;\n      }\n    }\n\n    return {\n      [turbopackExports]: dep,\n      [turbopackQueues]: () => {},\n    };\n  });\n}\n\nfunction asyncModule(\n  module: Module,\n  body: (\n    handleAsyncDependencies: (\n      deps: Dep[]\n    ) => Exports[] | Promise<() => Exports[]>,\n    asyncResult: (err?: any) => void\n  ) => void,\n  hasAwait: boolean\n) {\n  const queue: AsyncQueue | undefined = hasAwait\n    ? Object.assign([], { status: QueueStatus.Unknown })\n    : undefined;\n\n  const depQueues: Set<AsyncQueue> = new Set();\n\n  const { resolve, reject, promise: rawPromise } = createPromise<Exports>();\n\n  const promise: AsyncModulePromise = Object.assign(rawPromise, {\n    [turbopackExports]: module.exports,\n    [turbopackQueues]: (fn) => {\n      queue && fn(queue);\n      depQueues.forEach(fn);\n      promise[\"catch\"](() => {});\n    },\n  } satisfies AsyncModuleExt);\n\n  const attributes: PropertyDescriptor = {\n    get(): any {\n      return promise;\n    },\n    set(v: any) {\n      // Calling `esmExport` leads to this.\n      if (v !== promise) {\n        promise[turbopackExports] = v;\n      }\n    },\n  };\n\n  Object.defineProperty(module, \"exports\", attributes);\n  Object.defineProperty(module, \"namespaceObject\", attributes);\n\n  function handleAsyncDependencies(deps: Dep[]) {\n    const currentDeps = wrapDeps(deps);\n\n    const getResult = () =>\n      currentDeps.map((d) => {\n        if (d[turbopackError]) throw d[turbopackError];\n        return d[turbopackExports];\n      });\n\n    const { promise, resolve } = createPromise<() => Exports[]>();\n\n    const fn: AsyncQueueFn = Object.assign(() => resolve(getResult), {\n      queueCount: 0,\n    });\n\n    function fnQueue(q: AsyncQueue) {\n      if (q !== queue && !depQueues.has(q)) {\n        depQueues.add(q);\n        if (q && q.status === QueueStatus.Unresolved) {\n          fn.queueCount++;\n          q.push(fn);\n        }\n      }\n    }\n\n    currentDeps.map((dep) => dep[turbopackQueues](fnQueue));\n\n    return fn.queueCount ? promise : getResult();\n  }\n\n  function asyncResult(err?: any) {\n    if (err) {\n      reject((promise[turbopackError] = err));\n    } else {\n      resolve(promise[turbopackExports]);\n    }\n\n    resolveQueue(queue);\n  }\n\n  body(handleAsyncDependencies, asyncResult);\n\n  if (queue && queue.status === QueueStatus.Unknown) {\n    queue.status = QueueStatus.Unresolved;\n  }\n}\n\n/**\n * A pseudo \"fake\" URL object to resolve to its relative path.\n *\n * When UrlRewriteBehavior is set to relative, calls to the `new URL()` will construct url without base using this\n * runtime function to generate context-agnostic urls between different rendering context, i.e ssr / client to avoid\n * hydration mismatch.\n *\n * This is based on webpack's existing implementation:\n * https://github.com/webpack/webpack/blob/87660921808566ef3b8796f8df61bd79fc026108/lib/runtime/RelativeUrlRuntimeModule.js\n */\nconst relativeURL = function relativeURL(this: any, inputUrl: string) {\n  const realUrl = new URL(inputUrl, \"x:/\");\n  const values: Record<string, any> = {};\n  for (const key in realUrl) values[key] = (realUrl as any)[key];\n  values.href = inputUrl;\n  values.pathname = inputUrl.replace(/[?#].*/, \"\");\n  values.origin = values.protocol = \"\";\n  values.toString = values.toJSON = (..._args: Array<any>) => inputUrl;\n  for (const key in values)\n    Object.defineProperty(this, key, {\n      enumerable: true,\n      configurable: true,\n      value: values[key],\n    });\n};\n\nrelativeURL.prototype = URL.prototype;\n\n/**\n * Utility function to ensure all variants of an enum are handled.\n */\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\n  throw new Error(`Invariant: ${computeMessage(never)}`);\n}\n\n/**\n * A stub function to make `require` available but non-functional in ESM.\n */\nfunction requireStub(_moduleId: ModuleId): never {\n  throw new Error(\"dynamic usage of require is not supported\");\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,6CAA6C;AAU7C,MAAM,qBAAqB,OAAO;AA4BlC,MAAM,iBAAiB,OAAO,SAAS,CAAC,cAAc;AACtD,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,WAAW;AAEvE,SAAS,WACP,GAAQ,EACR,IAAiB,EACjB,OAA2C;IAE3C,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,OAC5B,OAAO,cAAc,CAAC,KAAK,MAAM;AACrC;AAEA;;CAEC,GACD,SAAS,IACP,OAAgB,EAChB,OAAoE;IAEpE,WAAW,SAAS,cAAc;QAAE,OAAO;IAAK;IAChD,IAAI,aAAa,WAAW,SAAS,aAAa;QAAE,OAAO;IAAS;IACpE,IAAK,MAAM,OAAO,QAAS;QACzB,MAAM,OAAO,OAAO,CAAC,IAAI;QACzB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,WAAW,SAAS,KAAK;gBACvB,KAAK,IAAI,CAAC,EAAE;gBACZ,KAAK,IAAI,CAAC,EAAE;gBACZ,YAAY;YACd;QACF,OAAO;YACL,WAAW,SAAS,KAAK;gBAAE,KAAK;gBAAM,YAAY;YAAK;QACzD;IACF;IACA,OAAO,IAAI,CAAC;AACd;AAEA;;CAEC,GACD,SAAS,UACP,MAAc,EACd,OAAgB,EAChB,OAAkC;IAElC,OAAO,eAAe,GAAG,OAAO,OAAO;IACvC,IAAI,SAAS;AACf;AAEA,SAAS,qBAAqB,MAAc,EAAE,OAAgB;IAC5D,IAAI,oBAAoB,MAAM,CAAC,mBAAmB;IAElD,IAAI,CAAC,mBAAmB;QACtB,oBAAoB,MAAM,CAAC,mBAAmB,GAAG,EAAE;QACnD,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG,IAAI,MAAM,SAAS;YAC3D,KAAI,MAAM,EAAE,IAAI;gBACd,IACE,eAAe,IAAI,CAAC,QAAQ,SAC5B,SAAS,aACT,SAAS,cACT;oBACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;gBAC7B;gBACA,KAAK,MAAM,OAAO,kBAAoB;oBACpC,MAAM,QAAQ,QAAQ,GAAG,CAAC,KAAK;oBAC/B,IAAI,UAAU,WAAW,OAAO;gBAClC;gBACA,OAAO;YACT;YACA,SAAQ,MAAM;gBACZ,MAAM,OAAO,QAAQ,OAAO,CAAC;gBAC7B,KAAK,MAAM,OAAO,kBAAoB;oBACpC,KAAK,MAAM,OAAO,QAAQ,OAAO,CAAC,KAAM;wBACtC,IAAI,QAAQ,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC;oBAC1D;gBACF;gBACA,OAAO;YACT;QACF;IACF;AACF;AAEA;;CAEC,GACD,SAAS,cACP,MAAc,EACd,OAAgB,EAChB,MAA2B;IAE3B,qBAAqB,QAAQ;IAE7B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;QACjD,MAAM,CAAC,mBAAmB,CAAE,IAAI,CAAC;IACnC;AACF;AAEA,SAAS,YAAY,MAAc,EAAE,KAAU;IAC7C,OAAO,OAAO,GAAG;AACnB;AAEA,SAAS,gBAAgB,MAAc,EAAE,SAAc;IACrD,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG;AAC5C;AAEA,SAAS,aAAa,GAAiC,EAAE,GAAoB;IAC3E,OAAO,IAAM,GAAG,CAAC,IAAI;AACvB;AAEA;;CAEC,GACD,MAAM,WAA8B,OAAO,cAAc,GACrD,CAAC,MAAQ,OAAO,cAAc,CAAC,OAC/B,CAAC,MAAQ,IAAI,SAAS;AAE1B,iDAAiD,GACjD,MAAM,kBAAkB;IAAC;IAAM,SAAS,CAAC;IAAI,SAAS,EAAE;IAAG,SAAS;CAAU;AAE9E;;;;;;CAMC,GACD,SAAS,WACP,GAAY,EACZ,EAAsB,EACtB,kBAA4B;IAE5B,MAAM,UAAsC,OAAO,MAAM,CAAC;IAC1D,IACE,IAAI,UAAU,KACd,CAAC,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU,KAC7D,CAAC,gBAAgB,QAAQ,CAAC,UAC1B,UAAU,SAAS,SACnB;QACA,KAAK,MAAM,OAAO,OAAO,mBAAmB,CAAC,SAAU;YACrD,OAAO,CAAC,IAAI,GAAG,aAAa,KAAK;QACnC;IACF;IAEA,6BAA6B;IAC7B,6EAA6E;IAC7E,IAAI,CAAC,CAAC,sBAAsB,aAAa,OAAO,GAAG;QACjD,OAAO,CAAC,UAAU,GAAG,IAAM;IAC7B;IAEA,IAAI,IAAI;IACR,OAAO;AACT;AAEA,SAAS,SAAS,GAAsB;IACtC,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,SAAqB,GAAG,IAAW;YACxC,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;QACzB;IACF,OAAO;QACL,OAAO,OAAO,MAAM,CAAC;IACvB;AACF;AAEA,SAAS,UACP,YAAoB,EACpB,EAAY;IAEZ,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IAEpC,8DAA8D;IAC9D,IAAI,OAAO,eAAe,EAAE,OAAO,OAAO,eAAe;IAEzD,iGAAiG;IACjG,MAAM,MAAM,OAAO,OAAO;IAC1B,OAAQ,OAAO,eAAe,GAAG,WAC/B,KACA,SAAS,MACT,OAAO,AAAC,IAAY,UAAU;AAElC;AAEA,+EAA+E;AAC/E,6EAA6E;AAC7E,MAAM,iBACJ,aAAa;AACb,OAAO,YAAY,aAEf,UACA,SAAS;IACP,MAAM,IAAI,MAAM;AAClB;AAEN,SAAS,gBAAgB,YAAoB,EAAE,EAAY;IACzD,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IACpC,OAAO,OAAO,OAAO;AACvB;AAEA;;CAEC,GACD,SAAS,cAAc,GAAqB;IAC1C,SAAS,cAAc,EAAY;QACjC,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM;QACvB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC/C,EAAU,IAAI,GAAG;QAClB,MAAM;IACR;IAEA,cAAc,IAAI,GAAG;QACnB,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA,cAAc,OAAO,GAAG,CAAC;QACvB,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;QACnB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC/C,EAAU,IAAI,GAAG;QAClB,MAAM;IACR;IAEA,cAAc,MAAM,GAAG,OAAO;QAC5B,OAAO,MAAO,cAAc;IAC9B;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,aAAa,SAAoB;IACxC,OAAO,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;AACnE;AAEA,SAAS,UAAmB,YAAiB;IAC3C,OACE,gBAAgB,QAChB,OAAO,iBAAiB,YACxB,UAAU,gBACV,OAAO,aAAa,IAAI,KAAK;AAEjC;AAEA,SAAS,iBAA+B,GAAM;IAC5C,OAAO,mBAAmB;AAC5B;AAEA,SAAS;IACP,IAAI;IACJ,IAAI;IAEJ,MAAM,UAAU,IAAI,QAAW,CAAC,KAAK;QACnC,SAAS;QACT,UAAU;IACZ;IAEA,OAAO;QACL;QACA,SAAS;QACT,QAAQ;IACV;AACF;AAEA,2CAA2C;AAC3C,+HAA+H;AAE/H,MAAM,kBAAkB,OAAO;AAC/B,MAAM,mBAAmB,OAAO;AAChC,MAAM,iBAAiB,OAAO;AAa9B,SAAS,aAAa,KAAkB;IACtC,IAAI,SAAS,MAAM,MAAM,QAA2B;QAClD,MAAM,MAAM;QACZ,MAAM,OAAO,CAAC,CAAC,KAAO,GAAG,UAAU;QACnC,MAAM,OAAO,CAAC,CAAC,KAAQ,GAAG,UAAU,KAAK,GAAG,UAAU,KAAK;IAC7D;AACF;AAYA,SAAS,SAAS,IAAW;IAC3B,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;YAC3C,IAAI,iBAAiB,MAAM,OAAO;YAClC,IAAI,UAAU,MAAM;gBAClB,MAAM,QAAoB,OAAO,MAAM,CAAC,EAAE,EAAE;oBAC1C,MAAM;gBACR;gBAEA,MAAM,MAAsB;oBAC1B,CAAC,iBAAiB,EAAE,CAAC;oBACrB,CAAC,gBAAgB,EAAE,CAAC,KAAoC,GAAG;gBAC7D;gBAEA,IAAI,IAAI,CACN,CAAC;oBACC,GAAG,CAAC,iBAAiB,GAAG;oBACxB,aAAa;gBACf,GACA,CAAC;oBACC,GAAG,CAAC,eAAe,GAAG;oBACtB,aAAa;gBACf;gBAGF,OAAO;YACT;QACF;QAEA,OAAO;YACL,CAAC,iBAAiB,EAAE;YACpB,CAAC,gBAAgB,EAAE,KAAO;QAC5B;IACF;AACF;AAEA,SAAS,YACP,MAAc,EACd,IAKS,EACT,QAAiB;IAEjB,MAAM,QAAgC,WAClC,OAAO,MAAM,CAAC,EAAE,EAAE;QAAE,MAAM;IAAsB,KAChD;IAEJ,MAAM,YAA6B,IAAI;IAEvC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;IAEjD,MAAM,UAA8B,OAAO,MAAM,CAAC,YAAY;QAC5D,CAAC,iBAAiB,EAAE,OAAO,OAAO;QAClC,CAAC,gBAAgB,EAAE,CAAC;YAClB,SAAS,GAAG;YACZ,UAAU,OAAO,CAAC;YAClB,OAAO,CAAC,QAAQ,CAAC,KAAO;QAC1B;IACF;IAEA,MAAM,aAAiC;QACrC;YACE,OAAO;QACT;QACA,KAAI,CAAM;YACR,qCAAqC;YACrC,IAAI,MAAM,SAAS;gBACjB,OAAO,CAAC,iBAAiB,GAAG;YAC9B;QACF;IACF;IAEA,OAAO,cAAc,CAAC,QAAQ,WAAW;IACzC,OAAO,cAAc,CAAC,QAAQ,mBAAmB;IAEjD,SAAS,wBAAwB,IAAW;QAC1C,MAAM,cAAc,SAAS;QAE7B,MAAM,YAAY,IAChB,YAAY,GAAG,CAAC,CAAC;gBACf,IAAI,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,eAAe;gBAC9C,OAAO,CAAC,CAAC,iBAAiB;YAC5B;QAEF,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAE7B,MAAM,KAAmB,OAAO,MAAM,CAAC,IAAM,QAAQ,YAAY;YAC/D,YAAY;QACd;QAEA,SAAS,QAAQ,CAAa;YAC5B,IAAI,MAAM,SAAS,CAAC,UAAU,GAAG,CAAC,IAAI;gBACpC,UAAU,GAAG,CAAC;gBACd,IAAI,KAAK,EAAE,MAAM,QAA6B;oBAC5C,GAAG,UAAU;oBACb,EAAE,IAAI,CAAC;gBACT;YACF;QACF;QAEA,YAAY,GAAG,CAAC,CAAC,MAAQ,GAAG,CAAC,gBAAgB,CAAC;QAE9C,OAAO,GAAG,UAAU,GAAG,UAAU;IACnC;IAEA,SAAS,YAAY,GAAS;QAC5B,IAAI,KAAK;YACP,OAAQ,OAAO,CAAC,eAAe,GAAG;QACpC,OAAO;YACL,QAAQ,OAAO,CAAC,iBAAiB;QACnC;QAEA,aAAa;IACf;IAEA,KAAK,yBAAyB;IAE9B,IAAI,SAAS,MAAM,MAAM,SAA0B;QACjD,MAAM,MAAM;IACd;AACF;AAEA;;;;;;;;;CASC,GACD,MAAM,cAAc,SAAS,YAAuB,QAAgB;IAClE,MAAM,UAAU,IAAI,IAAI,UAAU;IAClC,MAAM,SAA8B,CAAC;IACrC,IAAK,MAAM,OAAO,QAAS,MAAM,CAAC,IAAI,GAAG,AAAC,OAAe,CAAC,IAAI;IAC9D,OAAO,IAAI,GAAG;IACd,OAAO,QAAQ,GAAG,SAAS,OAAO,CAAC,UAAU;IAC7C,OAAO,MAAM,GAAG,OAAO,QAAQ,GAAG;IAClC,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,CAAC,GAAG,QAAsB;IAC5D,IAAK,MAAM,OAAO,OAChB,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;QAC/B,YAAY;QACZ,cAAc;QACd,OAAO,MAAM,CAAC,IAAI;IACpB;AACJ;AAEA,YAAY,SAAS,GAAG,IAAI,SAAS;AAErC;;CAEC,GACD,SAAS,UAAU,KAAY,EAAE,cAAoC;IACnE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,eAAe,QAAQ;AACvD;AAEA;;CAEC,GACD,SAAS,YAAY,SAAmB;IACtC,MAAM,IAAI,MAAM;AAClB", "ignoreList": [0]}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/base/runtime-base.ts"], "sourcesContent": ["/**\n * This file contains runtime types and functions that are shared between all\n * Turbopack *development* ECMAScript runtimes.\n *\n * It will be appended to the runtime code of each runtime right after the\n * shared runtime utils.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../base/globals.d.ts\" />\n/// <reference path=\"../../../shared/runtime-utils.ts\" />\n\n// Used in WebWorkers to tell the runtime about the chunk base path\ndeclare var TURBOPACK_WORKER_LOCATION: string;\n// Used in WebWorkers to tell the runtime about the current chunk url since it can't be detected via document.currentScript\n// Note it's stored in reversed order to use push and pop\ndeclare var TURBOPACK_NEXT_CHUNK_URLS: ChunkUrl[] | undefined;\n\n// Injected by rust code\ndeclare var CHUNK_BASE_PATH: string;\ndeclare var CHUNK_SUFFIX_PATH: string;\n\n// Provided by build or dev base\ndeclare function instantiateModule(id: ModuleId, source: SourceInfo): Module;\n\ntype RuntimeParams = {\n  otherChunks: ChunkData[];\n  runtimeModuleIds: ModuleId[];\n};\n\ntype ChunkRegistration = [\n  chunkPath: ChunkScript,\n  chunkModules: ModuleFactories,\n  params: RuntimeParams | undefined\n];\n\ntype ChunkList = {\n  script: ChunkListScript;\n  chunks: ChunkData[];\n  source: \"entry\" | \"dynamic\";\n};\n\nenum SourceType {\n  /**\n   * The module was instantiated because it was included in an evaluated chunk's\n   * runtime.\n   */\n  Runtime = 0,\n  /**\n   * The module was instantiated because a parent module imported it.\n   */\n  Parent = 1,\n  /**\n   * The module was instantiated because it was included in a chunk's hot module\n   * update.\n   */\n  Update = 2,\n}\n\ntype SourceInfo =\n  | {\n      type: SourceType.Runtime;\n      chunkPath: ChunkPath;\n    }\n  | {\n      type: SourceType.Parent;\n      parentId: ModuleId;\n    }\n  | {\n      type: SourceType.Update;\n      parents?: ModuleId[];\n    };\n\ninterface RuntimeBackend {\n  registerChunk: (chunkPath: ChunkPath, params?: RuntimeParams) => void;\n  loadChunk: (chunkUrl: ChunkUrl, source: SourceInfo) => Promise<void>;\n}\n\ninterface DevRuntimeBackend {\n  reloadChunk?: (chunkUrl: ChunkUrl) => Promise<void>;\n  unloadChunk?: (chunkUrl: ChunkUrl) => void;\n  restart: () => void;\n}\n\nconst moduleFactories: ModuleFactories = Object.create(null);\n/**\n * Module IDs that are instantiated as part of the runtime of a chunk.\n */\nconst runtimeModules: Set<ModuleId> = new Set();\n/**\n * Map from module ID to the chunks that contain this module.\n *\n * In HMR, we need to keep track of which modules are contained in which so\n * chunks. This is so we don't eagerly dispose of a module when it is removed\n * from chunk A, but still exists in chunk B.\n */\nconst moduleChunksMap: Map<ModuleId, Set<ChunkPath>> = new Map();\n/**\n * Map from a chunk path to all modules it contains.\n */\nconst chunkModulesMap: Map<ChunkPath, Set<ModuleId>> = new Map();\n/**\n * Chunk lists that contain a runtime. When these chunk lists receive an update\n * that can't be reconciled with the current state of the page, we need to\n * reload the runtime entirely.\n */\nconst runtimeChunkLists: Set<ChunkListPath> = new Set();\n/**\n * Map from a chunk list to the chunk paths it contains.\n */\nconst chunkListChunksMap: Map<ChunkListPath, Set<ChunkPath>> = new Map();\n/**\n * Map from a chunk path to the chunk lists it belongs to.\n */\nconst chunkChunkListsMap: Map<ChunkPath, Set<ChunkListPath>> = new Map();\n\nconst availableModules: Map<ModuleId, Promise<any> | true> = new Map();\n\nconst availableModuleChunks: Map<ChunkPath, Promise<any> | true> = new Map();\n\nasync function loadChunk(\n  source: SourceInfo,\n  chunkData: ChunkData\n): Promise<any> {\n  if (typeof chunkData === \"string\") {\n    return loadChunkPath(source, chunkData);\n  }\n\n  const includedList = chunkData.included || [];\n  const modulesPromises = includedList.map((included) => {\n    if (moduleFactories[included]) return true;\n    return availableModules.get(included);\n  });\n  if (modulesPromises.length > 0 && modulesPromises.every((p) => p)) {\n    // When all included items are already loaded or loading, we can skip loading ourselves\n    return Promise.all(modulesPromises);\n  }\n\n  const includedModuleChunksList = chunkData.moduleChunks || [];\n  const moduleChunksPromises = includedModuleChunksList\n    .map((included) => {\n      // TODO(alexkirsz) Do we need this check?\n      // if (moduleFactories[included]) return true;\n      return availableModuleChunks.get(included);\n    })\n    .filter((p) => p);\n\n  let promise;\n  if (moduleChunksPromises.length > 0) {\n    // Some module chunks are already loaded or loading.\n\n    if (moduleChunksPromises.length === includedModuleChunksList.length) {\n      // When all included module chunks are already loaded or loading, we can skip loading ourselves\n      return Promise.all(moduleChunksPromises);\n    }\n\n    const moduleChunksToLoad: Set<ChunkPath> = new Set();\n    for (const moduleChunk of includedModuleChunksList) {\n      if (!availableModuleChunks.has(moduleChunk)) {\n        moduleChunksToLoad.add(moduleChunk);\n      }\n    }\n\n    for (const moduleChunkToLoad of moduleChunksToLoad) {\n      const promise = loadChunkPath(source, moduleChunkToLoad);\n\n      availableModuleChunks.set(moduleChunkToLoad, promise);\n\n      moduleChunksPromises.push(promise);\n    }\n\n    promise = Promise.all(moduleChunksPromises);\n  } else {\n    promise = loadChunkPath(source, chunkData.path);\n\n    // Mark all included module chunks as loading if they are not already loaded or loading.\n    for (const includedModuleChunk of includedModuleChunksList) {\n      if (!availableModuleChunks.has(includedModuleChunk)) {\n        availableModuleChunks.set(includedModuleChunk, promise);\n      }\n    }\n  }\n\n  for (const included of includedList) {\n    if (!availableModules.has(included)) {\n      // It might be better to race old and new promises, but it's rare that the new promise will be faster than a request started earlier.\n      // In production it's even more rare, because the chunk optimization tries to deduplicate modules anyway.\n      availableModules.set(included, promise);\n    }\n  }\n\n  return promise;\n}\n\nasync function loadChunkByUrl(source: SourceInfo, chunkUrl: ChunkUrl) {\n  try {\n    await BACKEND.loadChunk(chunkUrl, source);\n  } catch (error) {\n    let loadReason;\n    switch (source.type) {\n      case SourceType.Runtime:\n        loadReason = `as a runtime dependency of chunk ${source.chunkPath}`;\n        break;\n      case SourceType.Parent:\n        loadReason = `from module ${source.parentId}`;\n        break;\n      case SourceType.Update:\n        loadReason = \"from an HMR update\";\n        break;\n      default:\n        invariant(source, (source) => `Unknown source type: ${source?.type}`);\n    }\n    throw new Error(\n      `Failed to load chunk ${chunkUrl} ${loadReason}${\n        error ? `: ${error}` : \"\"\n      }`,\n      error\n        ? {\n            cause: error,\n          }\n        : undefined\n    );\n  }\n}\n\nasync function loadChunkPath(\n  source: SourceInfo,\n  chunkPath: ChunkPath\n): Promise<any> {\n  const url = getChunkRelativeUrl(chunkPath);\n  return loadChunkByUrl(source, url);\n}\n\n/**\n * Returns an absolute url to an asset.\n */\nfunction createResolvePathFromModule(\n  resolver: (moduleId: string) => Exports\n): (moduleId: string) => string {\n  return function resolvePathFromModule(moduleId: string): string {\n    const exported = resolver(moduleId);\n    return exported?.default ?? exported;\n  };\n}\n\n/**\n * no-op for browser\n * @param modulePath\n */\nfunction resolveAbsolutePath(modulePath?: string): string {\n  return `/ROOT/${modulePath ?? \"\"}`;\n}\n\nfunction getWorkerBlobURL(chunks: ChunkPath[]): string {\n  let bootstrap = `self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};\nself.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(chunks.reverse().map(getChunkRelativeUrl), null, 2)};\nimportScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`;\n  let blob = new Blob([bootstrap], { type: \"text/javascript\" });\n  return URL.createObjectURL(blob);\n}\n\n/**\n * Adds a module to a chunk.\n */\nfunction addModuleToChunk(moduleId: ModuleId, chunkPath: ChunkPath) {\n  let moduleChunks = moduleChunksMap.get(moduleId);\n  if (!moduleChunks) {\n    moduleChunks = new Set([chunkPath]);\n    moduleChunksMap.set(moduleId, moduleChunks);\n  } else {\n    moduleChunks.add(chunkPath);\n  }\n\n  let chunkModules = chunkModulesMap.get(chunkPath);\n  if (!chunkModules) {\n    chunkModules = new Set([moduleId]);\n    chunkModulesMap.set(chunkPath, chunkModules);\n  } else {\n    chunkModules.add(moduleId);\n  }\n}\n\n/**\n * Returns the first chunk that included a module.\n * This is used by the Node.js backend, hence why it's marked as unused in this\n * file.\n */\nfunction getFirstModuleChunk(moduleId: ModuleId) {\n  const moduleChunkPaths = moduleChunksMap.get(moduleId);\n  if (moduleChunkPaths == null) {\n    return null;\n  }\n\n  return moduleChunkPaths.values().next().value;\n}\n\n/**\n * Instantiates a runtime module.\n */\nfunction instantiateRuntimeModule(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath\n): Module {\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath });\n}\n/**\n * Returns the URL relative to the origin where a chunk can be fetched from.\n */\nfunction getChunkRelativeUrl(chunkPath: ChunkPath | ChunkListPath): ChunkUrl {\n  return `${CHUNK_BASE_PATH}${chunkPath\n    .split(\"/\")\n    .map((p) => encodeURIComponent(p))\n    .join(\"/\")}${CHUNK_SUFFIX_PATH}` as ChunkUrl;\n}\n\n/**\n * Return the ChunkPath from a ChunkScript.\n */\nfunction getPathFromScript(chunkScript: ChunkPath | ChunkScript): ChunkPath;\nfunction getPathFromScript(chunkScript: ChunkListPath | ChunkListScript): ChunkListPath;\nfunction getPathFromScript(chunkScript: ChunkPath | ChunkListPath | ChunkScript | ChunkListScript): ChunkPath | ChunkListPath {\n  if (typeof chunkScript === \"string\") {\n    return chunkScript as ChunkPath | ChunkListPath;\n  }\n  const chunkUrl = typeof TURBOPACK_NEXT_CHUNK_URLS !== \"undefined\"\n    ? TURBOPACK_NEXT_CHUNK_URLS.pop()!\n    : chunkScript.getAttribute(\"src\")!;\n  const src = decodeURIComponent(chunkUrl.replace(/[?#].*$/, \"\"));\n  const path = src.startsWith(CHUNK_BASE_PATH) ? src.slice(CHUNK_BASE_PATH.length) : src;\n  return path as ChunkPath | ChunkListPath;\n}\n\n/**\n * Marks a chunk list as a runtime chunk list. There can be more than one\n * runtime chunk list. For instance, integration tests can have multiple chunk\n * groups loaded at runtime, each with its own chunk list.\n */\nfunction markChunkListAsRuntime(chunkListPath: ChunkListPath) {\n  runtimeChunkLists.add(chunkListPath);\n}\n\nfunction registerChunk([\n  chunkScript,\n  chunkModules,\n  runtimeParams,\n]: ChunkRegistration) {\n  const chunkPath = getPathFromScript(chunkScript);\n  for (const [moduleId, moduleFactory] of Object.entries(chunkModules)) {\n    if (!moduleFactories[moduleId]) {\n      moduleFactories[moduleId] = moduleFactory;\n    }\n    addModuleToChunk(moduleId, chunkPath);\n  }\n\n  return BACKEND.registerChunk(chunkPath, runtimeParams);\n}\n\nconst regexJsUrl = /\\.js(?:\\?[^#]*)?(?:#.*)?$/;\n/**\n * Checks if a given path/URL ends with .js, optionally followed by ?query or #fragment.\n */\nfunction isJs(chunkUrlOrPath: ChunkUrl | ChunkPath): boolean {\n  return regexJsUrl.test(chunkUrlOrPath);\n}\n\nconst regexCssUrl = /\\.css(?:\\?[^#]*)?(?:#.*)?$/;\n/**\n * Checks if a given path/URL ends with .css, optionally followed by ?query or #fragment.\n */\nfunction isCss(chunkUrl: ChunkUrl): boolean {\n  return regexCssUrl.test(chunkUrl);\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,oDAAoD,GAEpD,6CAA6C;AAC7C,yDAAyD;AAEzD,mEAAmE;AA8BnE,IAAA,AAAK,oCAAA;IACH;;;GAGC;IAED;;GAEC;IAED;;;GAGC;WAbE;EAAA;AA0CL,MAAM,kBAAmC,OAAO,MAAM,CAAC;AACvD;;CAEC,GACD,MAAM,iBAAgC,IAAI;AAC1C;;;;;;CAMC,GACD,MAAM,kBAAiD,IAAI;AAC3D;;CAEC,GACD,MAAM,kBAAiD,IAAI;AAC3D;;;;CAIC,GACD,MAAM,oBAAwC,IAAI;AAClD;;CAEC,GACD,MAAM,qBAAyD,IAAI;AACnE;;CAEC,GACD,MAAM,qBAAyD,IAAI;AAEnE,MAAM,mBAAuD,IAAI;AAEjE,MAAM,wBAA6D,IAAI;AAEvE,eAAe,UACb,MAAkB,EAClB,SAAoB;IAEpB,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,cAAc,QAAQ;IAC/B;IAEA,MAAM,eAAe,UAAU,QAAQ,IAAI,EAAE;IAC7C,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAC;QACxC,IAAI,eAAe,CAAC,SAAS,EAAE,OAAO;QACtC,OAAO,iBAAiB,GAAG,CAAC;IAC9B;IACA,IAAI,gBAAgB,MAAM,GAAG,KAAK,gBAAgB,KAAK,CAAC,CAAC,IAAM,IAAI;QACjE,uFAAuF;QACvF,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA,MAAM,2BAA2B,UAAU,YAAY,IAAI,EAAE;IAC7D,MAAM,uBAAuB,yBAC1B,GAAG,CAAC,CAAC;QACJ,yCAAyC;QACzC,8CAA8C;QAC9C,OAAO,sBAAsB,GAAG,CAAC;IACnC,GACC,MAAM,CAAC,CAAC,IAAM;IAEjB,IAAI;IACJ,IAAI,qBAAqB,MAAM,GAAG,GAAG;QACnC,oDAAoD;QAEpD,IAAI,qBAAqB,MAAM,KAAK,yBAAyB,MAAM,EAAE;YACnE,+FAA+F;YAC/F,OAAO,QAAQ,GAAG,CAAC;QACrB;QAEA,MAAM,qBAAqC,IAAI;QAC/C,KAAK,MAAM,eAAe,yBAA0B;YAClD,IAAI,CAAC,sBAAsB,GAAG,CAAC,cAAc;gBAC3C,mBAAmB,GAAG,CAAC;YACzB;QACF;QAEA,KAAK,MAAM,qBAAqB,mBAAoB;YAClD,MAAM,UAAU,cAAc,QAAQ;YAEtC,sBAAsB,GAAG,CAAC,mBAAmB;YAE7C,qBAAqB,IAAI,CAAC;QAC5B;QAEA,UAAU,QAAQ,GAAG,CAAC;IACxB,OAAO;QACL,UAAU,cAAc,QAAQ,UAAU,IAAI;QAE9C,wFAAwF;QACxF,KAAK,MAAM,uBAAuB,yBAA0B;YAC1D,IAAI,CAAC,sBAAsB,GAAG,CAAC,sBAAsB;gBACnD,sBAAsB,GAAG,CAAC,qBAAqB;YACjD;QACF;IACF;IAEA,KAAK,MAAM,YAAY,aAAc;QACnC,IAAI,CAAC,iBAAiB,GAAG,CAAC,WAAW;YACnC,qIAAqI;YACrI,yGAAyG;YACzG,iBAAiB,GAAG,CAAC,UAAU;QACjC;IACF;IAEA,OAAO;AACT;AAEA,eAAe,eAAe,MAAkB,EAAE,QAAkB;IAClE,IAAI;QACF,MAAM,QAAQ,SAAS,CAAC,UAAU;IACpC,EAAE,OAAO,OAAO;QACd,IAAI;QACJ,OAAQ,OAAO,IAAI;YACjB;gBACE,aAAa,CAAC,iCAAiC,EAAE,OAAO,SAAS,EAAE;gBACnE;YACF;gBACE,aAAa,CAAC,YAAY,EAAE,OAAO,QAAQ,EAAE;gBAC7C;YACF;gBACE,aAAa;gBACb;YACF;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;QACA,MAAM,IAAI,MACR,CAAC,qBAAqB,EAAE,SAAS,CAAC,EAAE,aAClC,QAAQ,CAAC,EAAE,EAAE,OAAO,GAAG,IACvB,EACF,QACI;YACE,OAAO;QACT,IACA;IAER;AACF;AAEA,eAAe,cACb,MAAkB,EAClB,SAAoB;IAEpB,MAAM,MAAM,oBAAoB;IAChC,OAAO,eAAe,QAAQ;AAChC;AAEA;;CAEC,GACD,SAAS,4BACP,QAAuC;IAEvC,OAAO,SAAS,sBAAsB,QAAgB;QACpD,MAAM,WAAW,SAAS;QAC1B,OAAO,UAAU,WAAW;IAC9B;AACF;AAEA;;;CAGC,GACD,SAAS,oBAAoB,UAAmB;IAC9C,OAAO,CAAC,MAAM,EAAE,cAAc,IAAI;AACpC;AAEA,SAAS,iBAAiB,MAAmB;IAC3C,IAAI,YAAY,CAAC,iCAAiC,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,EAAE;iCACrD,EAAE,KAAK,SAAS,CAAC,OAAO,OAAO,GAAG,GAAG,CAAC,sBAAsB,MAAM,GAAG;wGACE,CAAC;IACvG,IAAI,OAAO,IAAI,KAAK;QAAC;KAAU,EAAE;QAAE,MAAM;IAAkB;IAC3D,OAAO,IAAI,eAAe,CAAC;AAC7B;AAEA;;CAEC,GACD,SAAS,iBAAiB,QAAkB,EAAE,SAAoB;IAChE,IAAI,eAAe,gBAAgB,GAAG,CAAC;IACvC,IAAI,CAAC,cAAc;QACjB,eAAe,IAAI,IAAI;YAAC;SAAU;QAClC,gBAAgB,GAAG,CAAC,UAAU;IAChC,OAAO;QACL,aAAa,GAAG,CAAC;IACnB;IAEA,IAAI,eAAe,gBAAgB,GAAG,CAAC;IACvC,IAAI,CAAC,cAAc;QACjB,eAAe,IAAI,IAAI;YAAC;SAAS;QACjC,gBAAgB,GAAG,CAAC,WAAW;IACjC,OAAO;QACL,aAAa,GAAG,CAAC;IACnB;AACF;AAEA;;;;CAIC,GACD,SAAS,oBAAoB,QAAkB;IAC7C,MAAM,mBAAmB,gBAAgB,GAAG,CAAC;IAC7C,IAAI,oBAAoB,MAAM;QAC5B,OAAO;IACT;IAEA,OAAO,iBAAiB,MAAM,GAAG,IAAI,GAAG,KAAK;AAC/C;AAEA;;CAEC,GACD,SAAS,yBACP,QAAkB,EAClB,SAAoB;IAEpB,OAAO,kBAAkB,UAAU;QAAE,IAAI;QAAsB;IAAU;AAC3E;AACA;;CAEC,GACD,SAAS,oBAAoB,SAAoC;IAC/D,OAAO,GAAG,kBAAkB,UACzB,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,mBAAmB,IAC9B,IAAI,CAAC,OAAO,mBAAmB;AACpC;AAOA,SAAS,kBAAkB,WAAsE;IAC/F,IAAI,OAAO,gBAAgB,UAAU;QACnC,OAAO;IACT;IACA,MAAM,WAAW,OAAO,8BAA8B,cAClD,0BAA0B,GAAG,KAC7B,YAAY,YAAY,CAAC;IAC7B,MAAM,MAAM,mBAAmB,SAAS,OAAO,CAAC,WAAW;IAC3D,MAAM,OAAO,IAAI,UAAU,CAAC,mBAAmB,IAAI,KAAK,CAAC,gBAAgB,MAAM,IAAI;IACnF,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,aAA4B;IAC1D,kBAAkB,GAAG,CAAC;AACxB;AAEA,SAAS,cAAc,CACrB,aACA,cACA,cACkB;IAClB,MAAM,YAAY,kBAAkB;IACpC,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,cAAe;QACpE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;YAC9B,eAAe,CAAC,SAAS,GAAG;QAC9B;QACA,iBAAiB,UAAU;IAC7B;IAEA,OAAO,QAAQ,aAAa,CAAC,WAAW;AAC1C;AAEA,MAAM,aAAa;AACnB;;CAEC,GACD,SAAS,KAAK,cAAoC;IAChD,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,MAAM,cAAc;AACpB;;CAEC,GACD,SAAS,MAAM,QAAkB;IAC/B,OAAO,YAAY,IAAI,CAAC;AAC1B", "ignoreList": [0]}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/base/dev-base.ts"], "sourcesContent": ["/// <reference path=\"./dev-globals.d.ts\" />\n/// <reference path=\"./dev-protocol.d.ts\" />\n/// <reference path=\"./dev-extensions.ts\" />\n\n/**\n * This file contains runtime types and functions that are shared between all\n * Turbopack *development* ECMAScript runtimes.\n *\n * It will be appended to the runtime code of each runtime right after the\n * shared runtime utils.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\nconst devModuleCache: ModuleCache<HotModule> = Object.create(null);\n\n// This file must not use `import` and `export` statements. Otherwise, it\n// becomes impossible to augment interfaces declared in `<reference>`d files\n// (e.g. `Module`). Hence, the need for `import()` here.\ntype RefreshRuntimeGlobals =\n  import(\"@next/react-refresh-utils/dist/runtime\").RefreshRuntimeGlobals;\n\ndeclare var $RefreshHelpers$: RefreshRuntimeGlobals[\"$RefreshHelpers$\"];\ndeclare var $RefreshReg$: RefreshRuntimeGlobals[\"$RefreshReg$\"];\ndeclare var $RefreshSig$: RefreshRuntimeGlobals[\"$RefreshSig$\"];\ndeclare var $RefreshInterceptModuleExecution$:\n  | RefreshRuntimeGlobals[\"$RefreshInterceptModuleExecution$\"];\n\ntype RefreshContext = {\n  register: RefreshRuntimeGlobals[\"$RefreshReg$\"];\n  signature: RefreshRuntimeGlobals[\"$RefreshSig$\"];\n  registerExports: typeof registerExportsAndSetupBoundaryForReactRefresh;\n};\n\ntype RefreshHelpers = RefreshRuntimeGlobals[\"$RefreshHelpers$\"];\n\ninterface TurbopackDevBaseContext extends TurbopackBaseContext<Module> {\n  k: RefreshContext;\n  R: ResolvePathFromModule;\n}\n\ninterface TurbopackDevContext extends TurbopackDevBaseContext {}\n\ntype ModuleFactory = (\n  this: Module[\"exports\"],\n  context: TurbopackDevBaseContext\n) => undefined\n\ninterface DevRuntimeBackend {\n  reloadChunk?: (chunkUrl: ChunkUrl) => Promise<void>;\n  unloadChunk?: (chunkUrl: ChunkUrl) => void;\n  restart: () => void;\n}\n\nclass UpdateApplyError extends Error {\n  name = \"UpdateApplyError\";\n\n  dependencyChain: string[];\n\n  constructor(message: string, dependencyChain: string[]) {\n    super(message);\n    this.dependencyChain = dependencyChain;\n  }\n}\n\n/**\n * Maps module IDs to persisted data between executions of their hot module\n * implementation (`hot.data`).\n */\nconst moduleHotData: Map<ModuleId, HotData> = new Map();\n/**\n * Maps module instances to their hot module state.\n */\nconst moduleHotState: Map<Module, HotState> = new Map();\n/**\n * Modules that call `module.hot.invalidate()` (while being updated).\n */\nconst queuedInvalidatedModules: Set<ModuleId> = new Set();\n\n/**\n * Gets or instantiates a runtime module.\n */\n// @ts-ignore\nfunction getOrInstantiateRuntimeModule(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath,\n): Module {\n  const module = devModuleCache[moduleId];\n  if (module) {\n    if (module.error) {\n      throw module.error;\n    }\n    return module;\n  }\n\n  // @ts-ignore\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath });\n}\n\n/**\n * Retrieves a module from the cache, or instantiate it if it is not cached.\n */\n// @ts-ignore Defined in `runtime-utils.ts`\nconst getOrInstantiateModuleFromParent: GetOrInstantiateModuleFromParent<HotModule> = (\n  id,\n  sourceModule,\n) => {\n  if (!sourceModule.hot.active) {\n    console.warn(\n      `Unexpected import of module ${id} from module ${sourceModule.id}, which was deleted by an HMR update`\n    );\n  }\n\n  const module = devModuleCache[id];\n\n  if (sourceModule.children.indexOf(id) === -1) {\n    sourceModule.children.push(id);\n  }\n\n  if (module) {\n    if (module.parents.indexOf(sourceModule.id) === -1) {\n      module.parents.push(sourceModule.id);\n    }\n\n    return module;\n  }\n\n  return instantiateModule(id, {\n    type: SourceType.Parent,\n    parentId: sourceModule.id,\n  });\n};\n\n// @ts-ignore Defined in `runtime-base.ts`\nfunction instantiateModule(id: ModuleId, source: SourceInfo): Module {\n  const moduleFactory = moduleFactories[id];\n  if (typeof moduleFactory !== \"function\") {\n    // This can happen if modules incorrectly handle HMR disposes/updates,\n    // e.g. when they keep a `setTimeout` around which still executes old code\n    // and contains e.g. a `require(\"something\")` call.\n    let instantiationReason;\n    switch (source.type) {\n      case SourceType.Runtime:\n        instantiationReason = `as a runtime entry of chunk ${source.chunkPath}`;\n        break;\n      case SourceType.Parent:\n        instantiationReason = `because it was required from module ${source.parentId}`;\n        break;\n      case SourceType.Update:\n        instantiationReason = \"because of an HMR update\";\n        break;\n      default:\n        invariant(source, (source) => `Unknown source type: ${source?.type}`);\n    }\n    throw new Error(\n      `Module ${id} was instantiated ${instantiationReason}, but the module factory is not available. It might have been deleted in an HMR update.`\n    );\n  }\n\n  const hotData = moduleHotData.get(id)!;\n  const { hot, hotState } = createModuleHot(id, hotData);\n\n  let parents: ModuleId[];\n  switch (source.type) {\n    case SourceType.Runtime:\n      runtimeModules.add(id);\n      parents = [];\n      break;\n    case SourceType.Parent:\n      // No need to add this module as a child of the parent module here, this\n      // has already been taken care of in `getOrInstantiateModuleFromParent`.\n      parents = [source.parentId];\n      break;\n    case SourceType.Update:\n      parents = source.parents || [];\n      break;\n    default:\n      invariant(source, (source) => `Unknown source type: ${source?.type}`);\n  }\n\n  const module: HotModule = {\n    exports: {},\n    error: undefined,\n    loaded: false,\n    id,\n    parents,\n    children: [],\n    namespaceObject: undefined,\n    hot,\n  };\n\n  devModuleCache[id] = module;\n  moduleHotState.set(module, hotState);\n\n  // NOTE(alexkirsz) This can fail when the module encounters a runtime error.\n  try {\n    const sourceInfo: SourceInfo = { type: SourceType.Parent, parentId: id };\n\n    runModuleExecutionHooks(module, (refresh) => {\n      const r = commonJsRequire.bind(null, module);\n      moduleFactory.call(\n        module.exports,\n        augmentContext({\n          a: asyncModule.bind(null, module),\n          e: module.exports,\n          r: commonJsRequire.bind(null, module),\n          t: runtimeRequire,\n          f: moduleContext,\n          i: esmImport.bind(null, module),\n          s: esmExport.bind(null, module, module.exports),\n          j: dynamicExport.bind(null, module, module.exports),\n          v: exportValue.bind(null, module),\n          n: exportNamespace.bind(null, module),\n          m: module,\n          c: devModuleCache,\n          M: moduleFactories,\n          l: loadChunk.bind(null, sourceInfo),\n          L: loadChunkByUrl.bind(null, sourceInfo),\n          w: loadWebAssembly.bind(null, sourceInfo),\n          u: loadWebAssemblyModule.bind(null, sourceInfo),\n          g: globalThis,\n          P: resolveAbsolutePath,\n          U: relativeURL,\n          k: refresh,\n          R: createResolvePathFromModule(r),\n          b: getWorkerBlobURL,\n          z: requireStub,\n          d: typeof module.id === \"string\" ? module.id.replace(/(^|\\/)\\/+$/, \"\") : module.id\n        })\n      );\n    });\n  } catch (error) {\n    module.error = error as any;\n    throw error;\n  }\n\n  module.loaded = true;\n  if (module.namespaceObject && module.exports !== module.namespaceObject) {\n    // in case of a circular dependency: cjs1 -> esm2 -> cjs1\n    interopEsm(module.exports, module.namespaceObject);\n  }\n\n  return module;\n}\n\n/**\n * NOTE(alexkirsz) Webpack has a \"module execution\" interception hook that\n * Next.js' React Refresh runtime hooks into to add module context to the\n * refresh registry.\n */\nfunction runModuleExecutionHooks(\n  module: Module,\n  executeModule: (ctx: RefreshContext) => void\n) {\n  const cleanupReactRefreshIntercept =\n    typeof globalThis.$RefreshInterceptModuleExecution$ === \"function\"\n      ? globalThis.$RefreshInterceptModuleExecution$(module.id)\n      : () => {};\n\n  try {\n    executeModule({\n      register: globalThis.$RefreshReg$,\n      signature: globalThis.$RefreshSig$,\n      registerExports: registerExportsAndSetupBoundaryForReactRefresh,\n    });\n  } catch (e) {\n    throw e;\n  } finally {\n    // Always cleanup the intercept, even if module execution failed.\n    cleanupReactRefreshIntercept();\n  }\n}\n\n/**\n * This is adapted from https://github.com/vercel/next.js/blob/3466862d9dc9c8bb3131712134d38757b918d1c0/packages/react-refresh-utils/internal/ReactRefreshModule.runtime.ts\n */\nfunction registerExportsAndSetupBoundaryForReactRefresh(\n  module: HotModule,\n  helpers: RefreshHelpers\n) {\n  const currentExports = module.exports;\n  const prevExports = module.hot.data.prevExports ?? null;\n\n  helpers.registerExportsForReactRefresh(currentExports, module.id);\n\n  // A module can be accepted automatically based on its exports, e.g. when\n  // it is a Refresh Boundary.\n  if (helpers.isReactRefreshBoundary(currentExports)) {\n    // Save the previous exports on update, so we can compare the boundary\n    // signatures.\n    module.hot.dispose((data) => {\n      data.prevExports = currentExports;\n    });\n    // Unconditionally accept an update to this module, we'll check if it's\n    // still a Refresh Boundary later.\n    module.hot.accept();\n\n    // This field is set when the previous version of this module was a\n    // Refresh Boundary, letting us know we need to check for invalidation or\n    // enqueue an update.\n    if (prevExports !== null) {\n      // A boundary can become ineligible if its exports are incompatible\n      // with the previous exports.\n      //\n      // For example, if you add/remove/change exports, we'll want to\n      // re-execute the importing modules, and force those components to\n      // re-render. Similarly, if you convert a class component to a\n      // function, we want to invalidate the boundary.\n      if (\n        helpers.shouldInvalidateReactRefreshBoundary(\n          helpers.getRefreshBoundarySignature(prevExports),\n          helpers.getRefreshBoundarySignature(currentExports)\n        )\n      ) {\n        module.hot.invalidate();\n      } else {\n        helpers.scheduleUpdate();\n      }\n    }\n  } else {\n    // Since we just executed the code for the module, it's possible that the\n    // new exports made it ineligible for being a boundary.\n    // We only care about the case when we were _previously_ a boundary,\n    // because we already accepted this update (accidental side effect).\n    const isNoLongerABoundary = prevExports !== null;\n    if (isNoLongerABoundary) {\n      module.hot.invalidate();\n    }\n  }\n}\n\nfunction formatDependencyChain(dependencyChain: ModuleId[]): string {\n  return `Dependency chain: ${dependencyChain.join(\" -> \")}`;\n}\n\nfunction computeOutdatedModules(\n  added: Map<ModuleId, EcmascriptModuleEntry | undefined>,\n  modified: Map<ModuleId, EcmascriptModuleEntry>\n): {\n  outdatedModules: Set<ModuleId>;\n  newModuleFactories: Map<ModuleId, ModuleFactory>;\n} {\n  const newModuleFactories = new Map<ModuleId, ModuleFactory>();\n\n  for (const [moduleId, entry] of added) {\n    if (entry != null) {\n      newModuleFactories.set(moduleId, _eval(entry));\n    }\n  }\n\n  const outdatedModules = computedInvalidatedModules(modified.keys());\n\n  for (const [moduleId, entry] of modified) {\n    newModuleFactories.set(moduleId, _eval(entry));\n  }\n\n  return { outdatedModules, newModuleFactories };\n}\n\nfunction computedInvalidatedModules(\n  invalidated: Iterable<ModuleId>\n): Set<ModuleId> {\n  const outdatedModules = new Set<ModuleId>();\n\n  for (const moduleId of invalidated) {\n    const effect = getAffectedModuleEffects(moduleId);\n\n    switch (effect.type) {\n      case \"unaccepted\":\n        throw new UpdateApplyError(\n          `cannot apply update: unaccepted module. ${formatDependencyChain(\n            effect.dependencyChain\n          )}.`,\n          effect.dependencyChain\n        );\n      case \"self-declined\":\n        throw new UpdateApplyError(\n          `cannot apply update: self-declined module. ${formatDependencyChain(\n            effect.dependencyChain\n          )}.`,\n          effect.dependencyChain\n        );\n      case \"accepted\":\n        for (const outdatedModuleId of effect.outdatedModules) {\n          outdatedModules.add(outdatedModuleId);\n        }\n        break;\n      // TODO(alexkirsz) Dependencies: handle dependencies effects.\n      default:\n        invariant(effect, (effect) => `Unknown effect type: ${effect?.type}`);\n    }\n  }\n\n  return outdatedModules;\n}\n\nfunction computeOutdatedSelfAcceptedModules(\n  outdatedModules: Iterable<ModuleId>\n): { moduleId: ModuleId; errorHandler: true | Function }[] {\n  const outdatedSelfAcceptedModules: { moduleId: ModuleId; errorHandler: true | Function }[] = [];\n  for (const moduleId of outdatedModules) {\n    const module = devModuleCache[moduleId];\n    const hotState = moduleHotState.get(module)!;\n    if (module && hotState.selfAccepted && !hotState.selfInvalidated) {\n      outdatedSelfAcceptedModules.push({\n        moduleId,\n        errorHandler: hotState.selfAccepted,\n      });\n    }\n  }\n  return outdatedSelfAcceptedModules;\n}\n\n/**\n * Adds, deletes, and moves modules between chunks. This must happen before the\n * dispose phase as it needs to know which modules were removed from all chunks,\n * which we can only compute *after* taking care of added and moved modules.\n */\nfunction updateChunksPhase(\n  chunksAddedModules: Map<ChunkPath, Set<ModuleId>>,\n  chunksDeletedModules: Map<ChunkPath, Set<ModuleId>>\n): { disposedModules: Set<ModuleId> } {\n  for (const [chunkPath, addedModuleIds] of chunksAddedModules) {\n    for (const moduleId of addedModuleIds) {\n      addModuleToChunk(moduleId, chunkPath);\n    }\n  }\n\n  const disposedModules: Set<ModuleId> = new Set();\n  for (const [chunkPath, addedModuleIds] of chunksDeletedModules) {\n    for (const moduleId of addedModuleIds) {\n      if (removeModuleFromChunk(moduleId, chunkPath)) {\n        disposedModules.add(moduleId);\n      }\n    }\n  }\n\n  return { disposedModules };\n}\n\nfunction disposePhase(\n  outdatedModules: Iterable<ModuleId>,\n  disposedModules: Iterable<ModuleId>\n): { outdatedModuleParents: Map<ModuleId, Array<ModuleId>> } {\n  for (const moduleId of outdatedModules) {\n    disposeModule(moduleId, \"replace\");\n  }\n\n  for (const moduleId of disposedModules) {\n    disposeModule(moduleId, \"clear\");\n  }\n\n  // Removing modules from the module cache is a separate step.\n  // We also want to keep track of previous parents of the outdated modules.\n  const outdatedModuleParents = new Map();\n  for (const moduleId of outdatedModules) {\n    const oldModule = devModuleCache[moduleId];\n    outdatedModuleParents.set(moduleId, oldModule?.parents);\n    delete devModuleCache[moduleId];\n  }\n\n  // TODO(alexkirsz) Dependencies: remove outdated dependency from module\n  // children.\n\n  return { outdatedModuleParents };\n}\n\n/**\n * Disposes of an instance of a module.\n *\n * Returns the persistent hot data that should be kept for the next module\n * instance.\n *\n * NOTE: mode = \"replace\" will not remove modules from the devModuleCache\n * This must be done in a separate step afterwards.\n * This is important because all modules need to be disposed to update the\n * parent/child relationships before they are actually removed from the devModuleCache.\n * If this was done in this method, the following disposeModule calls won't find\n * the module from the module id in the cache.\n */\nfunction disposeModule(moduleId: ModuleId, mode: \"clear\" | \"replace\") {\n  const module = devModuleCache[moduleId];\n  if (!module) {\n    return;\n  }\n\n  const hotState = moduleHotState.get(module)!;\n  const data = {};\n\n  // Run the `hot.dispose` handler, if any, passing in the persistent\n  // `hot.data` object.\n  for (const disposeHandler of hotState.disposeHandlers) {\n    disposeHandler(data);\n  }\n\n  // This used to warn in `getOrInstantiateModuleFromParent` when a disposed\n  // module is still importing other modules.\n  module.hot.active = false;\n\n  moduleHotState.delete(module);\n\n  // TODO(alexkirsz) Dependencies: delete the module from outdated deps.\n\n  // Remove the disposed module from its children's parent list.\n  // It will be added back once the module re-instantiates and imports its\n  // children again.\n  for (const childId of module.children) {\n    const child = devModuleCache[childId];\n    if (!child) {\n      continue;\n    }\n\n    const idx = child.parents.indexOf(module.id);\n    if (idx >= 0) {\n      child.parents.splice(idx, 1);\n    }\n  }\n\n  switch (mode) {\n    case \"clear\":\n      delete devModuleCache[module.id];\n      moduleHotData.delete(module.id);\n      break;\n    case \"replace\":\n      moduleHotData.set(module.id, data);\n      break;\n    default:\n      invariant(mode, (mode) => `invalid mode: ${mode}`);\n  }\n}\n\nfunction applyPhase(\n  outdatedSelfAcceptedModules: {\n    moduleId: ModuleId;\n    errorHandler: true | Function;\n  }[],\n  newModuleFactories: Map<ModuleId, ModuleFactory>,\n  outdatedModuleParents: Map<ModuleId, Array<ModuleId>>,\n  reportError: (err: any) => void\n) {\n  // Update module factories.\n  for (const [moduleId, factory] of newModuleFactories.entries()) {\n    moduleFactories[moduleId] = factory;\n  }\n\n  // TODO(alexkirsz) Run new runtime entries here.\n\n  // TODO(alexkirsz) Dependencies: call accept handlers for outdated deps.\n\n  // Re-instantiate all outdated self-accepted modules.\n  for (const { moduleId, errorHandler } of outdatedSelfAcceptedModules) {\n    try {\n      instantiateModule(moduleId, {\n        type: SourceType.Update,\n        parents: outdatedModuleParents.get(moduleId),\n      });\n    } catch (err) {\n      if (typeof errorHandler === \"function\") {\n        try {\n          errorHandler(err, { moduleId, module: devModuleCache[moduleId] });\n        } catch (err2) {\n          reportError(err2);\n          reportError(err);\n        }\n      } else {\n        reportError(err);\n      }\n    }\n  }\n}\n\nfunction applyUpdate(update: PartialUpdate) {\n  switch (update.type) {\n    case \"ChunkListUpdate\":\n      applyChunkListUpdate(update);\n      break;\n    default:\n      invariant(update, (update) => `Unknown update type: ${update.type}`);\n  }\n}\n\nfunction applyChunkListUpdate(update: ChunkListUpdate) {\n  if (update.merged != null) {\n    for (const merged of update.merged) {\n      switch (merged.type) {\n        case \"EcmascriptMergedUpdate\":\n          applyEcmascriptMergedUpdate(merged);\n          break;\n        default:\n          invariant(merged, (merged) => `Unknown merged type: ${merged.type}`);\n      }\n    }\n  }\n\n  if (update.chunks != null) {\n    for (const [chunkPath, chunkUpdate] of Object.entries(update.chunks) as Array<[ChunkPath, ChunkUpdate]>) {\n      const chunkUrl = getChunkRelativeUrl(chunkPath);\n\n      switch (chunkUpdate.type) {\n        case \"added\":\n          BACKEND.loadChunk(chunkUrl, { type: SourceType.Update });\n          break;\n        case \"total\":\n          DEV_BACKEND.reloadChunk?.(chunkUrl);\n          break;\n        case \"deleted\":\n          DEV_BACKEND.unloadChunk?.(chunkUrl);\n          break;\n        case \"partial\":\n          invariant(\n            chunkUpdate.instruction,\n            (instruction) =>\n              `Unknown partial instruction: ${JSON.stringify(instruction)}.`\n          );\n          break;\n        default:\n          invariant(\n            chunkUpdate,\n            (chunkUpdate) => `Unknown chunk update type: ${chunkUpdate.type}`\n          );\n      }\n    }\n  }\n}\n\nfunction applyEcmascriptMergedUpdate(update: EcmascriptMergedUpdate) {\n  const { entries = {}, chunks = {} } = update;\n  const { added, modified, chunksAdded, chunksDeleted } = computeChangedModules(\n    entries,\n    chunks\n  );\n  const { outdatedModules, newModuleFactories } = computeOutdatedModules(\n    added,\n    modified\n  );\n  const { disposedModules } = updateChunksPhase(chunksAdded, chunksDeleted);\n\n  applyInternal(outdatedModules, disposedModules, newModuleFactories);\n}\n\nfunction applyInvalidatedModules(outdatedModules: Set<ModuleId>) {\n  if (queuedInvalidatedModules.size > 0) {\n    computedInvalidatedModules(queuedInvalidatedModules).forEach((moduleId) => {\n      outdatedModules.add(moduleId);\n    });\n\n    queuedInvalidatedModules.clear();\n  }\n\n  return outdatedModules;\n}\n\nfunction applyInternal(\n  outdatedModules: Set<ModuleId>,\n  disposedModules: Iterable<ModuleId>,\n  newModuleFactories: Map<ModuleId, ModuleFactory>\n) {\n  outdatedModules = applyInvalidatedModules(outdatedModules);\n\n  const outdatedSelfAcceptedModules =\n    computeOutdatedSelfAcceptedModules(outdatedModules);\n\n  const { outdatedModuleParents } = disposePhase(\n    outdatedModules,\n    disposedModules\n  );\n\n  // we want to continue on error and only throw the error after we tried applying all updates\n  let error: any;\n\n  function reportError(err: any) {\n    if (!error) error = err;\n  }\n\n  applyPhase(\n    outdatedSelfAcceptedModules,\n    newModuleFactories,\n    outdatedModuleParents,\n    reportError\n  );\n\n  if (error) {\n    throw error;\n  }\n\n  if (queuedInvalidatedModules.size > 0) {\n    applyInternal(new Set(), [], new Map());\n  }\n}\n\nfunction computeChangedModules(\n  entries: Record<ModuleId, EcmascriptModuleEntry>,\n  updates: Record<ChunkPath, EcmascriptMergedChunkUpdate>\n): {\n  added: Map<ModuleId, EcmascriptModuleEntry | undefined>;\n  modified: Map<ModuleId, EcmascriptModuleEntry>;\n  deleted: Set<ModuleId>;\n  chunksAdded: Map<ChunkPath, Set<ModuleId>>;\n  chunksDeleted: Map<ChunkPath, Set<ModuleId>>;\n} {\n  const chunksAdded = new Map();\n  const chunksDeleted = new Map();\n  const added: Map<ModuleId, EcmascriptModuleEntry> = new Map();\n  const modified = new Map();\n  const deleted: Set<ModuleId> = new Set();\n\n  for (const [chunkPath, mergedChunkUpdate] of Object.entries(updates) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\n    switch (mergedChunkUpdate.type) {\n      case \"added\": {\n        const updateAdded = new Set(mergedChunkUpdate.modules);\n        for (const moduleId of updateAdded) {\n          added.set(moduleId, entries[moduleId]);\n        }\n        chunksAdded.set(chunkPath, updateAdded);\n        break;\n      }\n      case \"deleted\": {\n        // We could also use `mergedChunkUpdate.modules` here.\n        const updateDeleted = new Set(chunkModulesMap.get(chunkPath));\n        for (const moduleId of updateDeleted) {\n          deleted.add(moduleId);\n        }\n        chunksDeleted.set(chunkPath, updateDeleted);\n        break;\n      }\n      case \"partial\": {\n        const updateAdded = new Set(mergedChunkUpdate.added);\n        const updateDeleted = new Set(mergedChunkUpdate.deleted);\n        for (const moduleId of updateAdded) {\n          added.set(moduleId, entries[moduleId]);\n        }\n        for (const moduleId of updateDeleted) {\n          deleted.add(moduleId);\n        }\n        chunksAdded.set(chunkPath, updateAdded);\n        chunksDeleted.set(chunkPath, updateDeleted);\n        break;\n      }\n      default:\n        invariant(\n          mergedChunkUpdate,\n          (mergedChunkUpdate) =>\n            `Unknown merged chunk update type: ${mergedChunkUpdate.type}`\n        );\n    }\n  }\n\n  // If a module was added from one chunk and deleted from another in the same update,\n  // consider it to be modified, as it means the module was moved from one chunk to another\n  // AND has new code in a single update.\n  for (const moduleId of added.keys()) {\n    if (deleted.has(moduleId)) {\n      added.delete(moduleId);\n      deleted.delete(moduleId);\n    }\n  }\n\n  for (const [moduleId, entry] of Object.entries(entries)) {\n    // Modules that haven't been added to any chunk but have new code are considered\n    // to be modified.\n    // This needs to be under the previous loop, as we need it to get rid of modules\n    // that were added and deleted in the same update.\n    if (!added.has(moduleId)) {\n      modified.set(moduleId, entry);\n    }\n  }\n\n  return { added, deleted, modified, chunksAdded, chunksDeleted };\n}\n\ntype ModuleEffect =\n  | {\n      type: \"unaccepted\";\n      dependencyChain: ModuleId[];\n    }\n  | {\n      type: \"self-declined\";\n      dependencyChain: ModuleId[];\n      moduleId: ModuleId;\n    }\n  | {\n      type: \"accepted\";\n      moduleId: ModuleId;\n      outdatedModules: Set<ModuleId>;\n    };\n\nfunction getAffectedModuleEffects(moduleId: ModuleId): ModuleEffect {\n  const outdatedModules: Set<ModuleId> = new Set();\n\n  type QueueItem = { moduleId?: ModuleId; dependencyChain: ModuleId[] };\n\n  const queue: QueueItem[] = [\n    {\n      moduleId,\n      dependencyChain: [],\n    },\n  ];\n\n  let nextItem;\n  while ((nextItem = queue.shift())) {\n    const { moduleId, dependencyChain } = nextItem;\n\n    if (moduleId != null) {\n      if (outdatedModules.has(moduleId)) {\n        // Avoid infinite loops caused by cycles between modules in the dependency chain.\n        continue;\n      }\n\n      outdatedModules.add(moduleId);\n    }\n\n    // We've arrived at the runtime of the chunk, which means that nothing\n    // else above can accept this update.\n    if (moduleId === undefined) {\n      return {\n        type: \"unaccepted\",\n        dependencyChain,\n      };\n    }\n\n    const module = devModuleCache[moduleId];\n    const hotState = moduleHotState.get(module)!;\n\n    if (\n      // The module is not in the cache. Since this is a \"modified\" update,\n      // it means that the module was never instantiated before.\n      !module || // The module accepted itself without invalidating globalThis.\n      // TODO is that right?\n      (hotState.selfAccepted && !hotState.selfInvalidated)\n    ) {\n      continue;\n    }\n\n    if (hotState.selfDeclined) {\n      return {\n        type: \"self-declined\",\n        dependencyChain,\n        moduleId,\n      };\n    }\n\n    if (runtimeModules.has(moduleId)) {\n      queue.push({\n        moduleId: undefined,\n        dependencyChain: [...dependencyChain, moduleId],\n      });\n      continue;\n    }\n\n    for (const parentId of module.parents) {\n      const parent = devModuleCache[parentId];\n\n      if (!parent) {\n        // TODO(alexkirsz) Is this even possible?\n        continue;\n      }\n\n      // TODO(alexkirsz) Dependencies: check accepted and declined\n      // dependencies here.\n\n      queue.push({\n        moduleId: parentId,\n        dependencyChain: [...dependencyChain, moduleId],\n      });\n    }\n  }\n\n  return {\n    type: \"accepted\",\n    moduleId,\n    outdatedModules,\n  };\n}\n\nfunction handleApply(chunkListPath: ChunkListPath, update: ServerMessage) {\n  switch (update.type) {\n    case \"partial\": {\n      // This indicates that the update is can be applied to the current state of the application.\n      applyUpdate(update.instruction);\n      break;\n    }\n    case \"restart\": {\n      // This indicates that there is no way to apply the update to the\n      // current state of the application, and that the application must be\n      // restarted.\n      DEV_BACKEND.restart();\n      break;\n    }\n    case \"notFound\": {\n      // This indicates that the chunk list no longer exists: either the dynamic import which created it was removed,\n      // or the page itself was deleted.\n      // If it is a dynamic import, we simply discard all modules that the chunk has exclusive access to.\n      // If it is a runtime chunk list, we restart the application.\n      if (runtimeChunkLists.has(chunkListPath)) {\n        DEV_BACKEND.restart();\n      } else {\n        disposeChunkList(chunkListPath);\n      }\n      break;\n    }\n    default:\n      throw new Error(`Unknown update type: ${update.type}`);\n  }\n}\n\nfunction createModuleHot(\n  moduleId: ModuleId,\n  hotData: HotData\n): { hot: Hot; hotState: HotState } {\n  const hotState: HotState = {\n    selfAccepted: false,\n    selfDeclined: false,\n    selfInvalidated: false,\n    disposeHandlers: [],\n  };\n\n  const hot: Hot = {\n    // TODO(alexkirsz) This is not defined in the HMR API. It was used to\n    // decide whether to warn whenever an HMR-disposed module required other\n    // modules. We might want to remove it.\n    active: true,\n\n    data: hotData ?? {},\n\n    // TODO(alexkirsz) Support full (dep, callback, errorHandler) form.\n    accept: (\n      modules?: string | string[] | AcceptErrorHandler,\n      _callback?: AcceptCallback,\n      _errorHandler?: AcceptErrorHandler\n    ) => {\n      if (modules === undefined) {\n        hotState.selfAccepted = true;\n      } else if (typeof modules === \"function\") {\n        hotState.selfAccepted = modules;\n      } else {\n        throw new Error(\"unsupported `accept` signature\");\n      }\n    },\n\n    decline: (dep) => {\n      if (dep === undefined) {\n        hotState.selfDeclined = true;\n      } else {\n        throw new Error(\"unsupported `decline` signature\");\n      }\n    },\n\n    dispose: (callback) => {\n      hotState.disposeHandlers.push(callback);\n    },\n\n    addDisposeHandler: (callback) => {\n      hotState.disposeHandlers.push(callback);\n    },\n\n    removeDisposeHandler: (callback) => {\n      const idx = hotState.disposeHandlers.indexOf(callback);\n      if (idx >= 0) {\n        hotState.disposeHandlers.splice(idx, 1);\n      }\n    },\n\n    invalidate: () => {\n      hotState.selfInvalidated = true;\n      queuedInvalidatedModules.add(moduleId);\n    },\n\n    // NOTE(alexkirsz) This is part of the management API, which we don't\n    // implement, but the Next.js React Refresh runtime uses this to decide\n    // whether to schedule an update.\n    status: () => \"idle\",\n\n    // NOTE(alexkirsz) Since we always return \"idle\" for now, these are no-ops.\n    addStatusHandler: (_handler) => {},\n    removeStatusHandler: (_handler) => {},\n\n    // NOTE(jridgewell) Check returns the list of updated modules, but we don't\n    // want the webpack code paths to ever update (the turbopack paths handle\n    // this already).\n    check: () => Promise.resolve(null),\n  };\n\n  return { hot, hotState };\n}\n\n/**\n * Removes a module from a chunk.\n * Returns `true` if there are no remaining chunks including this module.\n */\nfunction removeModuleFromChunk(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath\n): boolean {\n  const moduleChunks = moduleChunksMap.get(moduleId)!;\n  moduleChunks.delete(chunkPath);\n\n  const chunkModules = chunkModulesMap.get(chunkPath)!;\n  chunkModules.delete(moduleId);\n\n  const noRemainingModules = chunkModules.size === 0;\n  if (noRemainingModules) {\n    chunkModulesMap.delete(chunkPath);\n  }\n\n  const noRemainingChunks = moduleChunks.size === 0;\n  if (noRemainingChunks) {\n    moduleChunksMap.delete(moduleId);\n  }\n\n  return noRemainingChunks;\n}\n\n/**\n * Disposes of a chunk list and its corresponding exclusive chunks.\n */\nfunction disposeChunkList(chunkListPath: ChunkListPath): boolean {\n  const chunkPaths = chunkListChunksMap.get(chunkListPath);\n  if (chunkPaths == null) {\n    return false;\n  }\n  chunkListChunksMap.delete(chunkListPath);\n\n  for (const chunkPath of chunkPaths) {\n    const chunkChunkLists = chunkChunkListsMap.get(chunkPath)!;\n    chunkChunkLists.delete(chunkListPath);\n\n    if (chunkChunkLists.size === 0) {\n      chunkChunkListsMap.delete(chunkPath);\n      disposeChunk(chunkPath);\n    }\n  }\n\n  // We must also dispose of the chunk list's chunk itself to ensure it may\n  // be reloaded properly in the future.\n  const chunkListUrl = getChunkRelativeUrl(chunkListPath)\n\n  DEV_BACKEND.unloadChunk?.(chunkListUrl);\n\n  return true;\n}\n\n/**\n * Disposes of a chunk and its corresponding exclusive modules.\n *\n * @returns Whether the chunk was disposed of.\n */\nfunction disposeChunk(chunkPath: ChunkPath): boolean {\n  const chunkUrl = getChunkRelativeUrl(chunkPath)\n  // This should happen whether the chunk has any modules in it or not.\n  // For instance, CSS chunks have no modules in them, but they still need to be unloaded.\n  DEV_BACKEND.unloadChunk?.(chunkUrl);\n\n  const chunkModules = chunkModulesMap.get(chunkPath);\n  if (chunkModules == null) {\n    return false;\n  }\n  chunkModules.delete(chunkPath);\n\n  for (const moduleId of chunkModules) {\n    const moduleChunks = moduleChunksMap.get(moduleId)!;\n    moduleChunks.delete(chunkPath);\n\n    const noRemainingChunks = moduleChunks.size === 0;\n    if (noRemainingChunks) {\n      moduleChunksMap.delete(moduleId);\n      disposeModule(moduleId, \"clear\");\n      availableModules.delete(moduleId);\n    }\n  }\n\n  return true;\n}\n\n\n/**\n * Subscribes to chunk list updates from the update server and applies them.\n */\nfunction registerChunkList(\n  chunkList: ChunkList\n) {\n  const chunkListScript = chunkList.script;\n  const chunkListPath = getPathFromScript(chunkListScript);\n  // The \"chunk\" is also registered to finish the loading in the backend\n  BACKEND.registerChunk(chunkListPath as string as ChunkPath);\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS!.push([\n    chunkListPath,\n    handleApply.bind(null, chunkListPath),\n  ]);\n\n  // Adding chunks to chunk lists and vice versa.\n  const chunkPaths = new Set(chunkList.chunks.map(getChunkPath));\n  chunkListChunksMap.set(chunkListPath, chunkPaths);\n  for (const chunkPath of chunkPaths) {\n    let chunkChunkLists = chunkChunkListsMap.get(chunkPath);\n    if (!chunkChunkLists) {\n      chunkChunkLists = new Set([chunkListPath]);\n      chunkChunkListsMap.set(chunkPath, chunkChunkLists);\n    } else {\n      chunkChunkLists.add(chunkListPath);\n    }\n  }\n\n  if (chunkList.source === \"entry\") {\n    markChunkListAsRuntime(chunkListPath);\n  }\n}\n\nglobalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS ??= [];\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,4CAA4C;AAC5C,4CAA4C;AAE5C;;;;;;CAMC,GAED,oDAAoD,GAEpD,MAAM,iBAAyC,OAAO,MAAM,CAAC;AAwC7D,MAAM,yBAAyB;IAC7B,OAAO,mBAAmB;IAE1B,gBAA0B;IAE1B,YAAY,OAAe,EAAE,eAAyB,CAAE;QACtD,KAAK,CAAC;QACN,IAAI,CAAC,eAAe,GAAG;IACzB;AACF;AAEA;;;CAGC,GACD,MAAM,gBAAwC,IAAI;AAClD;;CAEC,GACD,MAAM,iBAAwC,IAAI;AAClD;;CAEC,GACD,MAAM,2BAA0C,IAAI;AAEpD;;CAEC,GACD,aAAa;AACb,SAAS,8BACP,QAAkB,EAClB,SAAoB;IAEpB,MAAM,SAAS,cAAc,CAAC,SAAS;IACvC,IAAI,QAAQ;QACV,IAAI,OAAO,KAAK,EAAE;YAChB,MAAM,OAAO,KAAK;QACpB;QACA,OAAO;IACT;IAEA,aAAa;IACb,OAAO,kBAAkB,UAAU;QAAE,MAAM,WAAW,OAAO;QAAE;IAAU;AAC3E;AAEA;;CAEC,GACD,2CAA2C;AAC3C,MAAM,mCAAgF,CACpF,IACA;IAEA,IAAI,CAAC,aAAa,GAAG,CAAC,MAAM,EAAE;QAC5B,QAAQ,IAAI,CACV,CAAC,4BAA4B,EAAE,GAAG,aAAa,EAAE,aAAa,EAAE,CAAC,oCAAoC,CAAC;IAE1G;IAEA,MAAM,SAAS,cAAc,CAAC,GAAG;IAEjC,IAAI,aAAa,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG;QAC5C,aAAa,QAAQ,CAAC,IAAI,CAAC;IAC7B;IAEA,IAAI,QAAQ;QACV,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,GAAG;YAClD,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;QACrC;QAEA,OAAO;IACT;IAEA,OAAO,kBAAkB,IAAI;QAC3B,MAAM,WAAW,MAAM;QACvB,UAAU,aAAa,EAAE;IAC3B;AACF;AAEA,0CAA0C;AAC1C,SAAS,kBAAkB,EAAY,EAAE,MAAkB;IACzD,MAAM,gBAAgB,eAAe,CAAC,GAAG;IACzC,IAAI,OAAO,kBAAkB,YAAY;QACvC,sEAAsE;QACtE,0EAA0E;QAC1E,mDAAmD;QACnD,IAAI;QACJ,OAAQ,OAAO,IAAI;YACjB,KAAK,WAAW,OAAO;gBACrB,sBAAsB,CAAC,4BAA4B,EAAE,OAAO,SAAS,EAAE;gBACvE;YACF,KAAK,WAAW,MAAM;gBACpB,sBAAsB,CAAC,oCAAoC,EAAE,OAAO,QAAQ,EAAE;gBAC9E;YACF,KAAK,WAAW,MAAM;gBACpB,sBAAsB;gBACtB;YACF;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;QACA,MAAM,IAAI,MACR,CAAC,OAAO,EAAE,GAAG,kBAAkB,EAAE,oBAAoB,uFAAuF,CAAC;IAEjJ;IAEA,MAAM,UAAU,cAAc,GAAG,CAAC;IAClC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IAE9C,IAAI;IACJ,OAAQ,OAAO,IAAI;QACjB,KAAK,WAAW,OAAO;YACrB,eAAe,GAAG,CAAC;YACnB,UAAU,EAAE;YACZ;QACF,KAAK,WAAW,MAAM;YACpB,wEAAwE;YACxE,wEAAwE;YACxE,UAAU;gBAAC,OAAO,QAAQ;aAAC;YAC3B;QACF,KAAK,WAAW,MAAM;YACpB,UAAU,OAAO,OAAO,IAAI,EAAE;YAC9B;QACF;YACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;IACxE;IAEA,MAAM,SAAoB;QACxB,SAAS,CAAC;QACV,OAAO;QACP,QAAQ;QACR;QACA;QACA,UAAU,EAAE;QACZ,iBAAiB;QACjB;IACF;IAEA,cAAc,CAAC,GAAG,GAAG;IACrB,eAAe,GAAG,CAAC,QAAQ;IAE3B,4EAA4E;IAC5E,IAAI;QACF,MAAM,aAAyB;YAAE,MAAM,WAAW,MAAM;YAAE,UAAU;QAAG;QAEvE,wBAAwB,QAAQ,CAAC;YAC/B,MAAM,IAAI,gBAAgB,IAAI,CAAC,MAAM;YACrC,cAAc,IAAI,CAChB,OAAO,OAAO,EACd,eAAe;gBACb,GAAG,YAAY,IAAI,CAAC,MAAM;gBAC1B,GAAG,OAAO,OAAO;gBACjB,GAAG,gBAAgB,IAAI,CAAC,MAAM;gBAC9B,GAAG;gBACH,GAAG;gBACH,GAAG,UAAU,IAAI,CAAC,MAAM;gBACxB,GAAG,UAAU,IAAI,CAAC,MAAM,QAAQ,OAAO,OAAO;gBAC9C,GAAG,cAAc,IAAI,CAAC,MAAM,QAAQ,OAAO,OAAO;gBAClD,GAAG,YAAY,IAAI,CAAC,MAAM;gBAC1B,GAAG,gBAAgB,IAAI,CAAC,MAAM;gBAC9B,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG,UAAU,IAAI,CAAC,MAAM;gBACxB,GAAG,eAAe,IAAI,CAAC,MAAM;gBAC7B,GAAG,gBAAgB,IAAI,CAAC,MAAM;gBAC9B,GAAG,sBAAsB,IAAI,CAAC,MAAM;gBACpC,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG,4BAA4B;gBAC/B,GAAG;gBACH,GAAG;gBACH,GAAG,OAAO,OAAO,EAAE,KAAK,WAAW,OAAO,EAAE,CAAC,OAAO,CAAC,cAAc,MAAM,OAAO,EAAE;YACpF;QAEJ;IACF,EAAE,OAAO,OAAO;QACd,OAAO,KAAK,GAAG;QACf,MAAM;IACR;IAEA,OAAO,MAAM,GAAG;IAChB,IAAI,OAAO,eAAe,IAAI,OAAO,OAAO,KAAK,OAAO,eAAe,EAAE;QACvE,yDAAyD;QACzD,WAAW,OAAO,OAAO,EAAE,OAAO,eAAe;IACnD;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,wBACP,MAAc,EACd,aAA4C;IAE5C,MAAM,+BACJ,OAAO,WAAW,iCAAiC,KAAK,aACpD,WAAW,iCAAiC,CAAC,OAAO,EAAE,IACtD,KAAO;IAEb,IAAI;QACF,cAAc;YACZ,UAAU,WAAW,YAAY;YACjC,WAAW,WAAW,YAAY;YAClC,iBAAiB;QACnB;IACF,EAAE,OAAO,GAAG;QACV,MAAM;IACR,SAAU;QACR,iEAAiE;QACjE;IACF;AACF;AAEA;;CAEC,GACD,SAAS,+CACP,MAAiB,EACjB,OAAuB;IAEvB,MAAM,iBAAiB,OAAO,OAAO;IACrC,MAAM,cAAc,OAAO,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI;IAEnD,QAAQ,8BAA8B,CAAC,gBAAgB,OAAO,EAAE;IAEhE,yEAAyE;IACzE,4BAA4B;IAC5B,IAAI,QAAQ,sBAAsB,CAAC,iBAAiB;QAClD,sEAAsE;QACtE,cAAc;QACd,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;YAClB,KAAK,WAAW,GAAG;QACrB;QACA,uEAAuE;QACvE,kCAAkC;QAClC,OAAO,GAAG,CAAC,MAAM;QAEjB,mEAAmE;QACnE,yEAAyE;QACzE,qBAAqB;QACrB,IAAI,gBAAgB,MAAM;YACxB,mEAAmE;YACnE,6BAA6B;YAC7B,EAAE;YACF,+DAA+D;YAC/D,kEAAkE;YAClE,8DAA8D;YAC9D,gDAAgD;YAChD,IACE,QAAQ,oCAAoC,CAC1C,QAAQ,2BAA2B,CAAC,cACpC,QAAQ,2BAA2B,CAAC,kBAEtC;gBACA,OAAO,GAAG,CAAC,UAAU;YACvB,OAAO;gBACL,QAAQ,cAAc;YACxB;QACF;IACF,OAAO;QACL,yEAAyE;QACzE,uDAAuD;QACvD,oEAAoE;QACpE,oEAAoE;QACpE,MAAM,sBAAsB,gBAAgB;QAC5C,IAAI,qBAAqB;YACvB,OAAO,GAAG,CAAC,UAAU;QACvB;IACF;AACF;AAEA,SAAS,sBAAsB,eAA2B;IACxD,OAAO,CAAC,kBAAkB,EAAE,gBAAgB,IAAI,CAAC,SAAS;AAC5D;AAEA,SAAS,uBACP,KAAuD,EACvD,QAA8C;IAK9C,MAAM,qBAAqB,IAAI;IAE/B,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,MAAO;QACrC,IAAI,SAAS,MAAM;YACjB,mBAAmB,GAAG,CAAC,UAAU,MAAM;QACzC;IACF;IAEA,MAAM,kBAAkB,2BAA2B,SAAS,IAAI;IAEhE,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,SAAU;QACxC,mBAAmB,GAAG,CAAC,UAAU,MAAM;IACzC;IAEA,OAAO;QAAE;QAAiB;IAAmB;AAC/C;AAEA,SAAS,2BACP,WAA+B;IAE/B,MAAM,kBAAkB,IAAI;IAE5B,KAAK,MAAM,YAAY,YAAa;QAClC,MAAM,SAAS,yBAAyB;QAExC,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,MAAM,IAAI,iBACR,CAAC,wCAAwC,EAAE,sBACzC,OAAO,eAAe,EACtB,CAAC,CAAC,EACJ,OAAO,eAAe;YAE1B,KAAK;gBACH,MAAM,IAAI,iBACR,CAAC,2CAA2C,EAAE,sBAC5C,OAAO,eAAe,EACtB,CAAC,CAAC,EACJ,OAAO,eAAe;YAE1B,KAAK;gBACH,KAAK,MAAM,oBAAoB,OAAO,eAAe,CAAE;oBACrD,gBAAgB,GAAG,CAAC;gBACtB;gBACA;YACF,6DAA6D;YAC7D;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;IACF;IAEA,OAAO;AACT;AAEA,SAAS,mCACP,eAAmC;IAEnC,MAAM,8BAAuF,EAAE;IAC/F,KAAK,MAAM,YAAY,gBAAiB;QACtC,MAAM,SAAS,cAAc,CAAC,SAAS;QACvC,MAAM,WAAW,eAAe,GAAG,CAAC;QACpC,IAAI,UAAU,SAAS,YAAY,IAAI,CAAC,SAAS,eAAe,EAAE;YAChE,4BAA4B,IAAI,CAAC;gBAC/B;gBACA,cAAc,SAAS,YAAY;YACrC;QACF;IACF;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,kBACP,kBAAiD,EACjD,oBAAmD;IAEnD,KAAK,MAAM,CAAC,WAAW,eAAe,IAAI,mBAAoB;QAC5D,KAAK,MAAM,YAAY,eAAgB;YACrC,iBAAiB,UAAU;QAC7B;IACF;IAEA,MAAM,kBAAiC,IAAI;IAC3C,KAAK,MAAM,CAAC,WAAW,eAAe,IAAI,qBAAsB;QAC9D,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,sBAAsB,UAAU,YAAY;gBAC9C,gBAAgB,GAAG,CAAC;YACtB;QACF;IACF;IAEA,OAAO;QAAE;IAAgB;AAC3B;AAEA,SAAS,aACP,eAAmC,EACnC,eAAmC;IAEnC,KAAK,MAAM,YAAY,gBAAiB;QACtC,cAAc,UAAU;IAC1B;IAEA,KAAK,MAAM,YAAY,gBAAiB;QACtC,cAAc,UAAU;IAC1B;IAEA,6DAA6D;IAC7D,0EAA0E;IAC1E,MAAM,wBAAwB,IAAI;IAClC,KAAK,MAAM,YAAY,gBAAiB;QACtC,MAAM,YAAY,cAAc,CAAC,SAAS;QAC1C,sBAAsB,GAAG,CAAC,UAAU,WAAW;QAC/C,OAAO,cAAc,CAAC,SAAS;IACjC;IAEA,uEAAuE;IACvE,YAAY;IAEZ,OAAO;QAAE;IAAsB;AACjC;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,cAAc,QAAkB,EAAE,IAAyB;IAClE,MAAM,SAAS,cAAc,CAAC,SAAS;IACvC,IAAI,CAAC,QAAQ;QACX;IACF;IAEA,MAAM,WAAW,eAAe,GAAG,CAAC;IACpC,MAAM,OAAO,CAAC;IAEd,mEAAmE;IACnE,qBAAqB;IACrB,KAAK,MAAM,kBAAkB,SAAS,eAAe,CAAE;QACrD,eAAe;IACjB;IAEA,0EAA0E;IAC1E,2CAA2C;IAC3C,OAAO,GAAG,CAAC,MAAM,GAAG;IAEpB,eAAe,MAAM,CAAC;IAEtB,sEAAsE;IAEtE,8DAA8D;IAC9D,wEAAwE;IACxE,kBAAkB;IAClB,KAAK,MAAM,WAAW,OAAO,QAAQ,CAAE;QACrC,MAAM,QAAQ,cAAc,CAAC,QAAQ;QACrC,IAAI,CAAC,OAAO;YACV;QACF;QAEA,MAAM,MAAM,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;QAC3C,IAAI,OAAO,GAAG;YACZ,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;QAC5B;IACF;IAEA,OAAQ;QACN,KAAK;YACH,OAAO,cAAc,CAAC,OAAO,EAAE,CAAC;YAChC,cAAc,MAAM,CAAC,OAAO,EAAE;YAC9B;QACF,KAAK;YACH,cAAc,GAAG,CAAC,OAAO,EAAE,EAAE;YAC7B;QACF;YACE,UAAU,MAAM,CAAC,OAAS,CAAC,cAAc,EAAE,MAAM;IACrD;AACF;AAEA,SAAS,WACP,2BAGG,EACH,kBAAgD,EAChD,qBAAqD,EACrD,WAA+B;IAE/B,2BAA2B;IAC3B,KAAK,MAAM,CAAC,UAAU,QAAQ,IAAI,mBAAmB,OAAO,GAAI;QAC9D,eAAe,CAAC,SAAS,GAAG;IAC9B;IAEA,gDAAgD;IAEhD,wEAAwE;IAExE,qDAAqD;IACrD,KAAK,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,4BAA6B;QACpE,IAAI;YACF,kBAAkB,UAAU;gBAC1B,MAAM,WAAW,MAAM;gBACvB,SAAS,sBAAsB,GAAG,CAAC;YACrC;QACF,EAAE,OAAO,KAAK;YACZ,IAAI,OAAO,iBAAiB,YAAY;gBACtC,IAAI;oBACF,aAAa,KAAK;wBAAE;wBAAU,QAAQ,cAAc,CAAC,SAAS;oBAAC;gBACjE,EAAE,OAAO,MAAM;oBACb,YAAY;oBACZ,YAAY;gBACd;YACF,OAAO;gBACL,YAAY;YACd;QACF;IACF;AACF;AAEA,SAAS,YAAY,MAAqB;IACxC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,qBAAqB;YACrB;QACF;YACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,OAAO,IAAI,EAAE;IACvE;AACF;AAEA,SAAS,qBAAqB,MAAuB;IACnD,IAAI,OAAO,MAAM,IAAI,MAAM;QACzB,KAAK,MAAM,UAAU,OAAO,MAAM,CAAE;YAClC,OAAQ,OAAO,IAAI;gBACjB,KAAK;oBACH,4BAA4B;oBAC5B;gBACF;oBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,OAAO,IAAI,EAAE;YACvE;QACF;IACF;IAEA,IAAI,OAAO,MAAM,IAAI,MAAM;QACzB,KAAK,MAAM,CAAC,WAAW,YAAY,IAAI,OAAO,OAAO,CAAC,OAAO,MAAM,EAAsC;YACvG,MAAM,WAAW,oBAAoB;YAErC,OAAQ,YAAY,IAAI;gBACtB,KAAK;oBACH,QAAQ,SAAS,CAAC,UAAU;wBAAE,MAAM,WAAW,MAAM;oBAAC;oBACtD;gBACF,KAAK;oBACH,YAAY,WAAW,GAAG;oBAC1B;gBACF,KAAK;oBACH,YAAY,WAAW,GAAG;oBAC1B;gBACF,KAAK;oBACH,UACE,YAAY,WAAW,EACvB,CAAC,cACC,CAAC,6BAA6B,EAAE,KAAK,SAAS,CAAC,aAAa,CAAC,CAAC;oBAElE;gBACF;oBACE,UACE,aACA,CAAC,cAAgB,CAAC,2BAA2B,EAAE,YAAY,IAAI,EAAE;YAEvE;QACF;IACF;AACF;AAEA,SAAS,4BAA4B,MAA8B;IACjE,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,GAAG;IACtC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,sBACtD,SACA;IAEF,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,uBAC9C,OACA;IAEF,MAAM,EAAE,eAAe,EAAE,GAAG,kBAAkB,aAAa;IAE3D,cAAc,iBAAiB,iBAAiB;AAClD;AAEA,SAAS,wBAAwB,eAA8B;IAC7D,IAAI,yBAAyB,IAAI,GAAG,GAAG;QACrC,2BAA2B,0BAA0B,OAAO,CAAC,CAAC;YAC5D,gBAAgB,GAAG,CAAC;QACtB;QAEA,yBAAyB,KAAK;IAChC;IAEA,OAAO;AACT;AAEA,SAAS,cACP,eAA8B,EAC9B,eAAmC,EACnC,kBAAgD;IAEhD,kBAAkB,wBAAwB;IAE1C,MAAM,8BACJ,mCAAmC;IAErC,MAAM,EAAE,qBAAqB,EAAE,GAAG,aAChC,iBACA;IAGF,4FAA4F;IAC5F,IAAI;IAEJ,SAAS,YAAY,GAAQ;QAC3B,IAAI,CAAC,OAAO,QAAQ;IACtB;IAEA,WACE,6BACA,oBACA,uBACA;IAGF,IAAI,OAAO;QACT,MAAM;IACR;IAEA,IAAI,yBAAyB,IAAI,GAAG,GAAG;QACrC,cAAc,IAAI,OAAO,EAAE,EAAE,IAAI;IACnC;AACF;AAEA,SAAS,sBACP,OAAgD,EAChD,OAAuD;IAQvD,MAAM,cAAc,IAAI;IACxB,MAAM,gBAAgB,IAAI;IAC1B,MAAM,QAA8C,IAAI;IACxD,MAAM,WAAW,IAAI;IACrB,MAAM,UAAyB,IAAI;IAEnC,KAAK,MAAM,CAAC,WAAW,kBAAkB,IAAI,OAAO,OAAO,CAAC,SAA6D;QACvH,OAAQ,kBAAkB,IAAI;YAC5B,KAAK;gBAAS;oBACZ,MAAM,cAAc,IAAI,IAAI,kBAAkB,OAAO;oBACrD,KAAK,MAAM,YAAY,YAAa;wBAClC,MAAM,GAAG,CAAC,UAAU,OAAO,CAAC,SAAS;oBACvC;oBACA,YAAY,GAAG,CAAC,WAAW;oBAC3B;gBACF;YACA,KAAK;gBAAW;oBACd,sDAAsD;oBACtD,MAAM,gBAAgB,IAAI,IAAI,gBAAgB,GAAG,CAAC;oBAClD,KAAK,MAAM,YAAY,cAAe;wBACpC,QAAQ,GAAG,CAAC;oBACd;oBACA,cAAc,GAAG,CAAC,WAAW;oBAC7B;gBACF;YACA,KAAK;gBAAW;oBACd,MAAM,cAAc,IAAI,IAAI,kBAAkB,KAAK;oBACnD,MAAM,gBAAgB,IAAI,IAAI,kBAAkB,OAAO;oBACvD,KAAK,MAAM,YAAY,YAAa;wBAClC,MAAM,GAAG,CAAC,UAAU,OAAO,CAAC,SAAS;oBACvC;oBACA,KAAK,MAAM,YAAY,cAAe;wBACpC,QAAQ,GAAG,CAAC;oBACd;oBACA,YAAY,GAAG,CAAC,WAAW;oBAC3B,cAAc,GAAG,CAAC,WAAW;oBAC7B;gBACF;YACA;gBACE,UACE,mBACA,CAAC,oBACC,CAAC,kCAAkC,EAAE,kBAAkB,IAAI,EAAE;QAErE;IACF;IAEA,oFAAoF;IACpF,yFAAyF;IACzF,uCAAuC;IACvC,KAAK,MAAM,YAAY,MAAM,IAAI,GAAI;QACnC,IAAI,QAAQ,GAAG,CAAC,WAAW;YACzB,MAAM,MAAM,CAAC;YACb,QAAQ,MAAM,CAAC;QACjB;IACF;IAEA,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;QACvD,gFAAgF;QAChF,kBAAkB;QAClB,gFAAgF;QAChF,kDAAkD;QAClD,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW;YACxB,SAAS,GAAG,CAAC,UAAU;QACzB;IACF;IAEA,OAAO;QAAE;QAAO;QAAS;QAAU;QAAa;IAAc;AAChE;AAkBA,SAAS,yBAAyB,QAAkB;IAClD,MAAM,kBAAiC,IAAI;IAI3C,MAAM,QAAqB;QACzB;YACE;YACA,iBAAiB,EAAE;QACrB;KACD;IAED,IAAI;IACJ,MAAQ,WAAW,MAAM,KAAK,GAAK;QACjC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QAEtC,IAAI,YAAY,MAAM;YACpB,IAAI,gBAAgB,GAAG,CAAC,WAAW;gBAEjC;YACF;YAEA,gBAAgB,GAAG,CAAC;QACtB;QAEA,sEAAsE;QACtE,qCAAqC;QACrC,IAAI,aAAa,WAAW;YAC1B,OAAO;gBACL,MAAM;gBACN;YACF;QACF;QAEA,MAAM,SAAS,cAAc,CAAC,SAAS;QACvC,MAAM,WAAW,eAAe,GAAG,CAAC;QAEpC,IACE,qEAAqE;QACrE,0DAA0D;QAC1D,CAAC,UAEA,SAAS,YAAY,IAAI,CAAC,SAAS,eAAe,EACnD;YACA;QACF;QAEA,IAAI,SAAS,YAAY,EAAE;YACzB,OAAO;gBACL,MAAM;gBACN;gBACA;YACF;QACF;QAEA,IAAI,eAAe,GAAG,CAAC,WAAW;YAChC,MAAM,IAAI,CAAC;gBACT,UAAU;gBACV,iBAAiB;uBAAI;oBAAiB;iBAAS;YACjD;YACA;QACF;QAEA,KAAK,MAAM,YAAY,OAAO,OAAO,CAAE;YACrC,MAAM,SAAS,cAAc,CAAC,SAAS;YAEvC,IAAI,CAAC,QAAQ;gBAEX;YACF;YAEA,4DAA4D;YAC5D,qBAAqB;YAErB,MAAM,IAAI,CAAC;gBACT,UAAU;gBACV,iBAAiB;uBAAI;oBAAiB;iBAAS;YACjD;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,YAAY,aAA4B,EAAE,MAAqB;IACtE,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAW;gBACd,4FAA4F;gBAC5F,YAAY,OAAO,WAAW;gBAC9B;YACF;QACA,KAAK;YAAW;gBACd,iEAAiE;gBACjE,qEAAqE;gBACrE,aAAa;gBACb,YAAY,OAAO;gBACnB;YACF;QACA,KAAK;YAAY;gBACf,+GAA+G;gBAC/G,kCAAkC;gBAClC,mGAAmG;gBACnG,6DAA6D;gBAC7D,IAAI,kBAAkB,GAAG,CAAC,gBAAgB;oBACxC,YAAY,OAAO;gBACrB,OAAO;oBACL,iBAAiB;gBACnB;gBACA;YACF;QACA;YACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,OAAO,IAAI,EAAE;IACzD;AACF;AAEA,SAAS,gBACP,QAAkB,EAClB,OAAgB;IAEhB,MAAM,WAAqB;QACzB,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,iBAAiB,EAAE;IACrB;IAEA,MAAM,MAAW;QACf,qEAAqE;QACrE,wEAAwE;QACxE,uCAAuC;QACvC,QAAQ;QAER,MAAM,WAAW,CAAC;QAElB,mEAAmE;QACnE,QAAQ,CACN,SACA,WACA;YAEA,IAAI,YAAY,WAAW;gBACzB,SAAS,YAAY,GAAG;YAC1B,OAAO,IAAI,OAAO,YAAY,YAAY;gBACxC,SAAS,YAAY,GAAG;YAC1B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,SAAS,CAAC;YACR,IAAI,QAAQ,WAAW;gBACrB,SAAS,YAAY,GAAG;YAC1B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,SAAS,CAAC;YACR,SAAS,eAAe,CAAC,IAAI,CAAC;QAChC;QAEA,mBAAmB,CAAC;YAClB,SAAS,eAAe,CAAC,IAAI,CAAC;QAChC;QAEA,sBAAsB,CAAC;YACrB,MAAM,MAAM,SAAS,eAAe,CAAC,OAAO,CAAC;YAC7C,IAAI,OAAO,GAAG;gBACZ,SAAS,eAAe,CAAC,MAAM,CAAC,KAAK;YACvC;QACF;QAEA,YAAY;YACV,SAAS,eAAe,GAAG;YAC3B,yBAAyB,GAAG,CAAC;QAC/B;QAEA,qEAAqE;QACrE,uEAAuE;QACvE,iCAAiC;QACjC,QAAQ,IAAM;QAEd,2EAA2E;QAC3E,kBAAkB,CAAC,YAAc;QACjC,qBAAqB,CAAC,YAAc;QAEpC,2EAA2E;QAC3E,yEAAyE;QACzE,iBAAiB;QACjB,OAAO,IAAM,QAAQ,OAAO,CAAC;IAC/B;IAEA,OAAO;QAAE;QAAK;IAAS;AACzB;AAEA;;;CAGC,GACD,SAAS,sBACP,QAAkB,EAClB,SAAoB;IAEpB,MAAM,eAAe,gBAAgB,GAAG,CAAC;IACzC,aAAa,MAAM,CAAC;IAEpB,MAAM,eAAe,gBAAgB,GAAG,CAAC;IACzC,aAAa,MAAM,CAAC;IAEpB,MAAM,qBAAqB,aAAa,IAAI,KAAK;IACjD,IAAI,oBAAoB;QACtB,gBAAgB,MAAM,CAAC;IACzB;IAEA,MAAM,oBAAoB,aAAa,IAAI,KAAK;IAChD,IAAI,mBAAmB;QACrB,gBAAgB,MAAM,CAAC;IACzB;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,iBAAiB,aAA4B;IACpD,MAAM,aAAa,mBAAmB,GAAG,CAAC;IAC1C,IAAI,cAAc,MAAM;QACtB,OAAO;IACT;IACA,mBAAmB,MAAM,CAAC;IAE1B,KAAK,MAAM,aAAa,WAAY;QAClC,MAAM,kBAAkB,mBAAmB,GAAG,CAAC;QAC/C,gBAAgB,MAAM,CAAC;QAEvB,IAAI,gBAAgB,IAAI,KAAK,GAAG;YAC9B,mBAAmB,MAAM,CAAC;YAC1B,aAAa;QACf;IACF;IAEA,yEAAyE;IACzE,sCAAsC;IACtC,MAAM,eAAe,oBAAoB;IAEzC,YAAY,WAAW,GAAG;IAE1B,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,aAAa,SAAoB;IACxC,MAAM,WAAW,oBAAoB;IACrC,qEAAqE;IACrE,wFAAwF;IACxF,YAAY,WAAW,GAAG;IAE1B,MAAM,eAAe,gBAAgB,GAAG,CAAC;IACzC,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IACA,aAAa,MAAM,CAAC;IAEpB,KAAK,MAAM,YAAY,aAAc;QACnC,MAAM,eAAe,gBAAgB,GAAG,CAAC;QACzC,aAAa,MAAM,CAAC;QAEpB,MAAM,oBAAoB,aAAa,IAAI,KAAK;QAChD,IAAI,mBAAmB;YACrB,gBAAgB,MAAM,CAAC;YACvB,cAAc,UAAU;YACxB,iBAAiB,MAAM,CAAC;QAC1B;IACF;IAEA,OAAO;AACT;AAGA;;CAEC,GACD,SAAS,kBACP,SAAoB;IAEpB,MAAM,kBAAkB,UAAU,MAAM;IACxC,MAAM,gBAAgB,kBAAkB;IACxC,sEAAsE;IACtE,QAAQ,aAAa,CAAC;IACtB,WAAW,gCAAgC,CAAE,IAAI,CAAC;QAChD;QACA,YAAY,IAAI,CAAC,MAAM;KACxB;IAED,+CAA+C;IAC/C,MAAM,aAAa,IAAI,IAAI,UAAU,MAAM,CAAC,GAAG,CAAC;IAChD,mBAAmB,GAAG,CAAC,eAAe;IACtC,KAAK,MAAM,aAAa,WAAY;QAClC,IAAI,kBAAkB,mBAAmB,GAAG,CAAC;QAC7C,IAAI,CAAC,iBAAiB;YACpB,kBAAkB,IAAI,IAAI;gBAAC;aAAc;YACzC,mBAAmB,GAAG,CAAC,WAAW;QACpC,OAAO;YACL,gBAAgB,GAAG,CAAC;QACtB;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,SAAS;QAChC,uBAAuB;IACzB;AACF;AAEA,WAAW,gCAAgC,KAAK,EAAE", "ignoreList": [0]}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/dom/runtime-backend-dom.ts"], "sourcesContent": ["/**\n * This file contains the runtime code specific to the Turbopack development\n * ECMAScript DOM runtime.\n *\n * It will be appended to the base development runtime code.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../../../browser/runtime/base/runtime-base.ts\" />\n/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n\ntype ChunkResolver = {\n  resolved: boolean;\n  resolve: () => void;\n  reject: (error?: Error) => void;\n  promise: Promise<void>;\n};\n\nlet BACKEND: RuntimeBackend;\n\nfunction augmentContext(context: unknown): unknown {\n  return context;\n}\n\nfunction fetchWebAssembly(wasmChunkPath: ChunkPath) {\n  return fetch(getChunkRelativeUrl(wasmChunkPath));\n}\n\nasync function loadWebAssembly(\n  _source: unknown,\n  wasmChunkPath: ChunkPath,\n  importsObj: WebAssembly.Imports\n): Promise<Exports> {\n  const req = fetchWebAssembly(wasmChunkPath);\n\n  const { instance } = await WebAssembly.instantiateStreaming(req, importsObj);\n\n  return instance.exports;\n}\n\nasync function loadWebAssemblyModule(\n  _source: unknown,\n  wasmChunkPath: ChunkPath\n): Promise<WebAssembly.Module> {\n  const req = fetchWebAssembly(wasmChunkPath);\n\n  return await WebAssembly.compileStreaming(req);\n}\n\n/**\n * Maps chunk paths to the corresponding resolver.\n */\nconst chunkResolvers: Map<ChunkUrl, ChunkResolver> = new Map();\n\n(() => {\n  BACKEND = {\n    async registerChunk(chunkPath, params) {\n      const chunkUrl = getChunkRelativeUrl(chunkPath);\n\n      const resolver = getOrCreateResolver(chunkUrl);\n      resolver.resolve();\n\n      if (params == null) {\n        return;\n      }\n\n      for (const otherChunkData of params.otherChunks) {\n        const otherChunkPath = getChunkPath(otherChunkData)\n        const otherChunkUrl = getChunkRelativeUrl(otherChunkPath);\n\n        // Chunk might have started loading, so we want to avoid triggering another load.\n        getOrCreateResolver(otherChunkUrl);\n      }\n\n      // This waits for chunks to be loaded, but also marks included items as available.\n      await Promise.all(\n        params.otherChunks.map((otherChunkData) =>\n          loadChunk({ type: SourceType.Runtime, chunkPath }, otherChunkData)\n        )\n      );\n\n      if (params.runtimeModuleIds.length > 0) {\n        for (const moduleId of params.runtimeModuleIds) {\n          getOrInstantiateRuntimeModule(moduleId, chunkPath);\n        }\n      }\n    },\n\n    /**\n     * Loads the given chunk, and returns a promise that resolves once the chunk\n     * has been loaded.\n    */\n    loadChunk(chunkUrl, source) {\n      return doLoadChunk(chunkUrl, source);\n    },\n  };\n\n  function getOrCreateResolver(chunkUrl: ChunkUrl): ChunkResolver {\n    let resolver = chunkResolvers.get(chunkUrl);\n    if (!resolver) {\n      let resolve: () => void;\n      let reject: (error?: Error) => void;\n      const promise = new Promise<void>((innerResolve, innerReject) => {\n        resolve = innerResolve;\n        reject = innerReject;\n      });\n      resolver = {\n        resolved: false,\n        promise,\n        resolve: () => {\n          resolver!.resolved = true;\n          resolve();\n        },\n        reject: reject!,\n      };\n      chunkResolvers.set(chunkUrl, resolver);\n    }\n    return resolver;\n  }\n\n   /**\n    * Loads the given chunk, and returns a promise that resolves once the chunk\n    * has been loaded.\n    */\n  function doLoadChunk(chunkUrl: ChunkUrl, source: SourceInfo) {\n    const resolver = getOrCreateResolver(chunkUrl);\n    if (resolver.resolved) {\n      return resolver.promise;\n    }\n\n    if (source.type === SourceType.Runtime) {\n      // We don't need to load chunks references from runtime code, as they're already\n      // present in the DOM.\n\n      if (isCss(chunkUrl)) {\n        // CSS chunks do not register themselves, and as such must be marked as\n        // loaded instantly.\n        resolver.resolve();\n      }\n\n      // We need to wait for JS chunks to register themselves within `registerChunk`\n      // before we can start instantiating runtime modules, hence the absence of\n      // `resolver.resolve()` in this branch.\n\n      return resolver.promise;\n    }\n\n    if (typeof importScripts === \"function\") {\n      // We're in a web worker\n      if (isCss(chunkUrl)) {\n        // ignore\n      } else if (isJs(chunkUrl)) {\n        self.TURBOPACK_NEXT_CHUNK_URLS!.push(chunkUrl);\n        importScripts(TURBOPACK_WORKER_LOCATION + chunkUrl);\n      } else {\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl} in worker`);\n      }\n    } else {\n      // TODO(PACK-2140): remove this once all filenames are guaranteed to be escaped.\n      const decodedChunkUrl = decodeURI(chunkUrl);\n\n      if (isCss(chunkUrl)) {\n        const previousLinks = document.querySelectorAll(\n          `link[rel=stylesheet][href=\"${chunkUrl}\"],link[rel=stylesheet][href^=\"${chunkUrl}?\"],link[rel=stylesheet][href=\"${decodedChunkUrl}\"],link[rel=stylesheet][href^=\"${decodedChunkUrl}?\"]`\n        );\n        if (previousLinks.length > 0) {\n          // CSS chunks do not register themselves, and as such must be marked as\n          // loaded instantly.\n          resolver.resolve();\n        } else {\n          const link = document.createElement(\"link\");\n          link.rel = \"stylesheet\";\n          link.href = chunkUrl;\n          link.onerror = () => {\n            resolver.reject();\n          };\n          link.onload = () => {\n            // CSS chunks do not register themselves, and as such must be marked as\n            // loaded instantly.\n            resolver.resolve();\n          };\n          document.body.appendChild(link);\n        }\n      } else if (isJs(chunkUrl)) {\n        const previousScripts = document.querySelectorAll(\n          `script[src=\"${chunkUrl}\"],script[src^=\"${chunkUrl}?\"],script[src=\"${decodedChunkUrl}\"],script[src^=\"${decodedChunkUrl}?\"]`\n        );\n        if (previousScripts.length > 0) {\n          // There is this edge where the script already failed loading, but we\n          // can't detect that. The Promise will never resolve in this case.\n          for (const script of Array.from(previousScripts)) {\n            script.addEventListener(\"error\", () => {\n              resolver.reject();\n            });\n          }\n        } else {\n          const script = document.createElement(\"script\");\n          script.src = chunkUrl;\n          // We'll only mark the chunk as loaded once the script has been executed,\n          // which happens in `registerChunk`. Hence the absence of `resolve()` in\n          // this branch.\n          script.onerror = () => {\n            resolver.reject();\n          };\n          document.body.appendChild(script);\n        }\n      } else {\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl}`);\n      }\n    }\n\n    return resolver.promise;\n  }\n\n})();\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,sEAAsE;AACtE,2DAA2D;AAS3D,IAAI;AAEJ,SAAS,eAAe,OAAgB;IACtC,OAAO;AACT;AAEA,SAAS,iBAAiB,aAAwB;IAChD,OAAO,MAAM,oBAAoB;AACnC;AAEA,eAAe,gBACb,OAAgB,EAChB,aAAwB,EACxB,UAA+B;IAE/B,MAAM,MAAM,iBAAiB;IAE7B,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,YAAY,oBAAoB,CAAC,KAAK;IAEjE,OAAO,SAAS,OAAO;AACzB;AAEA,eAAe,sBACb,OAAgB,EAChB,aAAwB;IAExB,MAAM,MAAM,iBAAiB;IAE7B,OAAO,MAAM,YAAY,gBAAgB,CAAC;AAC5C;AAEA;;CAEC,GACD,MAAM,iBAA+C,IAAI;AAEzD,CAAC;IACC,UAAU;QACR,MAAM,eAAc,SAAS,EAAE,MAAM;YACnC,MAAM,WAAW,oBAAoB;YAErC,MAAM,WAAW,oBAAoB;YACrC,SAAS,OAAO;YAEhB,IAAI,UAAU,MAAM;gBAClB;YACF;YAEA,KAAK,MAAM,kBAAkB,OAAO,WAAW,CAAE;gBAC/C,MAAM,iBAAiB,aAAa;gBACpC,MAAM,gBAAgB,oBAAoB;gBAE1C,iFAAiF;gBACjF,oBAAoB;YACtB;YAEA,kFAAkF;YAClF,MAAM,QAAQ,GAAG,CACf,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,iBACtB,UAAU;oBAAE,MAAM,WAAW,OAAO;oBAAE;gBAAU,GAAG;YAIvD,IAAI,OAAO,gBAAgB,CAAC,MAAM,GAAG,GAAG;gBACtC,KAAK,MAAM,YAAY,OAAO,gBAAgB,CAAE;oBAC9C,8BAA8B,UAAU;gBAC1C;YACF;QACF;QAEA;;;IAGA,GACA,WAAU,QAAQ,EAAE,MAAM;YACxB,OAAO,YAAY,UAAU;QAC/B;IACF;IAEA,SAAS,oBAAoB,QAAkB;QAC7C,IAAI,WAAW,eAAe,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU;YACb,IAAI;YACJ,IAAI;YACJ,MAAM,UAAU,IAAI,QAAc,CAAC,cAAc;gBAC/C,UAAU;gBACV,SAAS;YACX;YACA,WAAW;gBACT,UAAU;gBACV;gBACA,SAAS;oBACP,SAAU,QAAQ,GAAG;oBACrB;gBACF;gBACA,QAAQ;YACV;YACA,eAAe,GAAG,CAAC,UAAU;QAC/B;QACA,OAAO;IACT;IAEC;;;IAGC,GACF,SAAS,YAAY,QAAkB,EAAE,MAAkB;QACzD,MAAM,WAAW,oBAAoB;QACrC,IAAI,SAAS,QAAQ,EAAE;YACrB,OAAO,SAAS,OAAO;QACzB;QAEA,IAAI,OAAO,IAAI,KAAK,WAAW,OAAO,EAAE;YACtC,gFAAgF;YAChF,sBAAsB;YAEtB,IAAI,MAAM,WAAW;gBACnB,uEAAuE;gBACvE,oBAAoB;gBACpB,SAAS,OAAO;YAClB;YAEA,8EAA8E;YAC9E,0EAA0E;YAC1E,uCAAuC;YAEvC,OAAO,SAAS,OAAO;QACzB;QAEA,IAAI,OAAO,kBAAkB,YAAY;YACvC,wBAAwB;YACxB,IAAI,MAAM,WAAW;YACnB,SAAS;YACX,OAAO,IAAI,KAAK,WAAW;gBACzB,KAAK,yBAAyB,CAAE,IAAI,CAAC;gBACrC,cAAc,4BAA4B;YAC5C,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,SAAS,UAAU,CAAC;YAC5E;QACF,OAAO;YACL,gFAAgF;YAChF,MAAM,kBAAkB,UAAU;YAElC,IAAI,MAAM,WAAW;gBACnB,MAAM,gBAAgB,SAAS,gBAAgB,CAC7C,CAAC,2BAA2B,EAAE,SAAS,+BAA+B,EAAE,SAAS,+BAA+B,EAAE,gBAAgB,+BAA+B,EAAE,gBAAgB,GAAG,CAAC;gBAEzL,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,uEAAuE;oBACvE,oBAAoB;oBACpB,SAAS,OAAO;gBAClB,OAAO;oBACL,MAAM,OAAO,SAAS,aAAa,CAAC;oBACpC,KAAK,GAAG,GAAG;oBACX,KAAK,IAAI,GAAG;oBACZ,KAAK,OAAO,GAAG;wBACb,SAAS,MAAM;oBACjB;oBACA,KAAK,MAAM,GAAG;wBACZ,uEAAuE;wBACvE,oBAAoB;wBACpB,SAAS,OAAO;oBAClB;oBACA,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;YACF,OAAO,IAAI,KAAK,WAAW;gBACzB,MAAM,kBAAkB,SAAS,gBAAgB,CAC/C,CAAC,YAAY,EAAE,SAAS,gBAAgB,EAAE,SAAS,gBAAgB,EAAE,gBAAgB,gBAAgB,EAAE,gBAAgB,GAAG,CAAC;gBAE7H,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,qEAAqE;oBACrE,kEAAkE;oBAClE,KAAK,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAkB;wBAChD,OAAO,gBAAgB,CAAC,SAAS;4BAC/B,SAAS,MAAM;wBACjB;oBACF;gBACF,OAAO;oBACL,MAAM,SAAS,SAAS,aAAa,CAAC;oBACtC,OAAO,GAAG,GAAG;oBACb,yEAAyE;oBACzE,wEAAwE;oBACxE,eAAe;oBACf,OAAO,OAAO,GAAG;wBACf,SAAS,MAAM;oBACjB;oBACA,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,UAAU;YAClE;QACF;QAEA,OAAO,SAAS,OAAO;IACzB;AAEF,CAAC", "ignoreList": [0]}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/dom/dev-backend-dom.ts"], "sourcesContent": ["/**\n * This file contains the runtime code specific to the Turbopack development\n * ECMAScript DOM runtime.\n *\n * It will be appended to the base development runtime code.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../base/runtime-base.ts\" />\n/// <reference path=\"../base/dev-base.ts\" />\n/// <reference path=\"./runtime-backend-dom.ts\" />\n/// <reference path=\"../../../shared/require-type.d.ts\" />\n\nlet DEV_BACKEND: DevRuntimeBackend;\n\n(() => {\n  DEV_BACKEND = {\n    unloadChunk(chunkUrl) {\n      deleteResolver(chunkUrl);\n\n      // TODO(PACK-2140): remove this once all filenames are guaranteed to be escaped.\n      const decodedChunkUrl = decodeURI(chunkUrl);\n\n      if (isCss(chunkUrl)) {\n        const links = document.querySelectorAll(\n          `link[href=\"${chunkUrl}\"],link[href^=\"${chunkUrl}?\"],link[href=\"${decodedChunkUrl}\"],link[href^=\"${decodedChunkUrl}?\"]`\n        );\n        for (const link of Array.from(links)) {\n          link.remove();\n        }\n      } else if (isJs(chunkUrl)) {\n        // Unloading a JS chunk would have no effect, as it lives in the JS\n        // runtime once evaluated.\n        // However, we still want to remove the script tag from the DOM to keep\n        // the HTML somewhat consistent from the user's perspective.\n        const scripts = document.querySelectorAll(\n          `script[src=\"${chunkUrl}\"],script[src^=\"${chunkUrl}?\"],script[src=\"${decodedChunkUrl}\"],script[src^=\"${decodedChunkUrl}?\"]`\n        );\n        for (const script of Array.from(scripts)) {\n          script.remove();\n        }\n      } else {\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl}`);\n      }\n    },\n\n    reloadChunk(chunkUrl) {\n      return new Promise<void>((resolve, reject) => {\n        if (!isCss(chunkUrl)) {\n          reject(new Error(\"The DOM backend can only reload CSS chunks\"));\n          return;\n        }\n\n        const decodedChunkUrl = decodeURI(chunkUrl);\n        const previousLinks = document.querySelectorAll(\n          `link[rel=stylesheet][href=\"${chunkUrl}\"],link[rel=stylesheet][href^=\"${chunkUrl}?\"],link[rel=stylesheet][href=\"${decodedChunkUrl}\"],link[rel=stylesheet][href^=\"${decodedChunkUrl}?\"]`\n        );\n\n        if (previousLinks.length === 0) {\n          reject(new Error(`No link element found for chunk ${chunkUrl}`));\n          return;\n        }\n\n        const link = document.createElement(\"link\");\n        link.rel = \"stylesheet\";\n\n        if (navigator.userAgent.includes(\"Firefox\")) {\n          // Firefox won't reload CSS files that were previously loaded on the current page,\n          // we need to add a query param to make sure CSS is actually reloaded from the server.\n          //\n          // I believe this is this issue: https://bugzilla.mozilla.org/show_bug.cgi?id=1037506\n          //\n          // Safari has a similar issue, but only if you have a `<link rel=preload ... />` tag\n          // pointing to the same URL as the stylesheet: https://bugs.webkit.org/show_bug.cgi?id=187726\n          link.href = `${chunkUrl}?ts=${Date.now()}`;\n        } else {\n          link.href = chunkUrl;\n        }\n\n        link.onerror = () => {\n          reject();\n        };\n        link.onload = () => {\n          // First load the new CSS, then remove the old ones. This prevents visible\n          // flickering that would happen in-between removing the previous CSS and\n          // loading the new one.\n          for (const previousLink of Array.from(previousLinks))\n            previousLink.remove();\n\n          // CSS chunks do not register themselves, and as such must be marked as\n          // loaded instantly.\n          resolve();\n        };\n\n        // Make sure to insert the new CSS right after the previous one, so that\n        // its precedence is higher.\n        previousLinks[0].parentElement!.insertBefore(\n          link,\n          previousLinks[0].nextSibling\n        );\n      });\n    },\n\n    restart: () => self.location.reload(),\n  };\n\n  function deleteResolver(chunkUrl: ChunkUrl) {\n    chunkResolvers.delete(chunkUrl);\n  }\n})();\n\nfunction _eval({ code, url, map }: EcmascriptModuleEntry): ModuleFactory {\n  code += `\\n\\n//# sourceURL=${encodeURI(\n    location.origin + CHUNK_BASE_PATH + url + CHUNK_SUFFIX_PATH\n  )}`;\n  if (map) {\n    code += `\\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,${btoa(\n      // btoa doesn't handle nonlatin characters, so escape them as \\x sequences\n      // See https://stackoverflow.com/a/26603875\n      unescape(encodeURIComponent(map))\n    )}`;\n  }\n\n  // eslint-disable-next-line no-eval\n  return eval(code);\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,gDAAgD;AAChD,4CAA4C;AAC5C,iDAAiD;AACjD,0DAA0D;AAE1D,IAAI;AAEJ,CAAC;IACC,cAAc;QACZ,aAAY,QAAQ;YAClB,eAAe;YAEf,gFAAgF;YAChF,MAAM,kBAAkB,UAAU;YAElC,IAAI,MAAM,WAAW;gBACnB,MAAM,QAAQ,SAAS,gBAAgB,CACrC,CAAC,WAAW,EAAE,SAAS,eAAe,EAAE,SAAS,eAAe,EAAE,gBAAgB,eAAe,EAAE,gBAAgB,GAAG,CAAC;gBAEzH,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;oBACpC,KAAK,MAAM;gBACb;YACF,OAAO,IAAI,KAAK,WAAW;gBACzB,mEAAmE;gBACnE,0BAA0B;gBAC1B,uEAAuE;gBACvE,4DAA4D;gBAC5D,MAAM,UAAU,SAAS,gBAAgB,CACvC,CAAC,YAAY,EAAE,SAAS,gBAAgB,EAAE,SAAS,gBAAgB,EAAE,gBAAgB,gBAAgB,EAAE,gBAAgB,GAAG,CAAC;gBAE7H,KAAK,MAAM,UAAU,MAAM,IAAI,CAAC,SAAU;oBACxC,OAAO,MAAM;gBACf;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,UAAU;YAClE;QACF;QAEA,aAAY,QAAQ;YAClB,OAAO,IAAI,QAAc,CAAC,SAAS;gBACjC,IAAI,CAAC,MAAM,WAAW;oBACpB,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,MAAM,kBAAkB,UAAU;gBAClC,MAAM,gBAAgB,SAAS,gBAAgB,CAC7C,CAAC,2BAA2B,EAAE,SAAS,+BAA+B,EAAE,SAAS,+BAA+B,EAAE,gBAAgB,+BAA+B,EAAE,gBAAgB,GAAG,CAAC;gBAGzL,IAAI,cAAc,MAAM,KAAK,GAAG;oBAC9B,OAAO,IAAI,MAAM,CAAC,gCAAgC,EAAE,UAAU;oBAC9D;gBACF;gBAEA,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,GAAG,GAAG;gBAEX,IAAI,UAAU,SAAS,CAAC,QAAQ,CAAC,YAAY;oBAC3C,kFAAkF;oBAClF,sFAAsF;oBACtF,EAAE;oBACF,qFAAqF;oBACrF,EAAE;oBACF,oFAAoF;oBACpF,6FAA6F;oBAC7F,KAAK,IAAI,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK,GAAG,IAAI;gBAC5C,OAAO;oBACL,KAAK,IAAI,GAAG;gBACd;gBAEA,KAAK,OAAO,GAAG;oBACb;gBACF;gBACA,KAAK,MAAM,GAAG;oBACZ,0EAA0E;oBAC1E,wEAAwE;oBACxE,uBAAuB;oBACvB,KAAK,MAAM,gBAAgB,MAAM,IAAI,CAAC,eACpC,aAAa,MAAM;oBAErB,uEAAuE;oBACvE,oBAAoB;oBACpB;gBACF;gBAEA,wEAAwE;gBACxE,4BAA4B;gBAC5B,aAAa,CAAC,EAAE,CAAC,aAAa,CAAE,YAAY,CAC1C,MACA,aAAa,CAAC,EAAE,CAAC,WAAW;YAEhC;QACF;QAEA,SAAS,IAAM,KAAK,QAAQ,CAAC,MAAM;IACrC;IAEA,SAAS,eAAe,QAAkB;QACxC,eAAe,MAAM,CAAC;IACxB;AACF,CAAC;AAED,SAAS,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAyB;IACtD,QAAQ,CAAC,kBAAkB,EAAE,UAC3B,SAAS,MAAM,GAAG,kBAAkB,MAAM,oBACzC;IACH,IAAI,KAAK;QACP,QAAQ,CAAC,kEAAkE,EAAE,KAC3E,0EAA0E;QAC1E,2CAA2C;QAC3C,SAAS,mBAAmB,QAC3B;IACL;IAEA,mCAAmC;IACnC,OAAO,KAAK;AACd", "ignoreList": [0]}}]}